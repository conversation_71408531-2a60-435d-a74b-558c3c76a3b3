{"version": 3, "file": "tippy-bundle.umd.js", "sources": ["../src/css.ts", "../src/browser.ts", "../src/constants.ts", "../src/utils.ts", "../src/dom-utils.ts", "../src/bindGlobalEventListeners.ts", "../src/validation.ts", "../src/props.ts", "../src/template.ts", "../src/createTippy.ts", "../src/index.ts", "../src/addons/createSingleton.ts", "../src/addons/delegate.ts", "../src/plugins/animateFill.ts", "../src/plugins/followCursor.ts", "../src/plugins/inlinePositioning.ts", "../src/plugins/sticky.ts", "../build/bundle-umd.js"], "sourcesContent": ["export function injectCSS(css: string): void {\n  const style = document.createElement('style');\n  style.textContent = css;\n  style.setAttribute('data-__NAMESPACE_PREFIX__-stylesheet', '');\n  const head = document.head;\n  const firstStyleOrLinkTag = document.querySelector('head>style,head>link');\n\n  if (firstStyleOrLinkTag) {\n    head.insertBefore(style, firstStyleOrLinkTag);\n  } else {\n    head.appendChild(style);\n  }\n}\n", "export const isBrowser =\n  typeof window !== 'undefined' && typeof document !== 'undefined';\n\nconst ua = isBrowser ? navigator.userAgent : '';\n\nexport const isIE = /MSIE |Trident\\//.test(ua);\n", "export const ROUND_ARROW =\n  '<svg width=\"16\" height=\"6\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z\"></svg>';\n\nexport const BOX_CLASS = `__NAMESPACE_PREFIX__-box`;\nexport const CONTENT_CLASS = `__NAMESPACE_PREFIX__-content`;\nexport const BACKDROP_CLASS = `__NAMESPACE_PREFIX__-backdrop`;\nexport const ARROW_CLASS = `__NAMESPACE_PREFIX__-arrow`;\nexport const SVG_ARROW_CLASS = `__NAMESPACE_PREFIX__-svg-arrow`;\n\nexport const TOUCH_OPTIONS = {passive: true, capture: true};\n", "import {BasePlacement, Placement} from './types';\n\nexport function hasOwnProperty(obj: object, key: string): boolean {\n  return {}.hasOwnProperty.call(obj, key);\n}\n\nexport function getValueAtIndexOrReturn<T>(\n  value: T | [T | null, T | null],\n  index: number,\n  defaultValue: T | [T, T]\n): T {\n  if (Array.isArray(value)) {\n    const v = value[index];\n    return v == null\n      ? Array.isArray(defaultValue)\n        ? defaultValue[index]\n        : defaultValue\n      : v;\n  }\n\n  return value;\n}\n\nexport function isType(value: any, type: string): boolean {\n  const str = {}.toString.call(value);\n  return str.indexOf('[object') === 0 && str.indexOf(`${type}]`) > -1;\n}\n\nexport function invokeWithArgsOrReturn(value: any, args: any[]): any {\n  return typeof value === 'function' ? value(...args) : value;\n}\n\nexport function debounce<T>(\n  fn: (arg: T) => void,\n  ms: number\n): (arg: T) => void {\n  // Avoid wrapping in `setTimeout` if ms is 0 anyway\n  if (ms === 0) {\n    return fn;\n  }\n\n  let timeout: any;\n\n  return (arg): void => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => {\n      fn(arg);\n    }, ms);\n  };\n}\n\nexport function removeProperties<T>(obj: T, keys: string[]): Partial<T> {\n  const clone = {...obj};\n  keys.forEach((key) => {\n    delete (clone as any)[key];\n  });\n  return clone;\n}\n\nexport function splitBySpaces(value: string): string[] {\n  return value.split(/\\s+/).filter(Boolean);\n}\n\nexport function normalizeToArray<T>(value: T | T[]): T[] {\n  return ([] as T[]).concat(value);\n}\n\nexport function pushIfUnique<T>(arr: T[], value: T): void {\n  if (arr.indexOf(value) === -1) {\n    arr.push(value);\n  }\n}\n\nexport function appendPxIfNumber(value: string | number): string {\n  return typeof value === 'number' ? `${value}px` : value;\n}\n\nexport function unique<T>(arr: T[]): T[] {\n  return arr.filter((item, index) => arr.indexOf(item) === index);\n}\n\nexport function getNumber(value: string | number): number {\n  return typeof value === 'number' ? value : parseFloat(value);\n}\n\nexport function getBasePlacement(placement: Placement): BasePlacement {\n  return placement.split('-')[0] as BasePlacement;\n}\n\nexport function arrayFrom(value: ArrayLike<any>): any[] {\n  return [].slice.call(value);\n}\n\nexport function removeUndefinedProps(\n  obj: Record<string, unknown>\n): Partial<Record<string, unknown>> {\n  return Object.keys(obj).reduce((acc, key) => {\n    if (obj[key] !== undefined) {\n      (acc as any)[key] = obj[key];\n    }\n\n    return acc;\n  }, {});\n}\n", "import {ReferenceElement, Targets} from './types';\nimport {PopperTreeData} from './types-internal';\nimport {arrayFrom, isType, normalizeToArray, getBasePlacement} from './utils';\n\nexport function div(): HTMLDivElement {\n  return document.createElement('div');\n}\n\nexport function isElement(value: unknown): value is Element | DocumentFragment {\n  return ['Element', 'Fragment'].some((type) => isType(value, type));\n}\n\nexport function isNodeList(value: unknown): value is NodeList {\n  return isType(value, 'NodeList');\n}\n\nexport function isMouseEvent(value: unknown): value is MouseEvent {\n  return isType(value, 'MouseEvent');\n}\n\nexport function isReferenceElement(value: any): value is ReferenceElement {\n  return !!(value && value._tippy && value._tippy.reference === value);\n}\n\nexport function getArrayOfElements(value: Targets): Element[] {\n  if (isElement(value)) {\n    return [value];\n  }\n\n  if (isNodeList(value)) {\n    return arrayFrom(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return arrayFrom(document.querySelectorAll(value));\n}\n\nexport function setTransitionDuration(\n  els: (HTMLDivElement | null)[],\n  value: number\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.style.transitionDuration = `${value}ms`;\n    }\n  });\n}\n\nexport function setVisibilityState(\n  els: (HTMLDivElement | null)[],\n  state: 'visible' | 'hidden'\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.setAttribute('data-state', state);\n    }\n  });\n}\n\nexport function getOwnerDocument(\n  elementOrElements: Element | Element[]\n): Document {\n  const [element] = normalizeToArray(elementOrElements);\n  return element ? element.ownerDocument || document : document;\n}\n\nexport function isCursorOutsideInteractiveBorder(\n  popperTreeData: PopperTreeData[],\n  event: MouseEvent\n): boolean {\n  const {clientX, clientY} = event;\n\n  return popperTreeData.every(({popperRect, popperState, props}) => {\n    const {interactiveBorder} = props;\n    const basePlacement = getBasePlacement(popperState.placement);\n    const offsetData = popperState.modifiersData.offset;\n\n    if (!offsetData) {\n      return true;\n    }\n\n    const topDistance = basePlacement === 'bottom' ? offsetData.top!.y : 0;\n    const bottomDistance = basePlacement === 'top' ? offsetData.bottom!.y : 0;\n    const leftDistance = basePlacement === 'right' ? offsetData.left!.x : 0;\n    const rightDistance = basePlacement === 'left' ? offsetData.right!.x : 0;\n\n    const exceedsTop =\n      popperRect.top - clientY + topDistance > interactiveBorder;\n    const exceedsBottom =\n      clientY - popperRect.bottom - bottomDistance > interactiveBorder;\n    const exceedsLeft =\n      popperRect.left - clientX + leftDistance > interactiveBorder;\n    const exceedsRight =\n      clientX - popperRect.right - rightDistance > interactiveBorder;\n\n    return exceedsTop || exceedsBottom || exceedsLeft || exceedsRight;\n  });\n}\n\nexport function updateTransitionEndListener(\n  box: HTMLDivElement,\n  action: 'add' | 'remove',\n  listener: (event: TransitionEvent) => void\n): void {\n  const method = `${action}EventListener` as\n    | 'addEventListener'\n    | 'removeEventListener';\n\n  // some browsers apparently support `transition` (unprefixed) but only fire\n  // `webkitTransitionEnd`...\n  ['transitionend', 'webkitTransitionEnd'].forEach((event) => {\n    box[method](event, listener as EventListener);\n  });\n}\n", "import {TOUCH_OPTIONS} from './constants';\nimport {isReferenceElement} from './dom-utils';\n\nexport const currentInput = {isTouch: false};\nlet lastMouseMoveTime = 0;\n\n/**\n * When a `touchstart` event is fired, it's assumed the user is using touch\n * input. We'll bind a `mousemove` event listener to listen for mouse input in\n * the future. This way, the `isTouch` property is fully dynamic and will handle\n * hybrid devices that use a mix of touch + mouse input.\n */\nexport function onDocumentTouchStart(): void {\n  if (currentInput.isTouch) {\n    return;\n  }\n\n  currentInput.isTouch = true;\n\n  if (window.performance) {\n    document.addEventListener('mousemove', onDocumentMouseMove);\n  }\n}\n\n/**\n * When two `mousemove` event are fired consecutively within 20ms, it's assumed\n * the user is using mouse input again. `mousemove` can fire on touch devices as\n * well, but very rarely that quickly.\n */\nexport function onDocumentMouseMove(): void {\n  const now = performance.now();\n\n  if (now - lastMouseMoveTime < 20) {\n    currentInput.isTouch = false;\n\n    document.removeEventListener('mousemove', onDocumentMouseMove);\n  }\n\n  lastMouseMoveTime = now;\n}\n\n/**\n * When an element is in focus and has a tippy, leaving the tab/window and\n * returning causes it to show again. For mouse users this is unexpected, but\n * for keyboard use it makes sense.\n * TODO: find a better technique to solve this problem\n */\nexport function onWindowBlur(): void {\n  const activeElement = document.activeElement as HTMLElement | null;\n\n  if (isReferenceElement(activeElement)) {\n    const instance = activeElement._tippy!;\n\n    if (activeElement.blur && !instance.state.isVisible) {\n      activeElement.blur();\n    }\n  }\n}\n\nexport default function bindGlobalEventListeners(): void {\n  document.addEventListener('touchstart', onDocumentTouchStart, TOUCH_OPTIONS);\n  window.addEventListener('blur', onWindowBlur);\n}\n", "import {Targets} from './types';\n\nexport function createMemoryLeakWarning(method: string): string {\n  const txt = method === 'destroy' ? 'n already-' : ' ';\n\n  return [\n    `${method}() was called on a${txt}destroyed instance. This is a no-op but`,\n    'indicates a potential memory leak.',\n  ].join(' ');\n}\n\nexport function clean(value: string): string {\n  const spacesAndTabs = /[ \\t]{2,}/g;\n  const lineStartWithSpaces = /^[ \\t]*/gm;\n\n  return value\n    .replace(spacesAndTabs, ' ')\n    .replace(lineStartWithSpaces, '')\n    .trim();\n}\n\nfunction getDevMessage(message: string): string {\n  return clean(`\n  %ctippy.js\n\n  %c${clean(message)}\n\n  %c👷‍ This is a development-only message. It will be removed in production.\n  `);\n}\n\nexport function getFormattedMessage(message: string): string[] {\n  return [\n    getDevMessage(message),\n    // title\n    'color: #00C584; font-size: 1.3em; font-weight: bold;',\n    // message\n    'line-height: 1.5',\n    // footer\n    'color: #a6a095;',\n  ];\n}\n\n// Assume warnings and errors never have the same message\nlet visitedMessages: Set<string>;\nif (__DEV__) {\n  resetVisitedMessages();\n}\n\nexport function resetVisitedMessages(): void {\n  visitedMessages = new Set();\n}\n\nexport function warnWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.warn(...getFormattedMessage(message));\n  }\n}\n\nexport function errorWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.error(...getFormattedMessage(message));\n  }\n}\n\nexport function validateTargets(targets: Targets): void {\n  const didPassFalsyValue = !targets;\n  const didPassPlainObject =\n    Object.prototype.toString.call(targets) === '[object Object]' &&\n    !(targets as any).addEventListener;\n\n  errorWhen(\n    didPassFalsyValue,\n    [\n      'tippy() was passed',\n      '`' + String(targets) + '`',\n      'as its targets (first) argument. Valid types are: String, Element,',\n      'Element[], or NodeList.',\n    ].join(' ')\n  );\n\n  errorWhen(\n    didPassPlainObject,\n    [\n      'tippy() was passed a plain object which is not supported as an argument',\n      'for virtual positioning. Use props.getReferenceClientRect instead.',\n    ].join(' ')\n  );\n}\n", "import {DefaultProps, Plugin, Props, ReferenceElement, Tippy} from './types';\nimport {\n  hasOwnProperty,\n  removeProperties,\n  invokeWithArgsOrReturn,\n} from './utils';\nimport {warnWhen} from './validation';\n\nconst pluginProps = {\n  animateFill: false,\n  followCursor: false,\n  inlinePositioning: false,\n  sticky: false,\n};\n\nconst renderProps = {\n  allowHTML: false,\n  animation: 'fade',\n  arrow: true,\n  content: '',\n  inertia: false,\n  maxWidth: 350,\n  role: 'tooltip',\n  theme: '',\n  zIndex: 9999,\n};\n\nexport const defaultProps: DefaultProps = {\n  appendTo: () => document.body,\n  aria: {\n    content: 'auto',\n    expanded: 'auto',\n  },\n  delay: 0,\n  duration: [300, 250],\n  getReferenceClientRect: null,\n  hideOnClick: true,\n  ignoreAttributes: false,\n  interactive: false,\n  interactiveBorder: 2,\n  interactiveDebounce: 0,\n  moveTransition: '',\n  offset: [0, 10],\n  onAfterUpdate() {},\n  onBeforeUpdate() {},\n  onCreate() {},\n  onDestroy() {},\n  onHidden() {},\n  onHide() {},\n  onMount() {},\n  onShow() {},\n  onShown() {},\n  onTrigger() {},\n  onUntrigger() {},\n  onClickOutside() {},\n  placement: 'top',\n  plugins: [],\n  popperOptions: {},\n  render: null,\n  showOnCreate: false,\n  touch: true,\n  trigger: 'mouseenter focus',\n  triggerTarget: null,\n  ...pluginProps,\n  ...renderProps,\n};\n\nconst defaultKeys = Object.keys(defaultProps);\n\nexport const setDefaultProps: Tippy['setDefaultProps'] = (partialProps) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateProps(partialProps, []);\n  }\n\n  const keys = Object.keys(partialProps) as Array<keyof DefaultProps>;\n  keys.forEach((key) => {\n    (defaultProps as any)[key] = partialProps[key];\n  });\n};\n\nexport function getExtendedPassedProps(\n  passedProps: Partial<Props> & Record<string, unknown>\n): Partial<Props> {\n  const plugins = passedProps.plugins || [];\n  const pluginProps = plugins.reduce<Record<string, unknown>>((acc, plugin) => {\n    const {name, defaultValue} = plugin;\n\n    if (name) {\n      acc[name] =\n        passedProps[name] !== undefined ? passedProps[name] : defaultValue;\n    }\n\n    return acc;\n  }, {});\n\n  return {\n    ...passedProps,\n    ...pluginProps,\n  };\n}\n\nexport function getDataAttributeProps(\n  reference: ReferenceElement,\n  plugins: Plugin[]\n): Record<string, unknown> {\n  const propKeys = plugins\n    ? Object.keys(getExtendedPassedProps({...defaultProps, plugins}))\n    : defaultKeys;\n\n  const props = propKeys.reduce(\n    (acc: Partial<Props> & Record<string, unknown>, key) => {\n      const valueAsString = (\n        reference.getAttribute(`data-tippy-${key}`) || ''\n      ).trim();\n\n      if (!valueAsString) {\n        return acc;\n      }\n\n      if (key === 'content') {\n        acc[key] = valueAsString;\n      } else {\n        try {\n          acc[key] = JSON.parse(valueAsString);\n        } catch (e) {\n          acc[key] = valueAsString;\n        }\n      }\n\n      return acc;\n    },\n    {}\n  );\n\n  return props;\n}\n\nexport function evaluateProps(\n  reference: ReferenceElement,\n  props: Props\n): Props {\n  const out = {\n    ...props,\n    content: invokeWithArgsOrReturn(props.content, [reference]),\n    ...(props.ignoreAttributes\n      ? {}\n      : getDataAttributeProps(reference, props.plugins)),\n  };\n\n  out.aria = {\n    ...defaultProps.aria,\n    ...out.aria,\n  };\n\n  out.aria = {\n    expanded:\n      out.aria.expanded === 'auto' ? props.interactive : out.aria.expanded,\n    content:\n      out.aria.content === 'auto'\n        ? props.interactive\n          ? null\n          : 'describedby'\n        : out.aria.content,\n  };\n\n  return out;\n}\n\nexport function validateProps(\n  partialProps: Partial<Props> = {},\n  plugins: Plugin[] = []\n): void {\n  const keys = Object.keys(partialProps) as Array<keyof Props>;\n  keys.forEach((prop) => {\n    const nonPluginProps = removeProperties(\n      defaultProps,\n      Object.keys(pluginProps)\n    );\n\n    let didPassUnknownProp = !hasOwnProperty(nonPluginProps, prop);\n\n    // Check if the prop exists in `plugins`\n    if (didPassUnknownProp) {\n      didPassUnknownProp =\n        plugins.filter((plugin) => plugin.name === prop).length === 0;\n    }\n\n    warnWhen(\n      didPassUnknownProp,\n      [\n        `\\`${prop}\\``,\n        \"is not a valid prop. You may have spelled it incorrectly, or if it's\",\n        'a plugin, forgot to pass it in an array as props.plugins.',\n        '\\n\\n',\n        'All props: https://atomiks.github.io/tippyjs/v6/all-props/\\n',\n        'Plugins: https://atomiks.github.io/tippyjs/v6/plugins/',\n      ].join(' ')\n    );\n  });\n}\n", "import {\n  ARROW_CLASS,\n  BACKDROP_CLASS,\n  BOX_CLASS,\n  CONTENT_CLASS,\n  SVG_ARROW_CLASS,\n} from './constants';\nimport {div, isElement} from './dom-utils';\nimport {Instance, PopperElement, Props} from './types';\nimport {PopperChildren} from './types-internal';\nimport {arrayFrom} from './utils';\n\n// Firefox extensions don't allow .innerHTML = \"...\" property. This tricks it.\nconst innerHTML = (): 'innerHTML' => 'innerHTML';\n\nfunction dangerouslySetInnerHTML(element: Element, html: string): void {\n  element[innerHTML()] = html;\n}\n\nfunction createArrowElement(value: Props['arrow']): HTMLDivElement {\n  const arrow = div();\n\n  if (value === true) {\n    arrow.className = ARROW_CLASS;\n  } else {\n    arrow.className = SVG_ARROW_CLASS;\n\n    if (isElement(value)) {\n      arrow.appendChild(value);\n    } else {\n      dangerouslySetInnerHTML(arrow, value as string);\n    }\n  }\n\n  return arrow;\n}\n\nexport function setContent(content: HTMLDivElement, props: Props): void {\n  if (isElement(props.content)) {\n    dangerouslySetInnerHTML(content, '');\n    content.appendChild(props.content);\n  } else if (typeof props.content !== 'function') {\n    if (props.allowHTML) {\n      dangerouslySetInnerHTML(content, props.content);\n    } else {\n      content.textContent = props.content;\n    }\n  }\n}\n\nexport function getChildren(popper: PopperElement): PopperChildren {\n  const box = popper.firstElementChild as HTMLDivElement;\n  const boxChildren = arrayFrom(box.children);\n\n  return {\n    box,\n    content: boxChildren.find((node) => node.classList.contains(CONTENT_CLASS)),\n    arrow: boxChildren.find(\n      (node) =>\n        node.classList.contains(ARROW_CLASS) ||\n        node.classList.contains(SVG_ARROW_CLASS)\n    ),\n    backdrop: boxChildren.find((node) =>\n      node.classList.contains(BACKDROP_CLASS)\n    ),\n  };\n}\n\nexport function render(\n  instance: Instance\n): {\n  popper: PopperElement;\n  onUpdate?: (prevProps: Props, nextProps: Props) => void;\n} {\n  const popper = div();\n\n  const box = div();\n  box.className = BOX_CLASS;\n  box.setAttribute('data-state', 'hidden');\n  box.setAttribute('tabindex', '-1');\n\n  const content = div();\n  content.className = CONTENT_CLASS;\n  content.setAttribute('data-state', 'hidden');\n\n  setContent(content, instance.props);\n\n  popper.appendChild(box);\n  box.appendChild(content);\n\n  onUpdate(instance.props, instance.props);\n\n  function onUpdate(prevProps: Props, nextProps: Props): void {\n    const {box, content, arrow} = getChildren(popper);\n\n    if (nextProps.theme) {\n      box.setAttribute('data-theme', nextProps.theme);\n    } else {\n      box.removeAttribute('data-theme');\n    }\n\n    if (typeof nextProps.animation === 'string') {\n      box.setAttribute('data-animation', nextProps.animation);\n    } else {\n      box.removeAttribute('data-animation');\n    }\n\n    if (nextProps.inertia) {\n      box.setAttribute('data-inertia', '');\n    } else {\n      box.removeAttribute('data-inertia');\n    }\n\n    box.style.maxWidth =\n      typeof nextProps.maxWidth === 'number'\n        ? `${nextProps.maxWidth}px`\n        : nextProps.maxWidth;\n\n    if (nextProps.role) {\n      box.setAttribute('role', nextProps.role);\n    } else {\n      box.removeAttribute('role');\n    }\n\n    if (\n      prevProps.content !== nextProps.content ||\n      prevProps.allowHTML !== nextProps.allowHTML\n    ) {\n      setContent(content, instance.props);\n    }\n\n    if (nextProps.arrow) {\n      if (!arrow) {\n        box.appendChild(createArrowElement(nextProps.arrow));\n      } else if (prevProps.arrow !== nextProps.arrow) {\n        box.removeChild(arrow);\n        box.appendChild(createArrowElement(nextProps.arrow));\n      }\n    } else if (arrow) {\n      box.removeChild(arrow!);\n    }\n  }\n\n  return {\n    popper,\n    onUpdate,\n  };\n}\n\n// Runtime check to identify if the render function is the default one; this\n// way we can apply default CSS transitions logic and it can be tree-shaken away\nrender.$$tippy = true;\n", "import {createPopper, StrictModifiers, Modifier} from '@popperjs/core';\nimport {currentInput} from './bindGlobalEventListeners';\nimport {isIE} from './browser';\nimport {TOUCH_OPTIONS} from './constants';\nimport {\n  div,\n  getOwnerDocument,\n  isCursorOutsideInteractiveBorder,\n  isMouseEvent,\n  setTransitionDuration,\n  setVisibilityState,\n  updateTransitionEndListener,\n} from './dom-utils';\nimport {defaultProps, evaluateProps, getExtendedPassedProps} from './props';\nimport {getChildren} from './template';\nimport {\n  Content,\n  Instance,\n  LifecycleHooks,\n  PopperElement,\n  Props,\n  ReferenceElement,\n} from './types';\nimport {ListenerObject, PopperTreeData, PopperChildren} from './types-internal';\nimport {\n  arrayFrom,\n  debounce,\n  getValueAtIndexOrReturn,\n  invokeWithArgsOrReturn,\n  normalizeToArray,\n  pushIfUnique,\n  splitBySpaces,\n  unique,\n  removeUndefinedProps,\n} from './utils';\nimport {createMemoryLeakWarning, errorWhen, warnWhen} from './validation';\n\nlet idCounter = 1;\nlet mouseMoveListeners: ((event: MouseEvent) => void)[] = [];\n\n// Used by `hideAll()`\nexport let mountedInstances: Instance[] = [];\n\nexport default function createTippy(\n  reference: ReferenceElement,\n  passedProps: Partial<Props>\n): Instance {\n  const props = evaluateProps(reference, {\n    ...defaultProps,\n    ...getExtendedPassedProps(removeUndefinedProps(passedProps)),\n  });\n\n  // ===========================================================================\n  // 🔒 Private members\n  // ===========================================================================\n  let showTimeout: any;\n  let hideTimeout: any;\n  let scheduleHideAnimationFrame: number;\n  let isVisibleFromClick = false;\n  let didHideDueToDocumentMouseDown = false;\n  let didTouchMove = false;\n  let ignoreOnFirstUpdate = false;\n  let lastTriggerEvent: Event | undefined;\n  let currentTransitionEndListener: (event: TransitionEvent) => void;\n  let onFirstUpdate: () => void;\n  let listeners: ListenerObject[] = [];\n  let debouncedOnMouseMove = debounce(onMouseMove, props.interactiveDebounce);\n  let currentTarget: Element;\n  const doc = getOwnerDocument(props.triggerTarget || reference);\n\n  // ===========================================================================\n  // 🔑 Public members\n  // ===========================================================================\n  const id = idCounter++;\n  const popperInstance = null;\n  const plugins = unique(props.plugins);\n\n  const state = {\n    // Is the instance currently enabled?\n    isEnabled: true,\n    // Is the tippy currently showing and not transitioning out?\n    isVisible: false,\n    // Has the instance been destroyed?\n    isDestroyed: false,\n    // Is the tippy currently mounted to the DOM?\n    isMounted: false,\n    // Has the tippy finished transitioning in?\n    isShown: false,\n  };\n\n  const instance: Instance = {\n    // properties\n    id,\n    reference,\n    popper: div(),\n    popperInstance,\n    props,\n    state,\n    plugins,\n    // methods\n    clearDelayTimeouts,\n    setProps,\n    setContent,\n    show,\n    hide,\n    hideWithInteractivity,\n    enable,\n    disable,\n    unmount,\n    destroy,\n  };\n\n  // TODO: Investigate why this early return causes a TDZ error in the tests —\n  // it doesn't seem to happen in the browser\n  /* istanbul ignore if */\n  if (!props.render) {\n    if (__DEV__) {\n      errorWhen(true, 'render() function has not been supplied.');\n    }\n\n    return instance;\n  }\n\n  // ===========================================================================\n  // Initial mutations\n  // ===========================================================================\n  const {popper, onUpdate} = props.render(instance);\n\n  popper.setAttribute('data-__NAMESPACE_PREFIX__-root', '');\n  popper.id = `__NAMESPACE_PREFIX__-${instance.id}`;\n\n  instance.popper = popper;\n  reference._tippy = instance;\n  popper._tippy = instance;\n\n  const pluginsHooks = plugins.map((plugin) => plugin.fn(instance));\n  const hasAriaExpanded = reference.hasAttribute('aria-expanded');\n\n  addListeners();\n  handleAriaExpandedAttribute();\n  handleStyles();\n\n  invokeHook('onCreate', [instance]);\n\n  if (props.showOnCreate) {\n    scheduleShow();\n  }\n\n  // Prevent a tippy with a delay from hiding if the cursor left then returned\n  // before it started hiding\n  popper.addEventListener('mouseenter', () => {\n    if (instance.props.interactive && instance.state.isVisible) {\n      instance.clearDelayTimeouts();\n    }\n  });\n\n  popper.addEventListener('mouseleave', (event) => {\n    if (\n      instance.props.interactive &&\n      instance.props.trigger.indexOf('mouseenter') >= 0\n    ) {\n      doc.addEventListener('mousemove', debouncedOnMouseMove);\n      debouncedOnMouseMove(event);\n    }\n  });\n\n  return instance;\n\n  // ===========================================================================\n  // 🔒 Private methods\n  // ===========================================================================\n  function getNormalizedTouchSettings(): [string | boolean, number] {\n    const {touch} = instance.props;\n    return Array.isArray(touch) ? touch : [touch, 0];\n  }\n\n  function getIsCustomTouchBehavior(): boolean {\n    return getNormalizedTouchSettings()[0] === 'hold';\n  }\n\n  function getIsDefaultRenderFn(): boolean {\n    // @ts-ignore\n    return !!instance.props.render?.$$tippy;\n  }\n\n  function getCurrentTarget(): Element {\n    return currentTarget || reference;\n  }\n\n  function getDefaultTemplateChildren(): PopperChildren {\n    return getChildren(popper);\n  }\n\n  function getDelay(isShow: boolean): number {\n    // For touch or keyboard input, force `0` delay for UX reasons\n    // Also if the instance is mounted but not visible (transitioning out),\n    // ignore delay\n    if (\n      (instance.state.isMounted && !instance.state.isVisible) ||\n      currentInput.isTouch ||\n      (lastTriggerEvent && lastTriggerEvent.type === 'focus')\n    ) {\n      return 0;\n    }\n\n    return getValueAtIndexOrReturn(\n      instance.props.delay,\n      isShow ? 0 : 1,\n      defaultProps.delay\n    );\n  }\n\n  function handleStyles(): void {\n    popper.style.pointerEvents =\n      instance.props.interactive && instance.state.isVisible ? '' : 'none';\n    popper.style.zIndex = `${instance.props.zIndex}`;\n  }\n\n  function invokeHook(\n    hook: keyof LifecycleHooks,\n    args: [Instance, any?],\n    shouldInvokePropsHook = true\n  ): void {\n    pluginsHooks.forEach((pluginHooks) => {\n      if (pluginHooks[hook]) {\n        pluginHooks[hook]!(...args);\n      }\n    });\n\n    if (shouldInvokePropsHook) {\n      instance.props[hook](...args);\n    }\n  }\n\n  function handleAriaContentAttribute(): void {\n    const {aria} = instance.props;\n\n    if (!aria.content) {\n      return;\n    }\n\n    const attr = `aria-${aria.content}`;\n    const id = popper.id;\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      const currentValue = node.getAttribute(attr);\n\n      if (instance.state.isVisible) {\n        node.setAttribute(attr, currentValue ? `${currentValue} ${id}` : id);\n      } else {\n        const nextValue = currentValue && currentValue.replace(id, '').trim();\n\n        if (nextValue) {\n          node.setAttribute(attr, nextValue);\n        } else {\n          node.removeAttribute(attr);\n        }\n      }\n    });\n  }\n\n  function handleAriaExpandedAttribute(): void {\n    if (hasAriaExpanded || !instance.props.aria.expanded) {\n      return;\n    }\n\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      if (instance.props.interactive) {\n        node.setAttribute(\n          'aria-expanded',\n          instance.state.isVisible && node === getCurrentTarget()\n            ? 'true'\n            : 'false'\n        );\n      } else {\n        node.removeAttribute('aria-expanded');\n      }\n    });\n  }\n\n  function cleanupInteractiveMouseListeners(): void {\n    doc.removeEventListener('mousemove', debouncedOnMouseMove);\n    mouseMoveListeners = mouseMoveListeners.filter(\n      (listener) => listener !== debouncedOnMouseMove\n    );\n  }\n\n  function onDocumentPress(event: MouseEvent | TouchEvent): void {\n    // Moved finger to scroll instead of an intentional tap outside\n    if (currentInput.isTouch) {\n      if (didTouchMove || event.type === 'mousedown') {\n        return;\n      }\n    }\n\n    // Clicked on interactive popper\n    if (\n      instance.props.interactive &&\n      popper.contains(event.target as Element)\n    ) {\n      return;\n    }\n\n    // Clicked on the event listeners target\n    if (getCurrentTarget().contains(event.target as Element)) {\n      if (currentInput.isTouch) {\n        return;\n      }\n\n      if (\n        instance.state.isVisible &&\n        instance.props.trigger.indexOf('click') >= 0\n      ) {\n        return;\n      }\n    } else {\n      invokeHook('onClickOutside', [instance, event]);\n    }\n\n    if (instance.props.hideOnClick === true) {\n      isVisibleFromClick = false;\n      instance.clearDelayTimeouts();\n      instance.hide();\n\n      // `mousedown` event is fired right before `focus` if pressing the\n      // currentTarget. This lets a tippy with `focus` trigger know that it\n      // should not show\n      didHideDueToDocumentMouseDown = true;\n      setTimeout(() => {\n        didHideDueToDocumentMouseDown = false;\n      });\n\n      // The listener gets added in `scheduleShow()`, but this may be hiding it\n      // before it shows, and hide()'s early bail-out behavior can prevent it\n      // from being cleaned up\n      if (!instance.state.isMounted) {\n        removeDocumentPress();\n      }\n    }\n  }\n\n  function onTouchMove(): void {\n    didTouchMove = true;\n  }\n\n  function onTouchStart(): void {\n    didTouchMove = false;\n  }\n\n  function addDocumentPress(): void {\n    doc.addEventListener('mousedown', onDocumentPress, true);\n    doc.addEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.addEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.addEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function removeDocumentPress(): void {\n    doc.removeEventListener('mousedown', onDocumentPress, true);\n    doc.removeEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.removeEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.removeEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function onTransitionedOut(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, () => {\n      if (\n        !instance.state.isVisible &&\n        popper.parentNode &&\n        popper.parentNode.contains(popper)\n      ) {\n        callback();\n      }\n    });\n  }\n\n  function onTransitionedIn(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, callback);\n  }\n\n  function onTransitionEnd(duration: number, callback: () => void): void {\n    const box = getDefaultTemplateChildren().box;\n\n    function listener(event: TransitionEvent): void {\n      if (event.target === box) {\n        updateTransitionEndListener(box, 'remove', listener);\n        callback();\n      }\n    }\n\n    // Make callback synchronous if duration is 0\n    // `transitionend` won't fire otherwise\n    if (duration === 0) {\n      return callback();\n    }\n\n    updateTransitionEndListener(box, 'remove', currentTransitionEndListener);\n    updateTransitionEndListener(box, 'add', listener);\n\n    currentTransitionEndListener = listener;\n  }\n\n  function on(\n    eventType: string,\n    handler: EventListener,\n    options: boolean | object = false\n  ): void {\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach((node) => {\n      node.addEventListener(eventType, handler, options);\n      listeners.push({node, eventType, handler, options});\n    });\n  }\n\n  function addListeners(): void {\n    if (getIsCustomTouchBehavior()) {\n      on('touchstart', onTrigger, {passive: true});\n      on('touchend', onMouseLeave as EventListener, {passive: true});\n    }\n\n    splitBySpaces(instance.props.trigger).forEach((eventType) => {\n      if (eventType === 'manual') {\n        return;\n      }\n\n      on(eventType, onTrigger);\n\n      switch (eventType) {\n        case 'mouseenter':\n          on('mouseleave', onMouseLeave as EventListener);\n          break;\n        case 'focus':\n          on(isIE ? 'focusout' : 'blur', onBlurOrFocusOut as EventListener);\n          break;\n        case 'focusin':\n          on('focusout', onBlurOrFocusOut as EventListener);\n          break;\n      }\n    });\n  }\n\n  function removeListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function onTrigger(event: Event): void {\n    let shouldScheduleClickHide = false;\n\n    if (\n      !instance.state.isEnabled ||\n      isEventListenerStopped(event) ||\n      didHideDueToDocumentMouseDown\n    ) {\n      return;\n    }\n\n    const wasFocused = lastTriggerEvent?.type === 'focus';\n\n    lastTriggerEvent = event;\n    currentTarget = event.currentTarget as Element;\n\n    handleAriaExpandedAttribute();\n\n    if (!instance.state.isVisible && isMouseEvent(event)) {\n      // If scrolling, `mouseenter` events can be fired if the cursor lands\n      // over a new target, but `mousemove` events don't get fired. This\n      // causes interactive tooltips to get stuck open until the cursor is\n      // moved\n      mouseMoveListeners.forEach((listener) => listener(event));\n    }\n\n    // Toggle show/hide when clicking click-triggered tooltips\n    if (\n      event.type === 'click' &&\n      (instance.props.trigger.indexOf('mouseenter') < 0 ||\n        isVisibleFromClick) &&\n      instance.props.hideOnClick !== false &&\n      instance.state.isVisible\n    ) {\n      shouldScheduleClickHide = true;\n    } else {\n      scheduleShow(event);\n    }\n\n    if (event.type === 'click') {\n      isVisibleFromClick = !shouldScheduleClickHide;\n    }\n\n    if (shouldScheduleClickHide && !wasFocused) {\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseMove(event: MouseEvent): void {\n    const target = event.target as Node;\n    const isCursorOverReferenceOrPopper =\n      reference.contains(target) || popper.contains(target);\n\n    if (event.type === 'mousemove' && isCursorOverReferenceOrPopper) {\n      return;\n    }\n\n    const popperTreeData = getNestedPopperTree()\n      .concat(popper)\n      .map((popper) => {\n        const instance = popper._tippy!;\n        const state = instance.popperInstance?.state;\n\n        if (state) {\n          return {\n            popperRect: popper.getBoundingClientRect(),\n            popperState: state,\n            props,\n          };\n        }\n\n        return null;\n      })\n      .filter(Boolean) as PopperTreeData[];\n\n    if (isCursorOutsideInteractiveBorder(popperTreeData, event)) {\n      cleanupInteractiveMouseListeners();\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseLeave(event: MouseEvent): void {\n    const shouldBail =\n      isEventListenerStopped(event) ||\n      (instance.props.trigger.indexOf('click') >= 0 && isVisibleFromClick);\n\n    if (shouldBail) {\n      return;\n    }\n\n    if (instance.props.interactive) {\n      instance.hideWithInteractivity(event);\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function onBlurOrFocusOut(event: FocusEvent): void {\n    if (\n      instance.props.trigger.indexOf('focusin') < 0 &&\n      event.target !== getCurrentTarget()\n    ) {\n      return;\n    }\n\n    // If focus was moved to within the popper\n    if (\n      instance.props.interactive &&\n      event.relatedTarget &&\n      popper.contains(event.relatedTarget as Element)\n    ) {\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function isEventListenerStopped(event: Event): boolean {\n    return currentInput.isTouch\n      ? getIsCustomTouchBehavior() !== event.type.indexOf('touch') >= 0\n      : false;\n  }\n\n  function createPopperInstance(): void {\n    destroyPopperInstance();\n\n    const {\n      popperOptions,\n      placement,\n      offset,\n      getReferenceClientRect,\n      moveTransition,\n    } = instance.props;\n\n    const arrow = getIsDefaultRenderFn() ? getChildren(popper).arrow : null;\n\n    const computedReference = getReferenceClientRect\n      ? {\n          getBoundingClientRect: getReferenceClientRect,\n          contextElement:\n            getReferenceClientRect.contextElement || getCurrentTarget(),\n        }\n      : reference;\n\n    const tippyModifier: Modifier<'$$tippy', {}> = {\n      name: '$$tippy',\n      enabled: true,\n      phase: 'beforeWrite',\n      requires: ['computeStyles'],\n      fn({state}) {\n        if (getIsDefaultRenderFn()) {\n          const {box} = getDefaultTemplateChildren();\n\n          ['placement', 'reference-hidden', 'escaped'].forEach((attr) => {\n            if (attr === 'placement') {\n              box.setAttribute('data-placement', state.placement);\n            } else {\n              if (state.attributes.popper[`data-popper-${attr}`]) {\n                box.setAttribute(`data-${attr}`, '');\n              } else {\n                box.removeAttribute(`data-${attr}`);\n              }\n            }\n          });\n\n          state.attributes.popper = {};\n        }\n      },\n    };\n\n    type TippyModifier = Modifier<'$$tippy', {}>;\n    type ExtendedModifiers = StrictModifiers | Partial<TippyModifier>;\n\n    const modifiers: Array<ExtendedModifiers> = [\n      {\n        name: 'offset',\n        options: {\n          offset,\n        },\n      },\n      {\n        name: 'preventOverflow',\n        options: {\n          padding: {\n            top: 2,\n            bottom: 2,\n            left: 5,\n            right: 5,\n          },\n        },\n      },\n      {\n        name: 'flip',\n        options: {\n          padding: 5,\n        },\n      },\n      {\n        name: 'computeStyles',\n        options: {\n          adaptive: !moveTransition,\n        },\n      },\n      tippyModifier,\n    ];\n\n    if (getIsDefaultRenderFn() && arrow) {\n      modifiers.push({\n        name: 'arrow',\n        options: {\n          element: arrow,\n          padding: 3,\n        },\n      });\n    }\n\n    modifiers.push(...(popperOptions?.modifiers || []));\n\n    instance.popperInstance = createPopper<ExtendedModifiers>(\n      computedReference,\n      popper,\n      {\n        ...popperOptions,\n        placement,\n        onFirstUpdate,\n        modifiers,\n      }\n    );\n  }\n\n  function destroyPopperInstance(): void {\n    if (instance.popperInstance) {\n      instance.popperInstance.destroy();\n      instance.popperInstance = null;\n    }\n  }\n\n  function mount(): void {\n    const {appendTo} = instance.props;\n\n    let parentNode: any;\n\n    // By default, we'll append the popper to the triggerTargets's parentNode so\n    // it's directly after the reference element so the elements inside the\n    // tippy can be tabbed to\n    // If there are clipping issues, the user can specify a different appendTo\n    // and ensure focus management is handled correctly manually\n    const node = getCurrentTarget();\n\n    if (\n      (instance.props.interactive && appendTo === defaultProps.appendTo) ||\n      appendTo === 'parent'\n    ) {\n      parentNode = node.parentNode;\n    } else {\n      parentNode = invokeWithArgsOrReturn(appendTo, [node]);\n    }\n\n    // The popper element needs to exist on the DOM before its position can be\n    // updated as Popper needs to read its dimensions\n    if (!parentNode.contains(popper)) {\n      parentNode.appendChild(popper);\n    }\n\n    createPopperInstance();\n\n    /* istanbul ignore else */\n    if (__DEV__) {\n      // Accessibility check\n      warnWhen(\n        instance.props.interactive &&\n          appendTo === defaultProps.appendTo &&\n          node.nextElementSibling !== popper,\n        [\n          'Interactive tippy element may not be accessible via keyboard',\n          'navigation because it is not directly after the reference element',\n          'in the DOM source order.',\n          '\\n\\n',\n          'Using a wrapper <div> or <span> tag around the reference element',\n          'solves this by creating a new parentNode context.',\n          '\\n\\n',\n          'Specifying `appendTo: document.body` silences this warning, but it',\n          'assumes you are using a focus management solution to handle',\n          'keyboard navigation.',\n          '\\n\\n',\n          'See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity',\n        ].join(' ')\n      );\n    }\n  }\n\n  function getNestedPopperTree(): PopperElement[] {\n    return arrayFrom(\n      popper.querySelectorAll('[data-__NAMESPACE_PREFIX__-root]')\n    );\n  }\n\n  function scheduleShow(event?: Event): void {\n    instance.clearDelayTimeouts();\n\n    if (event) {\n      invokeHook('onTrigger', [instance, event]);\n    }\n\n    addDocumentPress();\n\n    let delay = getDelay(true);\n    const [touchValue, touchDelay] = getNormalizedTouchSettings();\n\n    if (currentInput.isTouch && touchValue === 'hold' && touchDelay) {\n      delay = touchDelay;\n    }\n\n    if (delay) {\n      showTimeout = setTimeout(() => {\n        instance.show();\n      }, delay);\n    } else {\n      instance.show();\n    }\n  }\n\n  function scheduleHide(event: Event): void {\n    instance.clearDelayTimeouts();\n\n    invokeHook('onUntrigger', [instance, event]);\n\n    if (!instance.state.isVisible) {\n      removeDocumentPress();\n\n      return;\n    }\n\n    // For interactive tippies, scheduleHide is added to a document.body handler\n    // from onMouseLeave so must intercept scheduled hides from mousemove/leave\n    // events when trigger contains mouseenter and click, and the tip is\n    // currently shown as a result of a click.\n    if (\n      instance.props.trigger.indexOf('mouseenter') >= 0 &&\n      instance.props.trigger.indexOf('click') >= 0 &&\n      ['mouseleave', 'mousemove'].indexOf(event.type) >= 0 &&\n      isVisibleFromClick\n    ) {\n      return;\n    }\n\n    const delay = getDelay(false);\n\n    if (delay) {\n      hideTimeout = setTimeout(() => {\n        if (instance.state.isVisible) {\n          instance.hide();\n        }\n      }, delay);\n    } else {\n      // Fixes a `transitionend` problem when it fires 1 frame too\n      // late sometimes, we don't want hide() to be called.\n      scheduleHideAnimationFrame = requestAnimationFrame(() => {\n        instance.hide();\n      });\n    }\n  }\n\n  // ===========================================================================\n  // 🔑 Public methods\n  // ===========================================================================\n  function enable(): void {\n    instance.state.isEnabled = true;\n  }\n\n  function disable(): void {\n    // Disabling the instance should also hide it\n    // https://github.com/atomiks/tippy.js-react/issues/106\n    instance.hide();\n    instance.state.isEnabled = false;\n  }\n\n  function clearDelayTimeouts(): void {\n    clearTimeout(showTimeout);\n    clearTimeout(hideTimeout);\n    cancelAnimationFrame(scheduleHideAnimationFrame);\n  }\n\n  function setProps(partialProps: Partial<Props>): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('setProps'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    invokeHook('onBeforeUpdate', [instance, partialProps]);\n\n    removeListeners();\n\n    const prevProps = instance.props;\n    const nextProps = evaluateProps(reference, {\n      ...instance.props,\n      ...partialProps,\n      ignoreAttributes: true,\n    });\n\n    instance.props = nextProps;\n\n    addListeners();\n\n    if (prevProps.interactiveDebounce !== nextProps.interactiveDebounce) {\n      cleanupInteractiveMouseListeners();\n      debouncedOnMouseMove = debounce(\n        onMouseMove,\n        nextProps.interactiveDebounce\n      );\n    }\n\n    // Ensure stale aria-expanded attributes are removed\n    if (prevProps.triggerTarget && !nextProps.triggerTarget) {\n      normalizeToArray(prevProps.triggerTarget).forEach((node) => {\n        node.removeAttribute('aria-expanded');\n      });\n    } else if (nextProps.triggerTarget) {\n      reference.removeAttribute('aria-expanded');\n    }\n\n    handleAriaExpandedAttribute();\n    handleStyles();\n\n    if (onUpdate) {\n      onUpdate(prevProps, nextProps);\n    }\n\n    if (instance.popperInstance) {\n      createPopperInstance();\n\n      // Fixes an issue with nested tippies if they are all getting re-rendered,\n      // and the nested ones get re-rendered first.\n      // https://github.com/atomiks/tippyjs-react/issues/177\n      // TODO: find a cleaner / more efficient solution(!)\n      getNestedPopperTree().forEach((nestedPopper) => {\n        // React (and other UI libs likely) requires a rAF wrapper as it flushes\n        // its work in one\n        requestAnimationFrame(nestedPopper._tippy!.popperInstance!.forceUpdate);\n      });\n    }\n\n    invokeHook('onAfterUpdate', [instance, partialProps]);\n  }\n\n  function setContent(content: Content): void {\n    instance.setProps({content});\n  }\n\n  function show(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('show'));\n    }\n\n    // Early bail-out\n    const isAlreadyVisible = instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const isTouchAndTouchDisabled =\n      currentInput.isTouch && !instance.props.touch;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      0,\n      defaultProps.duration\n    );\n\n    if (\n      isAlreadyVisible ||\n      isDestroyed ||\n      isDisabled ||\n      isTouchAndTouchDisabled\n    ) {\n      return;\n    }\n\n    // Normalize `disabled` behavior across browsers.\n    // Firefox allows events on disabled elements, but Chrome doesn't.\n    // Using a wrapper element (i.e. <span>) is recommended.\n    if (getCurrentTarget().hasAttribute('disabled')) {\n      return;\n    }\n\n    invokeHook('onShow', [instance], false);\n    if (instance.props.onShow(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = true;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'visible';\n    }\n\n    handleStyles();\n    addDocumentPress();\n\n    if (!instance.state.isMounted) {\n      popper.style.transition = 'none';\n    }\n\n    // If flipping to the opposite side after hiding at least once, the\n    // animation will use the wrong placement without resetting the duration\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n      setTransitionDuration([box, content], 0);\n    }\n\n    onFirstUpdate = (): void => {\n      if (!instance.state.isVisible || ignoreOnFirstUpdate) {\n        return;\n      }\n\n      ignoreOnFirstUpdate = true;\n\n      // reflow\n      void popper.offsetHeight;\n\n      popper.style.transition = instance.props.moveTransition;\n\n      if (getIsDefaultRenderFn() && instance.props.animation) {\n        const {box, content} = getDefaultTemplateChildren();\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'visible');\n      }\n\n      handleAriaContentAttribute();\n      handleAriaExpandedAttribute();\n\n      pushIfUnique(mountedInstances, instance);\n\n      instance.state.isMounted = true;\n      invokeHook('onMount', [instance]);\n\n      if (instance.props.animation && getIsDefaultRenderFn()) {\n        onTransitionedIn(duration, () => {\n          instance.state.isShown = true;\n          invokeHook('onShown', [instance]);\n        });\n      }\n    };\n\n    mount();\n  }\n\n  function hide(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hide'));\n    }\n\n    // Early bail-out\n    const isAlreadyHidden = !instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      1,\n      defaultProps.duration\n    );\n\n    if (isAlreadyHidden || isDestroyed || isDisabled) {\n      return;\n    }\n\n    invokeHook('onHide', [instance], false);\n    if (instance.props.onHide(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = false;\n    instance.state.isShown = false;\n    ignoreOnFirstUpdate = false;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'hidden';\n    }\n\n    cleanupInteractiveMouseListeners();\n    removeDocumentPress();\n    handleStyles();\n\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n\n      if (instance.props.animation) {\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'hidden');\n      }\n    }\n\n    handleAriaContentAttribute();\n    handleAriaExpandedAttribute();\n\n    if (instance.props.animation) {\n      if (getIsDefaultRenderFn()) {\n        onTransitionedOut(duration, instance.unmount);\n      }\n    } else {\n      instance.unmount();\n    }\n  }\n\n  function hideWithInteractivity(event: MouseEvent): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(\n        instance.state.isDestroyed,\n        createMemoryLeakWarning('hideWithInteractivity')\n      );\n    }\n\n    doc.addEventListener('mousemove', debouncedOnMouseMove);\n    pushIfUnique(mouseMoveListeners, debouncedOnMouseMove);\n    debouncedOnMouseMove(event);\n  }\n\n  function unmount(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('unmount'));\n    }\n\n    if (instance.state.isVisible) {\n      instance.hide();\n    }\n\n    if (!instance.state.isMounted) {\n      return;\n    }\n\n    destroyPopperInstance();\n\n    // If a popper is not interactive, it will be appended outside the popper\n    // tree by default. This seems mainly for interactive tippies, but we should\n    // find a workaround if possible\n    getNestedPopperTree().forEach((nestedPopper) => {\n      nestedPopper._tippy!.unmount();\n    });\n\n    if (popper.parentNode) {\n      popper.parentNode.removeChild(popper);\n    }\n\n    mountedInstances = mountedInstances.filter((i) => i !== instance);\n\n    instance.state.isMounted = false;\n    invokeHook('onHidden', [instance]);\n  }\n\n  function destroy(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('destroy'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    instance.clearDelayTimeouts();\n    instance.unmount();\n\n    removeListeners();\n\n    delete reference._tippy;\n\n    instance.state.isDestroyed = true;\n\n    invokeHook('onDestroy', [instance]);\n  }\n}\n", "import bindGlobalEventListeners, {\n  currentInput,\n} from './bindGlobalEventListeners';\nimport createTippy, {mountedInstances} from './createTippy';\nimport {getArrayOfElements, isElement, isReferenceElement} from './dom-utils';\nimport {defaultProps, setDefaultProps, validateProps} from './props';\nimport {HideAll, HideAllOptions, Instance, Props, Targets} from './types';\nimport {validateTargets, warnWhen} from './validation';\n\nfunction tippy(\n  targets: Targets,\n  optionalProps: Partial<Props> = {}\n): Instance | Instance[] {\n  const plugins = defaultProps.plugins.concat(optionalProps.plugins || []);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateTargets(targets);\n    validateProps(optionalProps, plugins);\n  }\n\n  bindGlobalEventListeners();\n\n  const passedProps: Partial<Props> = {...optionalProps, plugins};\n\n  const elements = getArrayOfElements(targets);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    const isSingleContentElement = isElement(passedProps.content);\n    const isMoreThanOneReferenceElement = elements.length > 1;\n    warnWhen(\n      isSingleContentElement && isMoreThanOneReferenceElement,\n      [\n        'tippy() was passed an Element as the `content` prop, but more than',\n        'one tippy instance was created by this invocation. This means the',\n        'content element will only be appended to the last tippy instance.',\n        '\\n\\n',\n        'Instead, pass the .innerHTML of the element, or use a function that',\n        'returns a cloned version of the element instead.',\n        '\\n\\n',\n        '1) content: element.innerHTML\\n',\n        '2) content: () => element.cloneNode(true)',\n      ].join(' ')\n    );\n  }\n\n  const instances = elements.reduce<Instance[]>(\n    (acc, reference): Instance[] => {\n      const instance = reference && createTippy(reference, passedProps);\n\n      if (instance) {\n        acc.push(instance);\n      }\n\n      return acc;\n    },\n    []\n  );\n\n  return isElement(targets) ? instances[0] : instances;\n}\n\ntippy.defaultProps = defaultProps;\ntippy.setDefaultProps = setDefaultProps;\ntippy.currentInput = currentInput;\n\nexport default tippy;\n\nexport const hideAll: HideAll = ({\n  exclude: excludedReferenceOrInstance,\n  duration,\n}: HideAllOptions = {}) => {\n  mountedInstances.forEach((instance) => {\n    let isExcluded = false;\n\n    if (excludedReferenceOrInstance) {\n      isExcluded = isReferenceElement(excludedReferenceOrInstance)\n        ? instance.reference === excludedReferenceOrInstance\n        : instance.popper === (excludedReferenceOrInstance as Instance).popper;\n    }\n\n    if (!isExcluded) {\n      const originalDuration = instance.props.duration;\n\n      instance.setProps({duration});\n      instance.hide();\n\n      if (!instance.state.isDestroyed) {\n        instance.setProps({duration: originalDuration});\n      }\n    }\n  });\n};\n", "import tippy from '..';\nimport {div} from '../dom-utils';\nimport {\n  C<PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  CreateSingletonProps,\n  ReferenceElement,\n  CreateSingletonInstance,\n} from '../types';\nimport {removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\n\nconst createSingleton: CreateSingleton = (\n  tippyInstances,\n  optionalProps = {}\n) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !Array.isArray(tippyInstances),\n      [\n        'The first argument passed to createSingleton() must be an array of',\n        'tippy instances. The passed value was',\n        String(tippyInstances),\n      ].join(' ')\n    );\n  }\n\n  let mutTippyInstances = tippyInstances;\n  let references: Array<ReferenceElement> = [];\n  let currentTarget: Element;\n  let overrides = optionalProps.overrides;\n\n  function setReferences(): void {\n    references = mutTippyInstances.map((instance) => instance.reference);\n  }\n\n  function enableInstances(isEnabled: boolean): void {\n    mutTippyInstances.forEach((instance) => {\n      if (isEnabled) {\n        instance.enable();\n      } else {\n        instance.disable();\n      }\n    });\n  }\n\n  enableInstances(false);\n  setReferences();\n\n  const singleton: Plugin = {\n    fn() {\n      return {\n        onDestroy(): void {\n          enableInstances(true);\n        },\n        onTrigger(instance, event): void {\n          const target = event.currentTarget as Element;\n          const index = references.indexOf(target);\n\n          // bail-out\n          if (target === currentTarget) {\n            return;\n          }\n\n          currentTarget = target;\n\n          const overrideProps = (overrides || [])\n            .concat('content')\n            .reduce((acc, prop) => {\n              (acc as any)[prop] = mutTippyInstances[index].props[prop];\n              return acc;\n            }, {});\n\n          instance.setProps({\n            ...overrideProps,\n            getReferenceClientRect: () => target.getBoundingClientRect(),\n          });\n        },\n      };\n    },\n  };\n\n  const instance = tippy(div(), {\n    ...removeProperties(optionalProps, ['overrides']),\n    plugins: [singleton, ...(optionalProps.plugins || [])],\n    triggerTarget: references,\n  }) as CreateSingletonInstance<CreateSingletonProps>;\n\n  const originalSetProps = instance.setProps;\n\n  instance.setProps = (props): void => {\n    overrides = props.overrides || overrides;\n    originalSetProps(props);\n  };\n\n  instance.setInstances = (nextInstances): void => {\n    enableInstances(true);\n\n    mutTippyInstances = nextInstances;\n\n    enableInstances(false);\n    setReferences();\n\n    instance.setProps({triggerTarget: references});\n  };\n\n  return instance;\n};\n\nexport default createSingleton;\n", "import tippy from '..';\nimport {defaultProps} from '../props';\nimport {Instance, Props, Targets} from '../types';\nimport {ListenerObject} from '../types-internal';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\n\nconst BUBBLING_EVENTS_MAP = {\n  mouseover: 'mouseenter',\n  focusin: 'focus',\n  click: 'click',\n};\n\n/**\n * Creates a delegate instance that controls the creation of tippy instances\n * for child elements (`target` CSS selector).\n */\nfunction delegate(\n  targets: Targets,\n  props: Partial<Props> & {target: string}\n): Instance | Instance[] {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !(props && props.target),\n      [\n        'You must specity a `target` prop indicating a CSS selector string matching',\n        'the target elements that should receive a tippy.',\n      ].join(' ')\n    );\n  }\n\n  let listeners: ListenerObject[] = [];\n  let childTippyInstances: Instance[] = [];\n\n  const {target} = props;\n\n  const nativeProps = removeProperties(props, ['target']);\n  const parentProps = {...nativeProps, trigger: 'manual', touch: false};\n  const childProps = {...nativeProps, showOnCreate: true};\n\n  const returnValue = tippy(targets, parentProps);\n  const normalizedReturnValue = normalizeToArray(returnValue);\n\n  function onTrigger(event: Event): void {\n    if (!event.target) {\n      return;\n    }\n\n    const targetNode = (event.target as Element).closest(target);\n\n    if (!targetNode) {\n      return;\n    }\n\n    // Get relevant trigger with fallbacks:\n    // 1. Check `data-tippy-trigger` attribute on target node\n    // 2. Fallback to `trigger` passed to `delegate()`\n    // 3. Fallback to `defaultProps.trigger`\n    const trigger =\n      targetNode.getAttribute('data-tippy-trigger') ||\n      props.trigger ||\n      defaultProps.trigger;\n\n    // @ts-ignore\n    if (targetNode._tippy) {\n      return;\n    }\n\n    if (event.type === 'touchstart' && typeof childProps.touch === 'boolean') {\n      return;\n    }\n\n    if (\n      event.type !== 'touchstart' &&\n      trigger.indexOf((BUBBLING_EVENTS_MAP as any)[event.type])\n    ) {\n      return;\n    }\n\n    const instance = tippy(targetNode, childProps);\n\n    if (instance) {\n      childTippyInstances = childTippyInstances.concat(instance);\n    }\n  }\n\n  function on(\n    node: Element,\n    eventType: string,\n    handler: EventListener,\n    options: object | boolean = false\n  ): void {\n    node.addEventListener(eventType, handler, options);\n    listeners.push({node, eventType, handler, options});\n  }\n\n  function addEventListeners(instance: Instance): void {\n    const {reference} = instance;\n\n    on(reference, 'touchstart', onTrigger);\n    on(reference, 'mouseover', onTrigger);\n    on(reference, 'focusin', onTrigger);\n    on(reference, 'click', onTrigger);\n  }\n\n  function removeEventListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function applyMutations(instance: Instance): void {\n    const originalDestroy = instance.destroy;\n    instance.destroy = (shouldDestroyChildInstances = true): void => {\n      if (shouldDestroyChildInstances) {\n        childTippyInstances.forEach((instance) => {\n          instance.destroy();\n        });\n      }\n\n      childTippyInstances = [];\n\n      removeEventListeners();\n      originalDestroy();\n    };\n\n    addEventListeners(instance);\n  }\n\n  normalizedReturnValue.forEach(applyMutations);\n\n  return returnValue;\n}\n\nexport default delegate;\n", "import {BACKDROP_CLASS} from '../constants';\nimport {div, setVisibilityState} from '../dom-utils';\nimport {getChildren} from '../template';\nimport {AnimateFill} from '../types';\nimport {errorWhen} from '../validation';\n\nconst animateFill: AnimateFill = {\n  name: 'animateFill',\n  defaultValue: false,\n  fn(instance) {\n    // @ts-ignore\n    if (!instance.props.render?.$$tippy) {\n      if (__DEV__) {\n        errorWhen(\n          instance.props.animateFill,\n          'The `animateFill` plugin requires the default render function.'\n        );\n      }\n\n      return {};\n    }\n\n    const {box, content} = getChildren(instance.popper);\n\n    const backdrop = instance.props.animateFill\n      ? createBackdropElement()\n      : null;\n\n    return {\n      onCreate(): void {\n        if (backdrop) {\n          box.insertBefore(backdrop, box.firstElementChild!);\n          box.setAttribute('data-animatefill', '');\n          box.style.overflow = 'hidden';\n\n          instance.setProps({arrow: false, animation: 'shift-away'});\n        }\n      },\n      onMount(): void {\n        if (backdrop) {\n          const {transitionDuration} = box.style;\n          const duration = Number(transitionDuration.replace('ms', ''));\n\n          // The content should fade in after the backdrop has mostly filled the\n          // tooltip element. `clip-path` is the other alternative but is not\n          // well-supported and is buggy on some devices.\n          content.style.transitionDelay = `${Math.round(duration / 10)}ms`;\n\n          backdrop.style.transitionDuration = transitionDuration;\n          setVisibilityState([backdrop], 'visible');\n        }\n      },\n      onShow(): void {\n        if (backdrop) {\n          backdrop.style.transitionDuration = '0ms';\n        }\n      },\n      onHide(): void {\n        if (backdrop) {\n          setVisibilityState([backdrop], 'hidden');\n        }\n      },\n    };\n  },\n};\n\nexport default animateFill;\n\nfunction createBackdropElement(): HTMLDivElement {\n  const backdrop = div();\n  backdrop.className = BACKDROP_CLASS;\n  setVisibilityState([backdrop], 'hidden');\n  return backdrop;\n}\n", "import {getOwnerDocument} from '../dom-utils';\nimport {FollowCursor, Instance} from '../types';\n\nlet mouseCoords = {clientX: 0, clientY: 0};\nlet activeInstances: Array<{instance: Instance; doc: Document}> = [];\n\nfunction storeMouseCoords({clientX, clientY}: MouseEvent): void {\n  mouseCoords = {clientX, clientY};\n}\n\nfunction addMouseCoordsListener(doc: Document): void {\n  doc.addEventListener('mousemove', storeMouseCoords);\n}\n\nfunction removeMouseCoordsListener(doc: Document): void {\n  doc.removeEventListener('mousemove', storeMouseCoords);\n}\n\nconst followCursor: FollowCursor = {\n  name: 'followCursor',\n  defaultValue: false,\n  fn(instance) {\n    const reference = instance.reference;\n    const doc = getOwnerDocument(instance.props.triggerTarget || reference);\n\n    let isInternalUpdate = false;\n    let wasFocusEvent = false;\n    let isUnmounted = true;\n    let prevProps = instance.props;\n\n    function getIsInitialBehavior(): boolean {\n      return (\n        instance.props.followCursor === 'initial' && instance.state.isVisible\n      );\n    }\n\n    function addListener(): void {\n      doc.addEventListener('mousemove', onMouseMove);\n    }\n\n    function removeListener(): void {\n      doc.removeEventListener('mousemove', onMouseMove);\n    }\n\n    function unsetGetReferenceClientRect(): void {\n      isInternalUpdate = true;\n      instance.setProps({getReferenceClientRect: null});\n      isInternalUpdate = false;\n    }\n\n    function onMouseMove(event: MouseEvent): void {\n      // If the instance is interactive, avoid updating the position unless it's\n      // over the reference element\n      const isCursorOverReference = event.target\n        ? reference.contains(event.target as Node)\n        : true;\n      const {followCursor} = instance.props;\n      const {clientX, clientY} = event;\n\n      const rect = reference.getBoundingClientRect();\n      const relativeX = clientX - rect.left;\n      const relativeY = clientY - rect.top;\n\n      if (isCursorOverReference || !instance.props.interactive) {\n        instance.setProps({\n          getReferenceClientRect() {\n            const rect = reference.getBoundingClientRect();\n\n            let x = clientX;\n            let y = clientY;\n\n            if (followCursor === 'initial') {\n              x = rect.left + relativeX;\n              y = rect.top + relativeY;\n            }\n\n            const top = followCursor === 'horizontal' ? rect.top : y;\n            const right = followCursor === 'vertical' ? rect.right : x;\n            const bottom = followCursor === 'horizontal' ? rect.bottom : y;\n            const left = followCursor === 'vertical' ? rect.left : x;\n\n            return {\n              width: right - left,\n              height: bottom - top,\n              top,\n              right,\n              bottom,\n              left,\n            };\n          },\n        });\n      }\n    }\n\n    function create(): void {\n      if (instance.props.followCursor) {\n        activeInstances.push({instance, doc});\n        addMouseCoordsListener(doc);\n      }\n    }\n\n    function destroy(): void {\n      activeInstances = activeInstances.filter(\n        (data) => data.instance !== instance\n      );\n\n      if (activeInstances.filter((data) => data.doc === doc).length === 0) {\n        removeMouseCoordsListener(doc);\n      }\n    }\n\n    return {\n      onCreate: create,\n      onDestroy: destroy,\n      onBeforeUpdate(): void {\n        prevProps = instance.props;\n      },\n      onAfterUpdate(_, {followCursor}): void {\n        if (isInternalUpdate) {\n          return;\n        }\n\n        if (\n          followCursor !== undefined &&\n          prevProps.followCursor !== followCursor\n        ) {\n          destroy();\n\n          if (followCursor) {\n            create();\n\n            if (\n              instance.state.isMounted &&\n              !wasFocusEvent &&\n              !getIsInitialBehavior()\n            ) {\n              addListener();\n            }\n          } else {\n            removeListener();\n            unsetGetReferenceClientRect();\n          }\n        }\n      },\n      onMount(): void {\n        if (instance.props.followCursor) {\n          if (isUnmounted) {\n            onMouseMove(mouseCoords as MouseEvent);\n            isUnmounted = false;\n          }\n\n          if (!wasFocusEvent && !getIsInitialBehavior()) {\n            addListener();\n          }\n        }\n      },\n      onTrigger(_, {type}): void {\n        wasFocusEvent = type === 'focus';\n      },\n      onHidden(): void {\n        if (instance.props.followCursor) {\n          unsetGetReferenceClientRect();\n          removeListener();\n          isUnmounted = true;\n        }\n      },\n    };\n  },\n};\n\nexport default followCursor;\n", "import {Modifier, Placement} from '@popperjs/core';\nimport {isMouseEvent} from '../dom-utils';\nimport {BasePlacement, InlinePositioning, Props} from '../types';\nimport {arrayFrom, getBasePlacement} from '../utils';\n\nfunction getProps(props: Props, modifier: Modifier<any, any>): Partial<Props> {\n  return {\n    popperOptions: {\n      ...props.popperOptions,\n      modifiers: [\n        ...(props.popperOptions?.modifiers || []).filter(\n          ({name}) => name !== modifier.name\n        ),\n        modifier,\n      ],\n    },\n  };\n}\n\nconst inlinePositioning: InlinePositioning = {\n  name: 'inlinePositioning',\n  defaultValue: false,\n  fn(instance) {\n    const {reference} = instance;\n\n    function isEnabled(): boolean {\n      return !!instance.props.inlinePositioning;\n    }\n\n    let placement: Placement;\n    let cursorRectIndex = -1;\n    let isInternalUpdate = false;\n\n    const modifier: Modifier<'tippyInlinePositioning', {}> = {\n      name: 'tippyInlinePositioning',\n      enabled: true,\n      phase: 'afterWrite',\n      fn({state}) {\n        if (isEnabled()) {\n          if (placement !== state.placement) {\n            instance.setProps({\n              getReferenceClientRect: () =>\n                getReferenceClientRect(state.placement),\n            });\n          }\n\n          placement = state.placement;\n        }\n      },\n    };\n\n    function getReferenceClientRect(placement: Placement): ClientRect {\n      return getInlineBoundingClientRect(\n        getBasePlacement(placement),\n        reference.getBoundingClientRect(),\n        arrayFrom(reference.getClientRects()),\n        cursorRectIndex\n      );\n    }\n\n    function setInternalProps(partialProps: Partial<Props>): void {\n      isInternalUpdate = true;\n      instance.setProps(partialProps);\n      isInternalUpdate = false;\n    }\n\n    function addModifier(): void {\n      if (!isInternalUpdate) {\n        setInternalProps(getProps(instance.props, modifier));\n      }\n    }\n\n    return {\n      onCreate: addModifier,\n      onAfterUpdate: addModifier,\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          const rects = arrayFrom(instance.reference.getClientRects());\n          const cursorRect = rects.find(\n            (rect) =>\n              rect.left - 2 <= event.clientX &&\n              rect.right + 2 >= event.clientX &&\n              rect.top - 2 <= event.clientY &&\n              rect.bottom + 2 >= event.clientY\n          );\n\n          cursorRectIndex = rects.indexOf(cursorRect);\n        }\n      },\n      onUntrigger(): void {\n        cursorRectIndex = -1;\n      },\n    };\n  },\n};\n\nexport default inlinePositioning;\n\nexport function getInlineBoundingClientRect(\n  currentBasePlacement: BasePlacement | null,\n  boundingRect: ClientRect,\n  clientRects: ClientRect[],\n  cursorRectIndex: number\n): ClientRect {\n  // Not an inline element, or placement is not yet known\n  if (clientRects.length < 2 || currentBasePlacement === null) {\n    return boundingRect;\n  }\n\n  // There are two rects and they are disjoined\n  if (\n    clientRects.length === 2 &&\n    cursorRectIndex >= 0 &&\n    clientRects[0].left > clientRects[1].right\n  ) {\n    return clientRects[cursorRectIndex] || boundingRect;\n  }\n\n  switch (currentBasePlacement) {\n    case 'top':\n    case 'bottom': {\n      const firstRect = clientRects[0];\n      const lastRect = clientRects[clientRects.length - 1];\n      const isTop = currentBasePlacement === 'top';\n\n      const top = firstRect.top;\n      const bottom = lastRect.bottom;\n      const left = isTop ? firstRect.left : lastRect.left;\n      const right = isTop ? firstRect.right : lastRect.right;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    case 'left':\n    case 'right': {\n      const minLeft = Math.min(...clientRects.map((rects) => rects.left));\n      const maxRight = Math.max(...clientRects.map((rects) => rects.right));\n      const measureRects = clientRects.filter((rect) =>\n        currentBasePlacement === 'left'\n          ? rect.left === minLeft\n          : rect.right === maxRight\n      );\n\n      const top = measureRects[0].top;\n      const bottom = measureRects[measureRects.length - 1].bottom;\n      const left = minLeft;\n      const right = maxRight;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    default: {\n      return boundingRect;\n    }\n  }\n}\n", "import {VirtualElement} from '@popperjs/core';\nimport {ReferenceElement, Sticky} from '../types';\n\nconst sticky: Sticky = {\n  name: 'sticky',\n  defaultValue: false,\n  fn(instance) {\n    const {reference, popper} = instance;\n\n    function getReference(): ReferenceElement | VirtualElement {\n      return instance.popperInstance\n        ? instance.popperInstance.state.elements.reference\n        : reference;\n    }\n\n    function shouldCheck(value: 'reference' | 'popper'): boolean {\n      return instance.props.sticky === true || instance.props.sticky === value;\n    }\n\n    let prevRefRect: ClientRect | null = null;\n    let prevPopRect: ClientRect | null = null;\n\n    function updatePosition(): void {\n      const currentRefRect = shouldCheck('reference')\n        ? getReference().getBoundingClientRect()\n        : null;\n      const currentPopRect = shouldCheck('popper')\n        ? popper.getBoundingClientRect()\n        : null;\n\n      if (\n        (currentRefRect && areRectsDifferent(prevRefRect, currentRefRect)) ||\n        (currentPopRect && areRectsDifferent(prevPopRect, currentPopRect))\n      ) {\n        if (instance.popperInstance) {\n          instance.popperInstance.update();\n        }\n      }\n\n      prevRefRect = currentRefRect;\n      prevPopRect = currentPopRect;\n\n      if (instance.state.isMounted) {\n        requestAnimationFrame(updatePosition);\n      }\n    }\n\n    return {\n      onMount(): void {\n        if (instance.props.sticky) {\n          updatePosition();\n        }\n      },\n    };\n  },\n};\n\nexport default sticky;\n\nfunction areRectsDifferent(\n  rectA: ClientRect | null,\n  rectB: ClientRect | null\n): boolean {\n  if (rectA && rectB) {\n    return (\n      rectA.top !== rectB.top ||\n      rectA.right !== rectB.right ||\n      rectA.bottom !== rectB.bottom ||\n      rectA.left !== rectB.left\n    );\n  }\n\n  return true;\n}\n", "import css from '../dist/tippy.css';\nimport {injectCSS} from '../src/css';\nimport {isBrowser} from '../src/browser';\nimport tippy, {hideAll} from '../src';\nimport createSingleton from '../src/addons/createSingleton';\nimport delegate from '../src/addons/delegate';\nimport animateFill from '../src/plugins/animateFill';\nimport followCursor from '../src/plugins/followCursor';\nimport inlinePositioning from '../src/plugins/inlinePositioning';\nimport sticky from '../src/plugins/sticky';\nimport {ROUND_ARROW} from '../src/constants';\nimport {render} from '../src/template';\n\nif (isBrowser) {\n  injectCSS(css);\n}\n\ntippy.setDefaultProps({\n  plugins: [animateFill, followCursor, inlinePositioning, sticky],\n  render,\n});\n\ntippy.createSingleton = createSingleton;\ntippy.delegate = delegate;\ntippy.hideAll = hideAll;\ntippy.roundArrow = ROUND_ARROW;\n\nexport default tippy;\n"], "names": ["injectCSS", "css", "style", "document", "createElement", "textContent", "setAttribute", "head", "firstStyleOrLinkTag", "querySelector", "insertBefore", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "window", "ua", "navigator", "userAgent", "isIE", "test", "ROUND_ARROW", "BOX_CLASS", "CONTENT_CLASS", "BACKDROP_CLASS", "ARROW_CLASS", "SVG_ARROW_CLASS", "TOUCH_OPTIONS", "passive", "capture", "hasOwnProperty", "obj", "key", "call", "getValueAtIndexOrReturn", "value", "index", "defaultValue", "Array", "isArray", "v", "isType", "type", "str", "toString", "indexOf", "invokeWithArgsOrReturn", "args", "debounce", "fn", "ms", "timeout", "arg", "clearTimeout", "setTimeout", "removeProperties", "keys", "clone", "for<PERSON>ach", "splitBySpaces", "split", "filter", "Boolean", "normalizeToArray", "concat", "pushIfUnique", "arr", "push", "unique", "item", "getBasePlacement", "placement", "arrayFrom", "slice", "removeUndefinedProps", "Object", "reduce", "acc", "undefined", "div", "isElement", "some", "isNodeList", "isMouseEvent", "isReferenceElement", "_tippy", "reference", "getArrayOfElements", "querySelectorAll", "setTransitionDuration", "els", "el", "transitionDuration", "setVisibilityState", "state", "getOwnerDocument", "elementOrElements", "element", "ownerDocument", "isCursorOutsideInteractiveBorder", "popperTreeData", "event", "clientX", "clientY", "every", "popperRect", "popperState", "props", "interactiveBorder", "basePlacement", "offsetData", "modifiersData", "offset", "topDistance", "top", "y", "bottomDistance", "bottom", "leftDistance", "left", "x", "rightDistance", "right", "exceedsTop", "exceedsBottom", "exceedsLeft", "exceedsRight", "updateTransitionEndListener", "box", "action", "listener", "method", "currentInput", "is<PERSON><PERSON>ch", "lastMouseMoveTime", "onDocumentTouchStart", "performance", "addEventListener", "onDocumentMouseMove", "now", "removeEventListener", "onWindowBlur", "activeElement", "instance", "blur", "isVisible", "bindGlobalEventListeners", "createMemoryLeakWarning", "txt", "join", "clean", "spacesAndTabs", "lineStartWithSpaces", "replace", "trim", "getDevMessage", "message", "getFormattedMessage", "visitedMessages", "resetVisitedMessages", "Set", "warn<PERSON><PERSON>", "condition", "has", "add", "console", "warn", "<PERSON><PERSON><PERSON>", "error", "validateTargets", "targets", "didPassFalsyValue", "didPassPlainObject", "prototype", "String", "pluginProps", "animateFill", "followCursor", "inlinePositioning", "sticky", "renderProps", "allowHTML", "animation", "arrow", "content", "inertia", "max<PERSON><PERSON><PERSON>", "role", "theme", "zIndex", "defaultProps", "appendTo", "body", "aria", "expanded", "delay", "duration", "getReferenceClientRect", "hideOnClick", "ignoreAttributes", "interactive", "interactiveDebounce", "moveTransition", "onAfterUpdate", "onBeforeUpdate", "onCreate", "onDestroy", "onHidden", "onHide", "onMount", "onShow", "onShown", "onTrigger", "onUntrigger", "onClickOutside", "plugins", "popperOptions", "render", "showOnCreate", "touch", "trigger", "triggerTarget", "defaultKeys", "setDefaultProps", "partialProps", "validateProps", "getExtendedPassedProps", "passedProps", "plugin", "name", "getDataAttributeProps", "propKeys", "valueAsString", "getAttribute", "JSON", "parse", "e", "evaluateProps", "out", "prop", "nonPluginProps", "didPassUnknownProp", "length", "innerHTML", "dangerouslySetInnerHTML", "html", "createArrowElement", "className", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "popper", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxChildren", "children", "find", "node", "classList", "contains", "backdrop", "onUpdate", "prevProps", "nextProps", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "$$tippy", "idCounter", "mouseMoveListeners", "mountedInstances", "createTippy", "showTimeout", "hideTimeout", "scheduleHideAnimationFrame", "isVisibleFromClick", "didHideDueToDocumentMouseDown", "didTouchMove", "ignoreOnFirstUpdate", "lastTriggerEvent", "currentTransitionEndListener", "onFirstUpdate", "listeners", "debouncedOnMouseMove", "onMouseMove", "currentTarget", "doc", "id", "popperInstance", "isEnabled", "isDestroyed", "isMounted", "isShown", "clearDelayTimeouts", "setProps", "show", "hide", "hideWithInteractivity", "enable", "disable", "unmount", "destroy", "pluginsHooks", "map", "hasAriaExpanded", "hasAttribute", "addListeners", "handleAriaExpandedAttribute", "handleStyles", "invokeHook", "scheduleShow", "getNormalizedTouchSettings", "getIsCustomTouchBehavior", "getIsDefaultRenderFn", "getC<PERSON>rentTarget", "getDefaultTemplateChildren", "get<PERSON>elay", "isShow", "pointerEvents", "hook", "shouldInvokePropsHook", "pluginHooks", "handleAriaContentAttribute", "attr", "nodes", "currentValue", "nextValue", "cleanupInteractiveMouseListeners", "onDocumentPress", "target", "removeDocumentPress", "onTouchMove", "onTouchStart", "addDocumentPress", "onTransitionedOut", "callback", "onTransitionEnd", "parentNode", "onTransitionedIn", "on", "eventType", "handler", "options", "onMouseLeave", "onBlurOrFocusOut", "removeListeners", "shouldScheduleClickHide", "isEventListenerStopped", "wasFocused", "scheduleHide", "isCursorOverReferenceOrPopper", "getNestedPopperTree", "getBoundingClientRect", "shouldBail", "relatedTarget", "createPopperInstance", "destroyPopperInstance", "computedReference", "contextElement", "tippyModifier", "enabled", "phase", "requires", "attributes", "modifiers", "padding", "adaptive", "createPopper", "mount", "nextElement<PERSON><PERSON>ling", "touchValue", "touchDelay", "requestAnimationFrame", "cancelAnimationFrame", "nestedPopper", "forceUpdate", "isAlreadyVisible", "isDisabled", "isTouchAndTouchDisabled", "visibility", "transition", "offsetHeight", "isAlreadyHidden", "i", "tippy", "optionalProps", "elements", "isSingleContentElement", "isMoreThanOneReferenceElement", "instances", "hide<PERSON>ll", "excludedReferenceOrInstance", "exclude", "isExcluded", "originalDuration", "createSingleton", "tippyInstances", "mutTippyInstances", "references", "overrides", "setReferences", "enableInstances", "singleton", "overrideProps", "originalSetProps", "setInstances", "nextInstances", "BUBBLING_EVENTS_MAP", "mouseover", "focusin", "click", "delegate", "childTippyInstances", "nativeProps", "parentProps", "childProps", "returnValue", "normalizedReturnValue", "targetNode", "closest", "addEventListeners", "removeEventListeners", "applyMutations", "original<PERSON><PERSON>roy", "shouldDestroyChildInstances", "createBackdropElement", "overflow", "Number", "transitionDelay", "Math", "round", "mouseCoords", "activeInstances", "storeMouseCoords", "addMouseCoordsListener", "removeMouseCoordsListener", "isInternalUpdate", "wasFocusEvent", "isUnmounted", "getIsInitialBehavior", "addListener", "removeListener", "unsetGetReferenceClientRect", "isCursorOverReference", "rect", "relativeX", "relativeY", "width", "height", "create", "data", "_", "getProps", "modifier", "cursorRectIndex", "getInlineBoundingClientRect", "getClientRects", "setInternalProps", "addModifier", "rects", "cursorRect", "currentBasePlacement", "boundingRect", "clientRects", "firstRect", "lastRect", "isTop", "minLeft", "min", "maxRight", "max", "measureRects", "getReference", "<PERSON><PERSON><PERSON><PERSON>", "prevRefRect", "prevPopRect", "updatePosition", "currentRefRect", "currentPopRect", "areRectsDifferent", "update", "rectA", "rectB", "roundArrow"], "mappings": ";;;;;;;;;;;;;EAAO,SAASA,SAAT,CAAmBC,GAAnB,EAAsC;EAC3C,MAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAT,CAAuB,OAAvB,CAAd;EACAF,EAAAA,KAAK,CAACG,WAAN,GAAoBJ,GAApB;EACAC,EAAAA,KAAK,CAACI,YAAN,CAAmB,uBAAnB,EAA2D,EAA3D;EACA,MAAMC,IAAI,GAAGJ,QAAQ,CAACI,IAAtB;EACA,MAAMC,mBAAmB,GAAGL,QAAQ,CAACM,aAAT,CAAuB,sBAAvB,CAA5B;;EAEA,MAAID,mBAAJ,EAAyB;EACvBD,IAAAA,IAAI,CAACG,YAAL,CAAkBR,KAAlB,EAAyBM,mBAAzB;EACD,GAFD,MAEO;EACLD,IAAAA,IAAI,CAACI,WAAL,CAAiBT,KAAjB;EACD;EACF;;ECZM,IAAMU,SAAS,GACpB,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,OAAOV,QAAP,KAAoB,WADhD;EAGP,IAAMW,EAAE,GAAGF,SAAS,GAAGG,SAAS,CAACC,SAAb,GAAyB,EAA7C;AAEA,EAAO,IAAMC,IAAI,GAAG,kBAAkBC,IAAlB,CAAuBJ,EAAvB,CAAb;;ECLA,IAAMK,WAAW,GACtB,0LADK;AAGP,EAAO,IAAMC,SAAS,cAAf;AACP,EAAO,IAAMC,aAAa,kBAAnB;AACP,EAAO,IAAMC,cAAc,mBAApB;AACP,EAAO,IAAMC,WAAW,gBAAjB;AACP,EAAO,IAAMC,eAAe,oBAArB;AAEP,EAAO,IAAMC,aAAa,GAAG;EAACC,EAAAA,OAAO,EAAE,IAAV;EAAgBC,EAAAA,OAAO,EAAE;EAAzB,CAAtB;;ECPA,SAASC,cAAT,CAAwBC,GAAxB,EAAqCC,GAArC,EAA2D;EAChE,SAAO,GAAGF,cAAH,CAAkBG,IAAlB,CAAuBF,GAAvB,EAA4BC,GAA5B,CAAP;EACD;AAED,EAAO,SAASE,uBAAT,CACLC,KADK,EAELC,KAFK,EAGLC,YAHK,EAIF;EACH,MAAIC,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;EACxB,QAAMK,CAAC,GAAGL,KAAK,CAACC,KAAD,CAAf;EACA,WAAOI,CAAC,IAAI,IAAL,GACHF,KAAK,CAACC,OAAN,CAAcF,YAAd,IACEA,YAAY,CAACD,KAAD,CADd,GAEEC,YAHC,GAIHG,CAJJ;EAKD;;EAED,SAAOL,KAAP;EACD;AAED,EAAO,SAASM,MAAT,CAAgBN,KAAhB,EAA4BO,IAA5B,EAAmD;EACxD,MAAMC,GAAG,GAAG,GAAGC,QAAH,CAAYX,IAAZ,CAAiBE,KAAjB,CAAZ;EACA,SAAOQ,GAAG,CAACE,OAAJ,CAAY,SAAZ,MAA2B,CAA3B,IAAgCF,GAAG,CAACE,OAAJ,CAAeH,IAAf,UAA0B,CAAC,CAAlE;EACD;AAED,EAAO,SAASI,sBAAT,CAAgCX,KAAhC,EAA4CY,IAA5C,EAA8D;EACnE,SAAO,OAAOZ,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,MAAL,SAASY,IAAT,CAA9B,GAA+CZ,KAAtD;EACD;AAED,EAAO,SAASa,QAAT,CACLC,EADK,EAELC,EAFK,EAGa;EAClB;EACA,MAAIA,EAAE,KAAK,CAAX,EAAc;EACZ,WAAOD,EAAP;EACD;;EAED,MAAIE,OAAJ;EAEA,SAAO,UAACC,GAAD,EAAe;EACpBC,IAAAA,YAAY,CAACF,OAAD,CAAZ;EACAA,IAAAA,OAAO,GAAGG,UAAU,CAAC,YAAM;EACzBL,MAAAA,EAAE,CAACG,GAAD,CAAF;EACD,KAFmB,EAEjBF,EAFiB,CAApB;EAGD,GALD;EAMD;AAED,EAAO,SAASK,gBAAT,CAA6BxB,GAA7B,EAAqCyB,IAArC,EAAiE;EACtE,MAAMC,KAAK,qBAAO1B,GAAP,CAAX;EACAyB,EAAAA,IAAI,CAACE,OAAL,CAAa,UAAC1B,GAAD,EAAS;EACpB,WAAQyB,KAAD,CAAezB,GAAf,CAAP;EACD,GAFD;EAGA,SAAOyB,KAAP;EACD;AAED,EAAO,SAASE,aAAT,CAAuBxB,KAAvB,EAAgD;EACrD,SAAOA,KAAK,CAACyB,KAAN,CAAY,KAAZ,EAAmBC,MAAnB,CAA0BC,OAA1B,CAAP;EACD;AAED,EAAO,SAASC,gBAAT,CAA6B5B,KAA7B,EAAkD;EACvD,SAAQ,EAAD,CAAY6B,MAAZ,CAAmB7B,KAAnB,CAAP;EACD;AAED,EAAO,SAAS8B,YAAT,CAAyBC,GAAzB,EAAmC/B,KAAnC,EAAmD;EACxD,MAAI+B,GAAG,CAACrB,OAAJ,CAAYV,KAAZ,MAAuB,CAAC,CAA5B,EAA+B;EAC7B+B,IAAAA,GAAG,CAACC,IAAJ,CAAShC,KAAT;EACD;EACF;AAED,EAIO,SAASiC,MAAT,CAAmBF,GAAnB,EAAkC;EACvC,SAAOA,GAAG,CAACL,MAAJ,CAAW,UAACQ,IAAD,EAAOjC,KAAP;EAAA,WAAiB8B,GAAG,CAACrB,OAAJ,CAAYwB,IAAZ,MAAsBjC,KAAvC;EAAA,GAAX,CAAP;EACD;AAED,EAIO,SAASkC,gBAAT,CAA0BC,SAA1B,EAA+D;EACpE,SAAOA,SAAS,CAACX,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAP;EACD;AAED,EAAO,SAASY,SAAT,CAAmBrC,KAAnB,EAAiD;EACtD,SAAO,GAAGsC,KAAH,CAASxC,IAAT,CAAcE,KAAd,CAAP;EACD;AAED,EAAO,SAASuC,oBAAT,CACL3C,GADK,EAE6B;EAClC,SAAO4C,MAAM,CAACnB,IAAP,CAAYzB,GAAZ,EAAiB6C,MAAjB,CAAwB,UAACC,GAAD,EAAM7C,GAAN,EAAc;EAC3C,QAAID,GAAG,CAACC,GAAD,CAAH,KAAa8C,SAAjB,EAA4B;EACzBD,MAAAA,GAAD,CAAa7C,GAAb,IAAoBD,GAAG,CAACC,GAAD,CAAvB;EACD;;EAED,WAAO6C,GAAP;EACD,GANM,EAMJ,EANI,CAAP;EAOD;;ECnGM,SAASE,GAAT,GAA+B;EACpC,SAAO1E,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAP;EACD;AAED,EAAO,SAAS0E,SAAT,CAAmB7C,KAAnB,EAAwE;EAC7E,SAAO,CAAC,SAAD,EAAY,UAAZ,EAAwB8C,IAAxB,CAA6B,UAACvC,IAAD;EAAA,WAAUD,MAAM,CAACN,KAAD,EAAQO,IAAR,CAAhB;EAAA,GAA7B,CAAP;EACD;AAED,EAAO,SAASwC,UAAT,CAAoB/C,KAApB,EAAuD;EAC5D,SAAOM,MAAM,CAACN,KAAD,EAAQ,UAAR,CAAb;EACD;AAED,EAAO,SAASgD,YAAT,CAAsBhD,KAAtB,EAA2D;EAChE,SAAOM,MAAM,CAACN,KAAD,EAAQ,YAAR,CAAb;EACD;AAED,EAAO,SAASiD,kBAAT,CAA4BjD,KAA5B,EAAmE;EACxE,SAAO,CAAC,EAAEA,KAAK,IAAIA,KAAK,CAACkD,MAAf,IAAyBlD,KAAK,CAACkD,MAAN,CAAaC,SAAb,KAA2BnD,KAAtD,CAAR;EACD;AAED,EAAO,SAASoD,kBAAT,CAA4BpD,KAA5B,EAAuD;EAC5D,MAAI6C,SAAS,CAAC7C,KAAD,CAAb,EAAsB;EACpB,WAAO,CAACA,KAAD,CAAP;EACD;;EAED,MAAI+C,UAAU,CAAC/C,KAAD,CAAd,EAAuB;EACrB,WAAOqC,SAAS,CAACrC,KAAD,CAAhB;EACD;;EAED,MAAIG,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;EACxB,WAAOA,KAAP;EACD;;EAED,SAAOqC,SAAS,CAACnE,QAAQ,CAACmF,gBAAT,CAA0BrD,KAA1B,CAAD,CAAhB;EACD;AAED,EAAO,SAASsD,qBAAT,CACLC,GADK,EAELvD,KAFK,EAGC;EACNuD,EAAAA,GAAG,CAAChC,OAAJ,CAAY,UAACiC,EAAD,EAAQ;EAClB,QAAIA,EAAJ,EAAQ;EACNA,MAAAA,EAAE,CAACvF,KAAH,CAASwF,kBAAT,GAAiCzD,KAAjC;EACD;EACF,GAJD;EAKD;AAED,EAAO,SAAS0D,kBAAT,CACLH,GADK,EAELI,KAFK,EAGC;EACNJ,EAAAA,GAAG,CAAChC,OAAJ,CAAY,UAACiC,EAAD,EAAQ;EAClB,QAAIA,EAAJ,EAAQ;EACNA,MAAAA,EAAE,CAACnF,YAAH,CAAgB,YAAhB,EAA8BsF,KAA9B;EACD;EACF,GAJD;EAKD;AAED,EAAO,SAASC,gBAAT,CACLC,iBADK,EAEK;EAAA,0BACQjC,gBAAgB,CAACiC,iBAAD,CADxB;EAAA,MACHC,OADG;;EAEV,SAAOA,OAAO,GAAGA,OAAO,CAACC,aAAR,IAAyB7F,QAA5B,GAAuCA,QAArD;EACD;AAED,EAAO,SAAS8F,gCAAT,CACLC,cADK,EAELC,KAFK,EAGI;EAAA,MACFC,OADE,GACkBD,KADlB,CACFC,OADE;EAAA,MACOC,OADP,GACkBF,KADlB,CACOE,OADP;EAGT,SAAOH,cAAc,CAACI,KAAf,CAAqB,gBAAsC;EAAA,QAApCC,UAAoC,QAApCA,UAAoC;EAAA,QAAxBC,WAAwB,QAAxBA,WAAwB;EAAA,QAAXC,KAAW,QAAXA,KAAW;EAAA,QACzDC,iBADyD,GACpCD,KADoC,CACzDC,iBADyD;EAEhE,QAAMC,aAAa,GAAGvC,gBAAgB,CAACoC,WAAW,CAACnC,SAAb,CAAtC;EACA,QAAMuC,UAAU,GAAGJ,WAAW,CAACK,aAAZ,CAA0BC,MAA7C;;EAEA,QAAI,CAACF,UAAL,EAAiB;EACf,aAAO,IAAP;EACD;;EAED,QAAMG,WAAW,GAAGJ,aAAa,KAAK,QAAlB,GAA6BC,UAAU,CAACI,GAAX,CAAgBC,CAA7C,GAAiD,CAArE;EACA,QAAMC,cAAc,GAAGP,aAAa,KAAK,KAAlB,GAA0BC,UAAU,CAACO,MAAX,CAAmBF,CAA7C,GAAiD,CAAxE;EACA,QAAMG,YAAY,GAAGT,aAAa,KAAK,OAAlB,GAA4BC,UAAU,CAACS,IAAX,CAAiBC,CAA7C,GAAiD,CAAtE;EACA,QAAMC,aAAa,GAAGZ,aAAa,KAAK,MAAlB,GAA2BC,UAAU,CAACY,KAAX,CAAkBF,CAA7C,GAAiD,CAAvE;EAEA,QAAMG,UAAU,GACdlB,UAAU,CAACS,GAAX,GAAiBX,OAAjB,GAA2BU,WAA3B,GAAyCL,iBAD3C;EAEA,QAAMgB,aAAa,GACjBrB,OAAO,GAAGE,UAAU,CAACY,MAArB,GAA8BD,cAA9B,GAA+CR,iBADjD;EAEA,QAAMiB,WAAW,GACfpB,UAAU,CAACc,IAAX,GAAkBjB,OAAlB,GAA4BgB,YAA5B,GAA2CV,iBAD7C;EAEA,QAAMkB,YAAY,GAChBxB,OAAO,GAAGG,UAAU,CAACiB,KAArB,GAA6BD,aAA7B,GAA6Cb,iBAD/C;EAGA,WAAOe,UAAU,IAAIC,aAAd,IAA+BC,WAA/B,IAA8CC,YAArD;EACD,GAxBM,CAAP;EAyBD;AAED,EAAO,SAASC,2BAAT,CACLC,GADK,EAELC,MAFK,EAGLC,QAHK,EAIC;EACN,MAAMC,MAAM,GAAMF,MAAN,kBAAZ,CADM;EAMN;;EACA,GAAC,eAAD,EAAkB,qBAAlB,EAAyCvE,OAAzC,CAAiD,UAAC2C,KAAD,EAAW;EAC1D2B,IAAAA,GAAG,CAACG,MAAD,CAAH,CAAY9B,KAAZ,EAAmB6B,QAAnB;EACD,GAFD;EAGD;;ECjHM,IAAME,YAAY,GAAG;EAACC,EAAAA,OAAO,EAAE;EAAV,CAArB;EACP,IAAIC,iBAAiB,GAAG,CAAxB;EAEA;;;;;;;AAMA,EAAO,SAASC,oBAAT,GAAsC;EAC3C,MAAIH,YAAY,CAACC,OAAjB,EAA0B;EACxB;EACD;;EAEDD,EAAAA,YAAY,CAACC,OAAb,GAAuB,IAAvB;;EAEA,MAAItH,MAAM,CAACyH,WAAX,EAAwB;EACtBnI,IAAAA,QAAQ,CAACoI,gBAAT,CAA0B,WAA1B,EAAuCC,mBAAvC;EACD;EACF;EAED;;;;;;AAKA,EAAO,SAASA,mBAAT,GAAqC;EAC1C,MAAMC,GAAG,GAAGH,WAAW,CAACG,GAAZ,EAAZ;;EAEA,MAAIA,GAAG,GAAGL,iBAAN,GAA0B,EAA9B,EAAkC;EAChCF,IAAAA,YAAY,CAACC,OAAb,GAAuB,KAAvB;EAEAhI,IAAAA,QAAQ,CAACuI,mBAAT,CAA6B,WAA7B,EAA0CF,mBAA1C;EACD;;EAEDJ,EAAAA,iBAAiB,GAAGK,GAApB;EACD;EAED;;;;;;;AAMA,EAAO,SAASE,YAAT,GAA8B;EACnC,MAAMC,aAAa,GAAGzI,QAAQ,CAACyI,aAA/B;;EAEA,MAAI1D,kBAAkB,CAAC0D,aAAD,CAAtB,EAAuC;EACrC,QAAMC,QAAQ,GAAGD,aAAa,CAACzD,MAA/B;;EAEA,QAAIyD,aAAa,CAACE,IAAd,IAAsB,CAACD,QAAQ,CAACjD,KAAT,CAAemD,SAA1C,EAAqD;EACnDH,MAAAA,aAAa,CAACE,IAAd;EACD;EACF;EACF;AAED,EAAe,SAASE,wBAAT,GAA0C;EACvD7I,EAAAA,QAAQ,CAACoI,gBAAT,CAA0B,YAA1B,EAAwCF,oBAAxC,EAA8D5G,aAA9D;EACAZ,EAAAA,MAAM,CAAC0H,gBAAP,CAAwB,MAAxB,EAAgCI,YAAhC;EACD;;EC5DM,SAASM,uBAAT,CAAiChB,MAAjC,EAAyD;EAC9D,MAAMiB,GAAG,GAAGjB,MAAM,KAAK,SAAX,GAAuB,YAAvB,GAAsC,GAAlD;EAEA,SAAO,CACFA,MADE,0BACyBiB,GADzB,8CAEL,oCAFK,EAGLC,IAHK,CAGA,GAHA,CAAP;EAID;AAED,EAAO,SAASC,KAAT,CAAenH,KAAf,EAAsC;EAC3C,MAAMoH,aAAa,GAAG,YAAtB;EACA,MAAMC,mBAAmB,GAAG,WAA5B;EAEA,SAAOrH,KAAK,CACTsH,OADI,CACIF,aADJ,EACmB,GADnB,EAEJE,OAFI,CAEID,mBAFJ,EAEyB,EAFzB,EAGJE,IAHI,EAAP;EAID;;EAED,SAASC,aAAT,CAAuBC,OAAvB,EAAgD;EAC9C,SAAON,KAAK,4BAGRA,KAAK,CAACM,OAAD,CAHG,0GAAZ;EAOD;;AAED,EAAO,SAASC,mBAAT,CAA6BD,OAA7B,EAAwD;EAC7D,SAAO,CACLD,aAAa,CAACC,OAAD,CADR;EAGL,wDAHK;EAKL,oBALK;EAOL,mBAPK,CAAP;EASD;;EAGD,IAAIE,eAAJ;;AACA,EAAa;EACXC,EAAAA,oBAAoB;EACrB;;AAED,EAAO,SAASA,oBAAT,GAAsC;EAC3CD,EAAAA,eAAe,GAAG,IAAIE,GAAJ,EAAlB;EACD;AAED,EAAO,SAASC,QAAT,CAAkBC,SAAlB,EAAsCN,OAAtC,EAA6D;EAClE,MAAIM,SAAS,IAAI,CAACJ,eAAe,CAACK,GAAhB,CAAoBP,OAApB,CAAlB,EAAgD;EAAA;;EAC9CE,IAAAA,eAAe,CAACM,GAAhB,CAAoBR,OAApB;;EACA,gBAAAS,OAAO,EAACC,IAAR,iBAAgBT,mBAAmB,CAACD,OAAD,CAAnC;EACD;EACF;AAED,EAAO,SAASW,SAAT,CAAmBL,SAAnB,EAAuCN,OAAvC,EAA8D;EACnE,MAAIM,SAAS,IAAI,CAACJ,eAAe,CAACK,GAAhB,CAAoBP,OAApB,CAAlB,EAAgD;EAAA;;EAC9CE,IAAAA,eAAe,CAACM,GAAhB,CAAoBR,OAApB;;EACA,iBAAAS,OAAO,EAACG,KAAR,kBAAiBX,mBAAmB,CAACD,OAAD,CAApC;EACD;EACF;AAED,EAAO,SAASa,eAAT,CAAyBC,OAAzB,EAAiD;EACtD,MAAMC,iBAAiB,GAAG,CAACD,OAA3B;EACA,MAAME,kBAAkB,GACtBjG,MAAM,CAACkG,SAAP,CAAiBjI,QAAjB,CAA0BX,IAA1B,CAA+ByI,OAA/B,MAA4C,iBAA5C,IACA,CAAEA,OAAD,CAAiBjC,gBAFpB;EAIA8B,EAAAA,SAAS,CACPI,iBADO,EAEP,CACE,oBADF,EAEE,MAAMG,MAAM,CAACJ,OAAD,CAAZ,GAAwB,GAF1B,EAGE,oEAHF,EAIE,yBAJF,EAKErB,IALF,CAKO,GALP,CAFO,CAAT;EAUAkB,EAAAA,SAAS,CACPK,kBADO,EAEP,CACE,yEADF,EAEE,oEAFF,EAGEvB,IAHF,CAGO,GAHP,CAFO,CAAT;EAOD;;EClFD,IAAM0B,WAAW,GAAG;EAClBC,EAAAA,WAAW,EAAE,KADK;EAElBC,EAAAA,YAAY,EAAE,KAFI;EAGlBC,EAAAA,iBAAiB,EAAE,KAHD;EAIlBC,EAAAA,MAAM,EAAE;EAJU,CAApB;EAOA,IAAMC,WAAW,GAAG;EAClBC,EAAAA,SAAS,EAAE,KADO;EAElBC,EAAAA,SAAS,EAAE,MAFO;EAGlBC,EAAAA,KAAK,EAAE,IAHW;EAIlBC,EAAAA,OAAO,EAAE,EAJS;EAKlBC,EAAAA,OAAO,EAAE,KALS;EAMlBC,EAAAA,QAAQ,EAAE,GANQ;EAOlBC,EAAAA,IAAI,EAAE,SAPY;EAQlBC,EAAAA,KAAK,EAAE,EARW;EASlBC,EAAAA,MAAM,EAAE;EATU,CAApB;AAYA,EAAO,IAAMC,YAA0B;EACrCC,EAAAA,QAAQ,EAAE;EAAA,WAAM1L,QAAQ,CAAC2L,IAAf;EAAA,GAD2B;EAErCC,EAAAA,IAAI,EAAE;EACJT,IAAAA,OAAO,EAAE,MADL;EAEJU,IAAAA,QAAQ,EAAE;EAFN,GAF+B;EAMrCC,EAAAA,KAAK,EAAE,CAN8B;EAOrCC,EAAAA,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,CAP2B;EAQrCC,EAAAA,sBAAsB,EAAE,IARa;EASrCC,EAAAA,WAAW,EAAE,IATwB;EAUrCC,EAAAA,gBAAgB,EAAE,KAVmB;EAWrCC,EAAAA,WAAW,EAAE,KAXwB;EAYrC5F,EAAAA,iBAAiB,EAAE,CAZkB;EAarC6F,EAAAA,mBAAmB,EAAE,CAbgB;EAcrCC,EAAAA,cAAc,EAAE,EAdqB;EAerC1F,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,EAAJ,CAf6B;EAgBrC2F,EAAAA,aAhBqC,2BAgBrB,EAhBqB;EAiBrCC,EAAAA,cAjBqC,4BAiBpB,EAjBoB;EAkBrCC,EAAAA,QAlBqC,sBAkB1B,EAlB0B;EAmBrCC,EAAAA,SAnBqC,uBAmBzB,EAnByB;EAoBrCC,EAAAA,QApBqC,sBAoB1B,EApB0B;EAqBrCC,EAAAA,MArBqC,oBAqB5B,EArB4B;EAsBrCC,EAAAA,OAtBqC,qBAsB3B,EAtB2B;EAuBrCC,EAAAA,MAvBqC,oBAuB5B,EAvB4B;EAwBrCC,EAAAA,OAxBqC,qBAwB3B,EAxB2B;EAyBrCC,EAAAA,SAzBqC,uBAyBzB,EAzByB;EA0BrCC,EAAAA,WA1BqC,yBA0BvB,EA1BuB;EA2BrCC,EAAAA,cA3BqC,4BA2BpB,EA3BoB;EA4BrC/I,EAAAA,SAAS,EAAE,KA5B0B;EA6BrCgJ,EAAAA,OAAO,EAAE,EA7B4B;EA8BrCC,EAAAA,aAAa,EAAE,EA9BsB;EA+BrCC,EAAAA,MAAM,EAAE,IA/B6B;EAgCrCC,EAAAA,YAAY,EAAE,KAhCuB;EAiCrCC,EAAAA,KAAK,EAAE,IAjC8B;EAkCrCC,EAAAA,OAAO,EAAE,kBAlC4B;EAmCrCC,EAAAA,aAAa,EAAE;EAnCsB,GAoClC9C,WApCkC,MAqClCK,WArCkC,CAAhC;EAwCP,IAAM0C,WAAW,GAAGnJ,MAAM,CAACnB,IAAP,CAAYsI,YAAZ,CAApB;AAEA,EAAO,IAAMiC,eAAyC,GAAG,SAA5CA,eAA4C,CAACC,YAAD,EAAkB;EACzE;EACA,EAAa;EACXC,IAAAA,aAAa,CAACD,YAAD,EAAe,EAAf,CAAb;EACD;;EAED,MAAMxK,IAAI,GAAGmB,MAAM,CAACnB,IAAP,CAAYwK,YAAZ,CAAb;EACAxK,EAAAA,IAAI,CAACE,OAAL,CAAa,UAAC1B,GAAD,EAAS;EACnB8J,IAAAA,YAAD,CAAsB9J,GAAtB,IAA6BgM,YAAY,CAAChM,GAAD,CAAzC;EACD,GAFD;EAGD,CAVM;AAYP,EAAO,SAASkM,sBAAT,CACLC,WADK,EAEW;EAChB,MAAMZ,OAAO,GAAGY,WAAW,CAACZ,OAAZ,IAAuB,EAAvC;EACA,MAAMxC,WAAW,GAAGwC,OAAO,CAAC3I,MAAR,CAAwC,UAACC,GAAD,EAAMuJ,MAAN,EAAiB;EAAA,QACpEC,IADoE,GAC9CD,MAD8C,CACpEC,IADoE;EAAA,QAC9DhM,YAD8D,GAC9C+L,MAD8C,CAC9D/L,YAD8D;;EAG3E,QAAIgM,IAAJ,EAAU;EACRxJ,MAAAA,GAAG,CAACwJ,IAAD,CAAH,GACEF,WAAW,CAACE,IAAD,CAAX,KAAsBvJ,SAAtB,GAAkCqJ,WAAW,CAACE,IAAD,CAA7C,GAAsDhM,YADxD;EAED;;EAED,WAAOwC,GAAP;EACD,GATmB,EASjB,EATiB,CAApB;EAWA,2BACKsJ,WADL,MAEKpD,WAFL;EAID;AAED,EAAO,SAASuD,qBAAT,CACLhJ,SADK,EAELiI,OAFK,EAGoB;EACzB,MAAMgB,QAAQ,GAAGhB,OAAO,GACpB5I,MAAM,CAACnB,IAAP,CAAY0K,sBAAsB,mBAAKpC,YAAL;EAAmByB,IAAAA,OAAO,EAAPA;EAAnB,KAAlC,CADoB,GAEpBO,WAFJ;EAIA,MAAMnH,KAAK,GAAG4H,QAAQ,CAAC3J,MAAT,CACZ,UAACC,GAAD,EAAgD7C,GAAhD,EAAwD;EACtD,QAAMwM,aAAa,GAAG,CACpBlJ,SAAS,CAACmJ,YAAV,iBAAqCzM,GAArC,KAA+C,EAD3B,EAEpB0H,IAFoB,EAAtB;;EAIA,QAAI,CAAC8E,aAAL,EAAoB;EAClB,aAAO3J,GAAP;EACD;;EAED,QAAI7C,GAAG,KAAK,SAAZ,EAAuB;EACrB6C,MAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWwM,aAAX;EACD,KAFD,MAEO;EACL,UAAI;EACF3J,QAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAW0M,IAAI,CAACC,KAAL,CAAWH,aAAX,CAAX;EACD,OAFD,CAEE,OAAOI,CAAP,EAAU;EACV/J,QAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWwM,aAAX;EACD;EACF;;EAED,WAAO3J,GAAP;EACD,GArBW,EAsBZ,EAtBY,CAAd;EAyBA,SAAO8B,KAAP;EACD;AAED,EAAO,SAASkI,aAAT,CACLvJ,SADK,EAELqB,KAFK,EAGE;EACP,MAAMmI,GAAG,qBACJnI,KADI;EAEP6E,IAAAA,OAAO,EAAE1I,sBAAsB,CAAC6D,KAAK,CAAC6E,OAAP,EAAgB,CAAClG,SAAD,CAAhB;EAFxB,KAGHqB,KAAK,CAAC4F,gBAAN,GACA,EADA,GAEA+B,qBAAqB,CAAChJ,SAAD,EAAYqB,KAAK,CAAC4G,OAAlB,CALlB,CAAT;EAQAuB,EAAAA,GAAG,CAAC7C,IAAJ,qBACKH,YAAY,CAACG,IADlB,MAEK6C,GAAG,CAAC7C,IAFT;EAKA6C,EAAAA,GAAG,CAAC7C,IAAJ,GAAW;EACTC,IAAAA,QAAQ,EACN4C,GAAG,CAAC7C,IAAJ,CAASC,QAAT,KAAsB,MAAtB,GAA+BvF,KAAK,CAAC6F,WAArC,GAAmDsC,GAAG,CAAC7C,IAAJ,CAASC,QAFrD;EAGTV,IAAAA,OAAO,EACLsD,GAAG,CAAC7C,IAAJ,CAAST,OAAT,KAAqB,MAArB,GACI7E,KAAK,CAAC6F,WAAN,GACE,IADF,GAEE,aAHN,GAIIsC,GAAG,CAAC7C,IAAJ,CAAST;EARN,GAAX;EAWA,SAAOsD,GAAP;EACD;AAED,EAAO,SAASb,aAAT,CACLD,YADK,EAELT,OAFK,EAGC;EAAA,MAFNS,YAEM;EAFNA,IAAAA,YAEM,GAFyB,EAEzB;EAAA;;EAAA,MADNT,OACM;EADNA,IAAAA,OACM,GADc,EACd;EAAA;;EACN,MAAM/J,IAAI,GAAGmB,MAAM,CAACnB,IAAP,CAAYwK,YAAZ,CAAb;EACAxK,EAAAA,IAAI,CAACE,OAAL,CAAa,UAACqL,IAAD,EAAU;EACrB,QAAMC,cAAc,GAAGzL,gBAAgB,CACrCuI,YADqC,EAErCnH,MAAM,CAACnB,IAAP,CAAYuH,WAAZ,CAFqC,CAAvC;EAKA,QAAIkE,kBAAkB,GAAG,CAACnN,cAAc,CAACkN,cAAD,EAAiBD,IAAjB,CAAxC,CANqB;;EASrB,QAAIE,kBAAJ,EAAwB;EACtBA,MAAAA,kBAAkB,GAChB1B,OAAO,CAAC1J,MAAR,CAAe,UAACuK,MAAD;EAAA,eAAYA,MAAM,CAACC,IAAP,KAAgBU,IAA5B;EAAA,OAAf,EAAiDG,MAAjD,KAA4D,CAD9D;EAED;;EAEDjF,IAAAA,QAAQ,CACNgF,kBADM,EAEN,OACOF,IADP,QAEE,sEAFF,EAGE,2DAHF,EAIE,MAJF,EAKE,8DALF,EAME,wDANF,EAOE1F,IAPF,CAOO,GAPP,CAFM,CAAR;EAWD,GAzBD;EA0BD;;EC3LD,IAAM8F,SAAS,GAAG,SAAZA,SAAY;EAAA,SAAmB,WAAnB;EAAA,CAAlB;;EAEA,SAASC,uBAAT,CAAiCnJ,OAAjC,EAAmDoJ,IAAnD,EAAuE;EACrEpJ,EAAAA,OAAO,CAACkJ,SAAS,EAAV,CAAP,GAAuBE,IAAvB;EACD;;EAED,SAASC,kBAAT,CAA4BnN,KAA5B,EAAmE;EACjE,MAAMoJ,KAAK,GAAGxG,GAAG,EAAjB;;EAEA,MAAI5C,KAAK,KAAK,IAAd,EAAoB;EAClBoJ,IAAAA,KAAK,CAACgE,SAAN,GAAkB9N,WAAlB;EACD,GAFD,MAEO;EACL8J,IAAAA,KAAK,CAACgE,SAAN,GAAkB7N,eAAlB;;EAEA,QAAIsD,SAAS,CAAC7C,KAAD,CAAb,EAAsB;EACpBoJ,MAAAA,KAAK,CAAC1K,WAAN,CAAkBsB,KAAlB;EACD,KAFD,MAEO;EACLiN,MAAAA,uBAAuB,CAAC7D,KAAD,EAAQpJ,KAAR,CAAvB;EACD;EACF;;EAED,SAAOoJ,KAAP;EACD;;AAED,EAAO,SAASiE,UAAT,CAAoBhE,OAApB,EAA6C7E,KAA7C,EAAiE;EACtE,MAAI3B,SAAS,CAAC2B,KAAK,CAAC6E,OAAP,CAAb,EAA8B;EAC5B4D,IAAAA,uBAAuB,CAAC5D,OAAD,EAAU,EAAV,CAAvB;EACAA,IAAAA,OAAO,CAAC3K,WAAR,CAAoB8F,KAAK,CAAC6E,OAA1B;EACD,GAHD,MAGO,IAAI,OAAO7E,KAAK,CAAC6E,OAAb,KAAyB,UAA7B,EAAyC;EAC9C,QAAI7E,KAAK,CAAC0E,SAAV,EAAqB;EACnB+D,MAAAA,uBAAuB,CAAC5D,OAAD,EAAU7E,KAAK,CAAC6E,OAAhB,CAAvB;EACD,KAFD,MAEO;EACLA,MAAAA,OAAO,CAACjL,WAAR,GAAsBoG,KAAK,CAAC6E,OAA5B;EACD;EACF;EACF;AAED,EAAO,SAASiE,WAAT,CAAqBC,MAArB,EAA4D;EACjE,MAAM1H,GAAG,GAAG0H,MAAM,CAACC,iBAAnB;EACA,MAAMC,WAAW,GAAGpL,SAAS,CAACwD,GAAG,CAAC6H,QAAL,CAA7B;EAEA,SAAO;EACL7H,IAAAA,GAAG,EAAHA,GADK;EAELwD,IAAAA,OAAO,EAAEoE,WAAW,CAACE,IAAZ,CAAiB,UAACC,IAAD;EAAA,aAAUA,IAAI,CAACC,SAAL,CAAeC,QAAf,CAAwB1O,aAAxB,CAAV;EAAA,KAAjB,CAFJ;EAGLgK,IAAAA,KAAK,EAAEqE,WAAW,CAACE,IAAZ,CACL,UAACC,IAAD;EAAA,aACEA,IAAI,CAACC,SAAL,CAAeC,QAAf,CAAwBxO,WAAxB,KACAsO,IAAI,CAACC,SAAL,CAAeC,QAAf,CAAwBvO,eAAxB,CAFF;EAAA,KADK,CAHF;EAQLwO,IAAAA,QAAQ,EAAEN,WAAW,CAACE,IAAZ,CAAiB,UAACC,IAAD;EAAA,aACzBA,IAAI,CAACC,SAAL,CAAeC,QAAf,CAAwBzO,cAAxB,CADyB;EAAA,KAAjB;EARL,GAAP;EAYD;AAED,EAAO,SAASiM,MAAT,CACL1E,QADK,EAKL;EACA,MAAM2G,MAAM,GAAG3K,GAAG,EAAlB;EAEA,MAAMiD,GAAG,GAAGjD,GAAG,EAAf;EACAiD,EAAAA,GAAG,CAACuH,SAAJ,GAAgBjO,SAAhB;EACA0G,EAAAA,GAAG,CAACxH,YAAJ,CAAiB,YAAjB,EAA+B,QAA/B;EACAwH,EAAAA,GAAG,CAACxH,YAAJ,CAAiB,UAAjB,EAA6B,IAA7B;EAEA,MAAMgL,OAAO,GAAGzG,GAAG,EAAnB;EACAyG,EAAAA,OAAO,CAAC+D,SAAR,GAAoBhO,aAApB;EACAiK,EAAAA,OAAO,CAAChL,YAAR,CAAqB,YAArB,EAAmC,QAAnC;EAEAgP,EAAAA,UAAU,CAAChE,OAAD,EAAUzC,QAAQ,CAACpC,KAAnB,CAAV;EAEA+I,EAAAA,MAAM,CAAC7O,WAAP,CAAmBmH,GAAnB;EACAA,EAAAA,GAAG,CAACnH,WAAJ,CAAgB2K,OAAhB;EAEA2E,EAAAA,QAAQ,CAACpH,QAAQ,CAACpC,KAAV,EAAiBoC,QAAQ,CAACpC,KAA1B,CAAR;;EAEA,WAASwJ,QAAT,CAAkBC,SAAlB,EAAoCC,SAApC,EAA4D;EAAA,uBAC5BZ,WAAW,CAACC,MAAD,CADiB;EAAA,QACnD1H,GADmD,gBACnDA,GADmD;EAAA,QAC9CwD,OAD8C,gBAC9CA,OAD8C;EAAA,QACrCD,KADqC,gBACrCA,KADqC;;EAG1D,QAAI8E,SAAS,CAACzE,KAAd,EAAqB;EACnB5D,MAAAA,GAAG,CAACxH,YAAJ,CAAiB,YAAjB,EAA+B6P,SAAS,CAACzE,KAAzC;EACD,KAFD,MAEO;EACL5D,MAAAA,GAAG,CAACsI,eAAJ,CAAoB,YAApB;EACD;;EAED,QAAI,OAAOD,SAAS,CAAC/E,SAAjB,KAA+B,QAAnC,EAA6C;EAC3CtD,MAAAA,GAAG,CAACxH,YAAJ,CAAiB,gBAAjB,EAAmC6P,SAAS,CAAC/E,SAA7C;EACD,KAFD,MAEO;EACLtD,MAAAA,GAAG,CAACsI,eAAJ,CAAoB,gBAApB;EACD;;EAED,QAAID,SAAS,CAAC5E,OAAd,EAAuB;EACrBzD,MAAAA,GAAG,CAACxH,YAAJ,CAAiB,cAAjB,EAAiC,EAAjC;EACD,KAFD,MAEO;EACLwH,MAAAA,GAAG,CAACsI,eAAJ,CAAoB,cAApB;EACD;;EAEDtI,IAAAA,GAAG,CAAC5H,KAAJ,CAAUsL,QAAV,GACE,OAAO2E,SAAS,CAAC3E,QAAjB,KAA8B,QAA9B,GACO2E,SAAS,CAAC3E,QADjB,UAEI2E,SAAS,CAAC3E,QAHhB;;EAKA,QAAI2E,SAAS,CAAC1E,IAAd,EAAoB;EAClB3D,MAAAA,GAAG,CAACxH,YAAJ,CAAiB,MAAjB,EAAyB6P,SAAS,CAAC1E,IAAnC;EACD,KAFD,MAEO;EACL3D,MAAAA,GAAG,CAACsI,eAAJ,CAAoB,MAApB;EACD;;EAED,QACEF,SAAS,CAAC5E,OAAV,KAAsB6E,SAAS,CAAC7E,OAAhC,IACA4E,SAAS,CAAC/E,SAAV,KAAwBgF,SAAS,CAAChF,SAFpC,EAGE;EACAmE,MAAAA,UAAU,CAAChE,OAAD,EAAUzC,QAAQ,CAACpC,KAAnB,CAAV;EACD;;EAED,QAAI0J,SAAS,CAAC9E,KAAd,EAAqB;EACnB,UAAI,CAACA,KAAL,EAAY;EACVvD,QAAAA,GAAG,CAACnH,WAAJ,CAAgByO,kBAAkB,CAACe,SAAS,CAAC9E,KAAX,CAAlC;EACD,OAFD,MAEO,IAAI6E,SAAS,CAAC7E,KAAV,KAAoB8E,SAAS,CAAC9E,KAAlC,EAAyC;EAC9CvD,QAAAA,GAAG,CAACuI,WAAJ,CAAgBhF,KAAhB;EACAvD,QAAAA,GAAG,CAACnH,WAAJ,CAAgByO,kBAAkB,CAACe,SAAS,CAAC9E,KAAX,CAAlC;EACD;EACF,KAPD,MAOO,IAAIA,KAAJ,EAAW;EAChBvD,MAAAA,GAAG,CAACuI,WAAJ,CAAgBhF,KAAhB;EACD;EACF;;EAED,SAAO;EACLmE,IAAAA,MAAM,EAANA,MADK;EAELS,IAAAA,QAAQ,EAARA;EAFK,GAAP;EAID;EAGD;;EACA1C,MAAM,CAAC+C,OAAP,GAAiB,IAAjB;;EClHA,IAAIC,SAAS,GAAG,CAAhB;EACA,IAAIC,kBAAmD,GAAG,EAA1D;;AAGA,EAAO,IAAIC,gBAA4B,GAAG,EAAnC;AAEP,EAAe,SAASC,WAAT,CACbtL,SADa,EAEb6I,WAFa,EAGH;EACV,MAAMxH,KAAK,GAAGkI,aAAa,CAACvJ,SAAD,oBACtBwG,YADsB,MAEtBoC,sBAAsB,CAACxJ,oBAAoB,CAACyJ,WAAD,CAArB,CAFA,EAA3B,CADU;EAOV;EACA;;EACA,MAAI0C,WAAJ;EACA,MAAIC,WAAJ;EACA,MAAIC,0BAAJ;EACA,MAAIC,kBAAkB,GAAG,KAAzB;EACA,MAAIC,6BAA6B,GAAG,KAApC;EACA,MAAIC,YAAY,GAAG,KAAnB;EACA,MAAIC,mBAAmB,GAAG,KAA1B;EACA,MAAIC,gBAAJ;EACA,MAAIC,4BAAJ;EACA,MAAIC,aAAJ;EACA,MAAIC,SAA2B,GAAG,EAAlC;EACA,MAAIC,oBAAoB,GAAGxO,QAAQ,CAACyO,WAAD,EAAc9K,KAAK,CAAC8F,mBAApB,CAAnC;EACA,MAAIiF,aAAJ;EACA,MAAMC,GAAG,GAAG5L,gBAAgB,CAACY,KAAK,CAACkH,aAAN,IAAuBvI,SAAxB,CAA5B,CAtBU;EAyBV;EACA;;EACA,MAAMsM,EAAE,GAAGnB,SAAS,EAApB;EACA,MAAMoB,cAAc,GAAG,IAAvB;EACA,MAAMtE,OAAO,GAAGnJ,MAAM,CAACuC,KAAK,CAAC4G,OAAP,CAAtB;EAEA,MAAMzH,KAAK,GAAG;EACZ;EACAgM,IAAAA,SAAS,EAAE,IAFC;EAGZ;EACA7I,IAAAA,SAAS,EAAE,KAJC;EAKZ;EACA8I,IAAAA,WAAW,EAAE,KAND;EAOZ;EACAC,IAAAA,SAAS,EAAE,KARC;EASZ;EACAC,IAAAA,OAAO,EAAE;EAVG,GAAd;EAaA,MAAMlJ,QAAkB,GAAG;EACzB;EACA6I,IAAAA,EAAE,EAAFA,EAFyB;EAGzBtM,IAAAA,SAAS,EAATA,SAHyB;EAIzBoK,IAAAA,MAAM,EAAE3K,GAAG,EAJc;EAKzB8M,IAAAA,cAAc,EAAdA,cALyB;EAMzBlL,IAAAA,KAAK,EAALA,KANyB;EAOzBb,IAAAA,KAAK,EAALA,KAPyB;EAQzByH,IAAAA,OAAO,EAAPA,OARyB;EASzB;EACA2E,IAAAA,kBAAkB,EAAlBA,kBAVyB;EAWzBC,IAAAA,QAAQ,EAARA,QAXyB;EAYzB3C,IAAAA,UAAU,EAAVA,UAZyB;EAazB4C,IAAAA,IAAI,EAAJA,IAbyB;EAczBC,IAAAA,IAAI,EAAJA,IAdyB;EAezBC,IAAAA,qBAAqB,EAArBA,qBAfyB;EAgBzBC,IAAAA,MAAM,EAANA,MAhByB;EAiBzBC,IAAAA,OAAO,EAAPA,OAjByB;EAkBzBC,IAAAA,OAAO,EAAPA,OAlByB;EAmBzBC,IAAAA,OAAO,EAAPA;EAnByB,GAA3B,CA5CU;EAmEV;;EACA;;EACA,MAAI,CAAC/L,KAAK,CAAC8G,MAAX,EAAmB;EACjB,IAAa;EACXlD,MAAAA,SAAS,CAAC,IAAD,EAAO,0CAAP,CAAT;EACD;;EAED,WAAOxB,QAAP;EACD,GA3ES;EA8EV;EACA;;;EA/EU,sBAgFiBpC,KAAK,CAAC8G,MAAN,CAAa1E,QAAb,CAhFjB;EAAA,MAgFH2G,MAhFG,iBAgFHA,MAhFG;EAAA,MAgFKS,QAhFL,iBAgFKA,QAhFL;;EAkFVT,EAAAA,MAAM,CAAClP,YAAP,CAAoB,iBAApB,EAAsD,EAAtD;EACAkP,EAAAA,MAAM,CAACkC,EAAP,cAAoC7I,QAAQ,CAAC6I,EAA7C;EAEA7I,EAAAA,QAAQ,CAAC2G,MAAT,GAAkBA,MAAlB;EACApK,EAAAA,SAAS,CAACD,MAAV,GAAmB0D,QAAnB;EACA2G,EAAAA,MAAM,CAACrK,MAAP,GAAgB0D,QAAhB;EAEA,MAAM4J,YAAY,GAAGpF,OAAO,CAACqF,GAAR,CAAY,UAACxE,MAAD;EAAA,WAAYA,MAAM,CAACnL,EAAP,CAAU8F,QAAV,CAAZ;EAAA,GAAZ,CAArB;EACA,MAAM8J,eAAe,GAAGvN,SAAS,CAACwN,YAAV,CAAuB,eAAvB,CAAxB;EAEAC,EAAAA,YAAY;EACZC,EAAAA,2BAA2B;EAC3BC,EAAAA,YAAY;EAEZC,EAAAA,UAAU,CAAC,UAAD,EAAa,CAACnK,QAAD,CAAb,CAAV;;EAEA,MAAIpC,KAAK,CAAC+G,YAAV,EAAwB;EACtByF,IAAAA,YAAY;EACb,GApGS;EAuGV;;;EACAzD,EAAAA,MAAM,CAACjH,gBAAP,CAAwB,YAAxB,EAAsC,YAAM;EAC1C,QAAIM,QAAQ,CAACpC,KAAT,CAAe6F,WAAf,IAA8BzD,QAAQ,CAACjD,KAAT,CAAemD,SAAjD,EAA4D;EAC1DF,MAAAA,QAAQ,CAACmJ,kBAAT;EACD;EACF,GAJD;EAMAxC,EAAAA,MAAM,CAACjH,gBAAP,CAAwB,YAAxB,EAAsC,UAACpC,KAAD,EAAW;EAC/C,QACE0C,QAAQ,CAACpC,KAAT,CAAe6F,WAAf,IACAzD,QAAQ,CAACpC,KAAT,CAAeiH,OAAf,CAAuB/K,OAAvB,CAA+B,YAA/B,KAAgD,CAFlD,EAGE;EACA8O,MAAAA,GAAG,CAAClJ,gBAAJ,CAAqB,WAArB,EAAkC+I,oBAAlC;EACAA,MAAAA,oBAAoB,CAACnL,KAAD,CAApB;EACD;EACF,GARD;EAUA,SAAO0C,QAAP,CAxHU;EA2HV;EACA;;EACA,WAASqK,0BAAT,GAAkE;EAAA,QACzDzF,KADyD,GAChD5E,QAAQ,CAACpC,KADuC,CACzDgH,KADyD;EAEhE,WAAOrL,KAAK,CAACC,OAAN,CAAcoL,KAAd,IAAuBA,KAAvB,GAA+B,CAACA,KAAD,EAAQ,CAAR,CAAtC;EACD;;EAED,WAAS0F,wBAAT,GAA6C;EAC3C,WAAOD,0BAA0B,GAAG,CAAH,CAA1B,KAAoC,MAA3C;EACD;;EAED,WAASE,oBAAT,GAAyC;EAAA;;EACvC;EACA,WAAO,CAAC,2BAACvK,QAAQ,CAACpC,KAAT,CAAe8G,MAAhB,qBAAC,sBAAuB+C,OAAxB,CAAR;EACD;;EAED,WAAS+C,gBAAT,GAAqC;EACnC,WAAO7B,aAAa,IAAIpM,SAAxB;EACD;;EAED,WAASkO,0BAAT,GAAsD;EACpD,WAAO/D,WAAW,CAACC,MAAD,CAAlB;EACD;;EAED,WAAS+D,QAAT,CAAkBC,MAAlB,EAA2C;EACzC;EACA;EACA;EACA,QACG3K,QAAQ,CAACjD,KAAT,CAAekM,SAAf,IAA4B,CAACjJ,QAAQ,CAACjD,KAAT,CAAemD,SAA7C,IACAb,YAAY,CAACC,OADb,IAEC+I,gBAAgB,IAAIA,gBAAgB,CAAC1O,IAAjB,KAA0B,OAHjD,EAIE;EACA,aAAO,CAAP;EACD;;EAED,WAAOR,uBAAuB,CAC5B6G,QAAQ,CAACpC,KAAT,CAAewF,KADa,EAE5BuH,MAAM,GAAG,CAAH,GAAO,CAFe,EAG5B5H,YAAY,CAACK,KAHe,CAA9B;EAKD;;EAED,WAAS8G,YAAT,GAA8B;EAC5BvD,IAAAA,MAAM,CAACtP,KAAP,CAAauT,aAAb,GACE5K,QAAQ,CAACpC,KAAT,CAAe6F,WAAf,IAA8BzD,QAAQ,CAACjD,KAAT,CAAemD,SAA7C,GAAyD,EAAzD,GAA8D,MADhE;EAEAyG,IAAAA,MAAM,CAACtP,KAAP,CAAayL,MAAb,QAAyB9C,QAAQ,CAACpC,KAAT,CAAekF,MAAxC;EACD;;EAED,WAASqH,UAAT,CACEU,IADF,EAEE7Q,IAFF,EAGE8Q,qBAHF,EAIQ;EAAA,QADNA,qBACM;EADNA,MAAAA,qBACM,GADkB,IAClB;EAAA;;EACNlB,IAAAA,YAAY,CAACjP,OAAb,CAAqB,UAACoQ,WAAD,EAAiB;EACpC,UAAIA,WAAW,CAACF,IAAD,CAAf,EAAuB;EACrBE,QAAAA,WAAW,CAACF,IAAD,CAAX,eAAsB7Q,IAAtB;EACD;EACF,KAJD;;EAMA,QAAI8Q,qBAAJ,EAA2B;EAAA;;EACzB,yBAAA9K,QAAQ,CAACpC,KAAT,EAAeiN,IAAf,yBAAwB7Q,IAAxB;EACD;EACF;;EAED,WAASgR,0BAAT,GAA4C;EAAA,QACnC9H,IADmC,GAC3BlD,QAAQ,CAACpC,KADkB,CACnCsF,IADmC;;EAG1C,QAAI,CAACA,IAAI,CAACT,OAAV,EAAmB;EACjB;EACD;;EAED,QAAMwI,IAAI,aAAW/H,IAAI,CAACT,OAA1B;EACA,QAAMoG,EAAE,GAAGlC,MAAM,CAACkC,EAAlB;EACA,QAAMqC,KAAK,GAAGlQ,gBAAgB,CAACgF,QAAQ,CAACpC,KAAT,CAAekH,aAAf,IAAgCvI,SAAjC,CAA9B;EAEA2O,IAAAA,KAAK,CAACvQ,OAAN,CAAc,UAACqM,IAAD,EAAU;EACtB,UAAMmE,YAAY,GAAGnE,IAAI,CAACtB,YAAL,CAAkBuF,IAAlB,CAArB;;EAEA,UAAIjL,QAAQ,CAACjD,KAAT,CAAemD,SAAnB,EAA8B;EAC5B8G,QAAAA,IAAI,CAACvP,YAAL,CAAkBwT,IAAlB,EAAwBE,YAAY,GAAMA,YAAN,SAAsBtC,EAAtB,GAA6BA,EAAjE;EACD,OAFD,MAEO;EACL,YAAMuC,SAAS,GAAGD,YAAY,IAAIA,YAAY,CAACzK,OAAb,CAAqBmI,EAArB,EAAyB,EAAzB,EAA6BlI,IAA7B,EAAlC;;EAEA,YAAIyK,SAAJ,EAAe;EACbpE,UAAAA,IAAI,CAACvP,YAAL,CAAkBwT,IAAlB,EAAwBG,SAAxB;EACD,SAFD,MAEO;EACLpE,UAAAA,IAAI,CAACO,eAAL,CAAqB0D,IAArB;EACD;EACF;EACF,KAdD;EAeD;;EAED,WAAShB,2BAAT,GAA6C;EAC3C,QAAIH,eAAe,IAAI,CAAC9J,QAAQ,CAACpC,KAAT,CAAesF,IAAf,CAAoBC,QAA5C,EAAsD;EACpD;EACD;;EAED,QAAM+H,KAAK,GAAGlQ,gBAAgB,CAACgF,QAAQ,CAACpC,KAAT,CAAekH,aAAf,IAAgCvI,SAAjC,CAA9B;EAEA2O,IAAAA,KAAK,CAACvQ,OAAN,CAAc,UAACqM,IAAD,EAAU;EACtB,UAAIhH,QAAQ,CAACpC,KAAT,CAAe6F,WAAnB,EAAgC;EAC9BuD,QAAAA,IAAI,CAACvP,YAAL,CACE,eADF,EAEEuI,QAAQ,CAACjD,KAAT,CAAemD,SAAf,IAA4B8G,IAAI,KAAKwD,gBAAgB,EAArD,GACI,MADJ,GAEI,OAJN;EAMD,OAPD,MAOO;EACLxD,QAAAA,IAAI,CAACO,eAAL,CAAqB,eAArB;EACD;EACF,KAXD;EAYD;;EAED,WAAS8D,gCAAT,GAAkD;EAChDzC,IAAAA,GAAG,CAAC/I,mBAAJ,CAAwB,WAAxB,EAAqC4I,oBAArC;EACAd,IAAAA,kBAAkB,GAAGA,kBAAkB,CAAC7M,MAAnB,CACnB,UAACqE,QAAD;EAAA,aAAcA,QAAQ,KAAKsJ,oBAA3B;EAAA,KADmB,CAArB;EAGD;;EAED,WAAS6C,eAAT,CAAyBhO,KAAzB,EAA+D;EAC7D;EACA,QAAI+B,YAAY,CAACC,OAAjB,EAA0B;EACxB,UAAI6I,YAAY,IAAI7K,KAAK,CAAC3D,IAAN,KAAe,WAAnC,EAAgD;EAC9C;EACD;EACF,KAN4D;;;EAS7D,QACEqG,QAAQ,CAACpC,KAAT,CAAe6F,WAAf,IACAkD,MAAM,CAACO,QAAP,CAAgB5J,KAAK,CAACiO,MAAtB,CAFF,EAGE;EACA;EACD,KAd4D;;;EAiB7D,QAAIf,gBAAgB,GAAGtD,QAAnB,CAA4B5J,KAAK,CAACiO,MAAlC,CAAJ,EAA0D;EACxD,UAAIlM,YAAY,CAACC,OAAjB,EAA0B;EACxB;EACD;;EAED,UACEU,QAAQ,CAACjD,KAAT,CAAemD,SAAf,IACAF,QAAQ,CAACpC,KAAT,CAAeiH,OAAf,CAAuB/K,OAAvB,CAA+B,OAA/B,KAA2C,CAF7C,EAGE;EACA;EACD;EACF,KAXD,MAWO;EACLqQ,MAAAA,UAAU,CAAC,gBAAD,EAAmB,CAACnK,QAAD,EAAW1C,KAAX,CAAnB,CAAV;EACD;;EAED,QAAI0C,QAAQ,CAACpC,KAAT,CAAe2F,WAAf,KAA+B,IAAnC,EAAyC;EACvC0E,MAAAA,kBAAkB,GAAG,KAArB;EACAjI,MAAAA,QAAQ,CAACmJ,kBAAT;EACAnJ,MAAAA,QAAQ,CAACsJ,IAAT,GAHuC;EAMvC;EACA;;EACApB,MAAAA,6BAA6B,GAAG,IAAhC;EACA3N,MAAAA,UAAU,CAAC,YAAM;EACf2N,QAAAA,6BAA6B,GAAG,KAAhC;EACD,OAFS,CAAV,CATuC;EAcvC;EACA;;EACA,UAAI,CAAClI,QAAQ,CAACjD,KAAT,CAAekM,SAApB,EAA+B;EAC7BuC,QAAAA,mBAAmB;EACpB;EACF;EACF;;EAED,WAASC,WAAT,GAA6B;EAC3BtD,IAAAA,YAAY,GAAG,IAAf;EACD;;EAED,WAASuD,YAAT,GAA8B;EAC5BvD,IAAAA,YAAY,GAAG,KAAf;EACD;;EAED,WAASwD,gBAAT,GAAkC;EAChC/C,IAAAA,GAAG,CAAClJ,gBAAJ,CAAqB,WAArB,EAAkC4L,eAAlC,EAAmD,IAAnD;EACA1C,IAAAA,GAAG,CAAClJ,gBAAJ,CAAqB,UAArB,EAAiC4L,eAAjC,EAAkD1S,aAAlD;EACAgQ,IAAAA,GAAG,CAAClJ,gBAAJ,CAAqB,YAArB,EAAmCgM,YAAnC,EAAiD9S,aAAjD;EACAgQ,IAAAA,GAAG,CAAClJ,gBAAJ,CAAqB,WAArB,EAAkC+L,WAAlC,EAA+C7S,aAA/C;EACD;;EAED,WAAS4S,mBAAT,GAAqC;EACnC5C,IAAAA,GAAG,CAAC/I,mBAAJ,CAAwB,WAAxB,EAAqCyL,eAArC,EAAsD,IAAtD;EACA1C,IAAAA,GAAG,CAAC/I,mBAAJ,CAAwB,UAAxB,EAAoCyL,eAApC,EAAqD1S,aAArD;EACAgQ,IAAAA,GAAG,CAAC/I,mBAAJ,CAAwB,YAAxB,EAAsC6L,YAAtC,EAAoD9S,aAApD;EACAgQ,IAAAA,GAAG,CAAC/I,mBAAJ,CAAwB,WAAxB,EAAqC4L,WAArC,EAAkD7S,aAAlD;EACD;;EAED,WAASgT,iBAAT,CAA2BvI,QAA3B,EAA6CwI,QAA7C,EAAyE;EACvEC,IAAAA,eAAe,CAACzI,QAAD,EAAW,YAAM;EAC9B,UACE,CAACrD,QAAQ,CAACjD,KAAT,CAAemD,SAAhB,IACAyG,MAAM,CAACoF,UADP,IAEApF,MAAM,CAACoF,UAAP,CAAkB7E,QAAlB,CAA2BP,MAA3B,CAHF,EAIE;EACAkF,QAAAA,QAAQ;EACT;EACF,KARc,CAAf;EASD;;EAED,WAASG,gBAAT,CAA0B3I,QAA1B,EAA4CwI,QAA5C,EAAwE;EACtEC,IAAAA,eAAe,CAACzI,QAAD,EAAWwI,QAAX,CAAf;EACD;;EAED,WAASC,eAAT,CAAyBzI,QAAzB,EAA2CwI,QAA3C,EAAuE;EACrE,QAAM5M,GAAG,GAAGwL,0BAA0B,GAAGxL,GAAzC;;EAEA,aAASE,QAAT,CAAkB7B,KAAlB,EAAgD;EAC9C,UAAIA,KAAK,CAACiO,MAAN,KAAiBtM,GAArB,EAA0B;EACxBD,QAAAA,2BAA2B,CAACC,GAAD,EAAM,QAAN,EAAgBE,QAAhB,CAA3B;EACA0M,QAAAA,QAAQ;EACT;EACF,KARoE;EAWrE;;;EACA,QAAIxI,QAAQ,KAAK,CAAjB,EAAoB;EAClB,aAAOwI,QAAQ,EAAf;EACD;;EAED7M,IAAAA,2BAA2B,CAACC,GAAD,EAAM,QAAN,EAAgBqJ,4BAAhB,CAA3B;EACAtJ,IAAAA,2BAA2B,CAACC,GAAD,EAAM,KAAN,EAAaE,QAAb,CAA3B;EAEAmJ,IAAAA,4BAA4B,GAAGnJ,QAA/B;EACD;;EAED,WAAS8M,EAAT,CACEC,SADF,EAEEC,OAFF,EAGEC,OAHF,EAIQ;EAAA,QADNA,OACM;EADNA,MAAAA,OACM,GADsB,KACtB;EAAA;;EACN,QAAMlB,KAAK,GAAGlQ,gBAAgB,CAACgF,QAAQ,CAACpC,KAAT,CAAekH,aAAf,IAAgCvI,SAAjC,CAA9B;EACA2O,IAAAA,KAAK,CAACvQ,OAAN,CAAc,UAACqM,IAAD,EAAU;EACtBA,MAAAA,IAAI,CAACtH,gBAAL,CAAsBwM,SAAtB,EAAiCC,OAAjC,EAA0CC,OAA1C;EACA5D,MAAAA,SAAS,CAACpN,IAAV,CAAe;EAAC4L,QAAAA,IAAI,EAAJA,IAAD;EAAOkF,QAAAA,SAAS,EAATA,SAAP;EAAkBC,QAAAA,OAAO,EAAPA,OAAlB;EAA2BC,QAAAA,OAAO,EAAPA;EAA3B,OAAf;EACD,KAHD;EAID;;EAED,WAASpC,YAAT,GAA8B;EAC5B,QAAIM,wBAAwB,EAA5B,EAAgC;EAC9B2B,MAAAA,EAAE,CAAC,YAAD,EAAe5H,SAAf,EAA0B;EAACxL,QAAAA,OAAO,EAAE;EAAV,OAA1B,CAAF;EACAoT,MAAAA,EAAE,CAAC,UAAD,EAAaI,YAAb,EAA4C;EAACxT,QAAAA,OAAO,EAAE;EAAV,OAA5C,CAAF;EACD;;EAED+B,IAAAA,aAAa,CAACoF,QAAQ,CAACpC,KAAT,CAAeiH,OAAhB,CAAb,CAAsClK,OAAtC,CAA8C,UAACuR,SAAD,EAAe;EAC3D,UAAIA,SAAS,KAAK,QAAlB,EAA4B;EAC1B;EACD;;EAEDD,MAAAA,EAAE,CAACC,SAAD,EAAY7H,SAAZ,CAAF;;EAEA,cAAQ6H,SAAR;EACE,aAAK,YAAL;EACED,UAAAA,EAAE,CAAC,YAAD,EAAeI,YAAf,CAAF;EACA;;EACF,aAAK,OAAL;EACEJ,UAAAA,EAAE,CAAC7T,IAAI,GAAG,UAAH,GAAgB,MAArB,EAA6BkU,gBAA7B,CAAF;EACA;;EACF,aAAK,SAAL;EACEL,UAAAA,EAAE,CAAC,UAAD,EAAaK,gBAAb,CAAF;EACA;EATJ;EAWD,KAlBD;EAmBD;;EAED,WAASC,eAAT,GAAiC;EAC/B/D,IAAAA,SAAS,CAAC7N,OAAV,CAAkB,gBAAyD;EAAA,UAAvDqM,IAAuD,QAAvDA,IAAuD;EAAA,UAAjDkF,SAAiD,QAAjDA,SAAiD;EAAA,UAAtCC,OAAsC,QAAtCA,OAAsC;EAAA,UAA7BC,OAA6B,QAA7BA,OAA6B;EACzEpF,MAAAA,IAAI,CAACnH,mBAAL,CAAyBqM,SAAzB,EAAoCC,OAApC,EAA6CC,OAA7C;EACD,KAFD;EAGA5D,IAAAA,SAAS,GAAG,EAAZ;EACD;;EAED,WAASnE,SAAT,CAAmB/G,KAAnB,EAAuC;EAAA;;EACrC,QAAIkP,uBAAuB,GAAG,KAA9B;;EAEA,QACE,CAACxM,QAAQ,CAACjD,KAAT,CAAegM,SAAhB,IACA0D,sBAAsB,CAACnP,KAAD,CADtB,IAEA4K,6BAHF,EAIE;EACA;EACD;;EAED,QAAMwE,UAAU,GAAG,sBAAArE,gBAAgB,SAAhB,8BAAkB1O,IAAlB,MAA2B,OAA9C;EAEA0O,IAAAA,gBAAgB,GAAG/K,KAAnB;EACAqL,IAAAA,aAAa,GAAGrL,KAAK,CAACqL,aAAtB;EAEAsB,IAAAA,2BAA2B;;EAE3B,QAAI,CAACjK,QAAQ,CAACjD,KAAT,CAAemD,SAAhB,IAA6B9D,YAAY,CAACkB,KAAD,CAA7C,EAAsD;EACpD;EACA;EACA;EACA;EACAqK,MAAAA,kBAAkB,CAAChN,OAAnB,CAA2B,UAACwE,QAAD;EAAA,eAAcA,QAAQ,CAAC7B,KAAD,CAAtB;EAAA,OAA3B;EACD,KAxBoC;;;EA2BrC,QACEA,KAAK,CAAC3D,IAAN,KAAe,OAAf,KACCqG,QAAQ,CAACpC,KAAT,CAAeiH,OAAf,CAAuB/K,OAAvB,CAA+B,YAA/B,IAA+C,CAA/C,IACCmO,kBAFF,KAGAjI,QAAQ,CAACpC,KAAT,CAAe2F,WAAf,KAA+B,KAH/B,IAIAvD,QAAQ,CAACjD,KAAT,CAAemD,SALjB,EAME;EACAsM,MAAAA,uBAAuB,GAAG,IAA1B;EACD,KARD,MAQO;EACLpC,MAAAA,YAAY,CAAC9M,KAAD,CAAZ;EACD;;EAED,QAAIA,KAAK,CAAC3D,IAAN,KAAe,OAAnB,EAA4B;EAC1BsO,MAAAA,kBAAkB,GAAG,CAACuE,uBAAtB;EACD;;EAED,QAAIA,uBAAuB,IAAI,CAACE,UAAhC,EAA4C;EAC1CC,MAAAA,YAAY,CAACrP,KAAD,CAAZ;EACD;EACF;;EAED,WAASoL,WAAT,CAAqBpL,KAArB,EAA8C;EAC5C,QAAMiO,MAAM,GAAGjO,KAAK,CAACiO,MAArB;EACA,QAAMqB,6BAA6B,GACjCrQ,SAAS,CAAC2K,QAAV,CAAmBqE,MAAnB,KAA8B5E,MAAM,CAACO,QAAP,CAAgBqE,MAAhB,CADhC;;EAGA,QAAIjO,KAAK,CAAC3D,IAAN,KAAe,WAAf,IAA8BiT,6BAAlC,EAAiE;EAC/D;EACD;;EAED,QAAMvP,cAAc,GAAGwP,mBAAmB,GACvC5R,MADoB,CACb0L,MADa,EAEpBkD,GAFoB,CAEhB,UAAClD,MAAD,EAAY;EAAA;;EACf,UAAM3G,QAAQ,GAAG2G,MAAM,CAACrK,MAAxB;EACA,UAAMS,KAAK,4BAAGiD,QAAQ,CAAC8I,cAAZ,qBAAG,sBAAyB/L,KAAvC;;EAEA,UAAIA,KAAJ,EAAW;EACT,eAAO;EACLW,UAAAA,UAAU,EAAEiJ,MAAM,CAACmG,qBAAP,EADP;EAELnP,UAAAA,WAAW,EAAEZ,KAFR;EAGLa,UAAAA,KAAK,EAALA;EAHK,SAAP;EAKD;;EAED,aAAO,IAAP;EACD,KAfoB,EAgBpB9C,MAhBoB,CAgBbC,OAhBa,CAAvB;;EAkBA,QAAIqC,gCAAgC,CAACC,cAAD,EAAiBC,KAAjB,CAApC,EAA6D;EAC3D+N,MAAAA,gCAAgC;EAChCsB,MAAAA,YAAY,CAACrP,KAAD,CAAZ;EACD;EACF;;EAED,WAAS+O,YAAT,CAAsB/O,KAAtB,EAA+C;EAC7C,QAAMyP,UAAU,GACdN,sBAAsB,CAACnP,KAAD,CAAtB,IACC0C,QAAQ,CAACpC,KAAT,CAAeiH,OAAf,CAAuB/K,OAAvB,CAA+B,OAA/B,KAA2C,CAA3C,IAAgDmO,kBAFnD;;EAIA,QAAI8E,UAAJ,EAAgB;EACd;EACD;;EAED,QAAI/M,QAAQ,CAACpC,KAAT,CAAe6F,WAAnB,EAAgC;EAC9BzD,MAAAA,QAAQ,CAACuJ,qBAAT,CAA+BjM,KAA/B;EACA;EACD;;EAEDqP,IAAAA,YAAY,CAACrP,KAAD,CAAZ;EACD;;EAED,WAASgP,gBAAT,CAA0BhP,KAA1B,EAAmD;EACjD,QACE0C,QAAQ,CAACpC,KAAT,CAAeiH,OAAf,CAAuB/K,OAAvB,CAA+B,SAA/B,IAA4C,CAA5C,IACAwD,KAAK,CAACiO,MAAN,KAAiBf,gBAAgB,EAFnC,EAGE;EACA;EACD,KANgD;;;EASjD,QACExK,QAAQ,CAACpC,KAAT,CAAe6F,WAAf,IACAnG,KAAK,CAAC0P,aADN,IAEArG,MAAM,CAACO,QAAP,CAAgB5J,KAAK,CAAC0P,aAAtB,CAHF,EAIE;EACA;EACD;;EAEDL,IAAAA,YAAY,CAACrP,KAAD,CAAZ;EACD;;EAED,WAASmP,sBAAT,CAAgCnP,KAAhC,EAAuD;EACrD,WAAO+B,YAAY,CAACC,OAAb,GACHgL,wBAAwB,OAAOhN,KAAK,CAAC3D,IAAN,CAAWG,OAAX,CAAmB,OAAnB,KAA+B,CAD3D,GAEH,KAFJ;EAGD;;EAED,WAASmT,oBAAT,GAAsC;EACpCC,IAAAA,qBAAqB;EADe,2BAShClN,QAAQ,CAACpC,KATuB;EAAA,QAIlC6G,aAJkC,oBAIlCA,aAJkC;EAAA,QAKlCjJ,SALkC,oBAKlCA,SALkC;EAAA,QAMlCyC,MANkC,oBAMlCA,MANkC;EAAA,QAOlCqF,sBAPkC,oBAOlCA,sBAPkC;EAAA,QAQlCK,cARkC,oBAQlCA,cARkC;EAWpC,QAAMnB,KAAK,GAAG+H,oBAAoB,KAAK7D,WAAW,CAACC,MAAD,CAAX,CAAoBnE,KAAzB,GAAiC,IAAnE;EAEA,QAAM2K,iBAAiB,GAAG7J,sBAAsB,GAC5C;EACEwJ,MAAAA,qBAAqB,EAAExJ,sBADzB;EAEE8J,MAAAA,cAAc,EACZ9J,sBAAsB,CAAC8J,cAAvB,IAAyC5C,gBAAgB;EAH7D,KAD4C,GAM5CjO,SANJ;EAQA,QAAM8Q,aAAsC,GAAG;EAC7C/H,MAAAA,IAAI,EAAE,SADuC;EAE7CgI,MAAAA,OAAO,EAAE,IAFoC;EAG7CC,MAAAA,KAAK,EAAE,aAHsC;EAI7CC,MAAAA,QAAQ,EAAE,CAAC,eAAD,CAJmC;EAK7CtT,MAAAA,EAL6C,qBAKjC;EAAA,YAAR6C,KAAQ,SAARA,KAAQ;;EACV,YAAIwN,oBAAoB,EAAxB,EAA4B;EAAA,sCACZE,0BAA0B,EADd;EAAA,cACnBxL,GADmB,yBACnBA,GADmB;;EAG1B,WAAC,WAAD,EAAc,kBAAd,EAAkC,SAAlC,EAA6CtE,OAA7C,CAAqD,UAACsQ,IAAD,EAAU;EAC7D,gBAAIA,IAAI,KAAK,WAAb,EAA0B;EACxBhM,cAAAA,GAAG,CAACxH,YAAJ,CAAiB,gBAAjB,EAAmCsF,KAAK,CAACvB,SAAzC;EACD,aAFD,MAEO;EACL,kBAAIuB,KAAK,CAAC0Q,UAAN,CAAiB9G,MAAjB,kBAAuCsE,IAAvC,CAAJ,EAAoD;EAClDhM,gBAAAA,GAAG,CAACxH,YAAJ,WAAyBwT,IAAzB,EAAiC,EAAjC;EACD,eAFD,MAEO;EACLhM,gBAAAA,GAAG,CAACsI,eAAJ,WAA4B0D,IAA5B;EACD;EACF;EACF,WAVD;EAYAlO,UAAAA,KAAK,CAAC0Q,UAAN,CAAiB9G,MAAjB,GAA0B,EAA1B;EACD;EACF;EAvB4C,KAA/C;EA6BA,QAAM+G,SAAmC,GAAG,CAC1C;EACEpI,MAAAA,IAAI,EAAE,QADR;EAEE8G,MAAAA,OAAO,EAAE;EACPnO,QAAAA,MAAM,EAANA;EADO;EAFX,KAD0C,EAO1C;EACEqH,MAAAA,IAAI,EAAE,iBADR;EAEE8G,MAAAA,OAAO,EAAE;EACPuB,QAAAA,OAAO,EAAE;EACPxP,UAAAA,GAAG,EAAE,CADE;EAEPG,UAAAA,MAAM,EAAE,CAFD;EAGPE,UAAAA,IAAI,EAAE,CAHC;EAIPG,UAAAA,KAAK,EAAE;EAJA;EADF;EAFX,KAP0C,EAkB1C;EACE2G,MAAAA,IAAI,EAAE,MADR;EAEE8G,MAAAA,OAAO,EAAE;EACPuB,QAAAA,OAAO,EAAE;EADF;EAFX,KAlB0C,EAwB1C;EACErI,MAAAA,IAAI,EAAE,eADR;EAEE8G,MAAAA,OAAO,EAAE;EACPwB,QAAAA,QAAQ,EAAE,CAACjK;EADJ;EAFX,KAxB0C,EA8B1C0J,aA9B0C,CAA5C;;EAiCA,QAAI9C,oBAAoB,MAAM/H,KAA9B,EAAqC;EACnCkL,MAAAA,SAAS,CAACtS,IAAV,CAAe;EACbkK,QAAAA,IAAI,EAAE,OADO;EAEb8G,QAAAA,OAAO,EAAE;EACPlP,UAAAA,OAAO,EAAEsF,KADF;EAEPmL,UAAAA,OAAO,EAAE;EAFF;EAFI,OAAf;EAOD;;EAEDD,IAAAA,SAAS,CAACtS,IAAV,OAAAsS,SAAS,EAAU,CAAAjJ,aAAa,QAAb,YAAAA,aAAa,CAAEiJ,SAAf,KAA4B,EAAtC,CAAT;EAEA1N,IAAAA,QAAQ,CAAC8I,cAAT,GAA0B+E,iBAAY,CACpCV,iBADoC,EAEpCxG,MAFoC,oBAI/BlC,aAJ+B;EAKlCjJ,MAAAA,SAAS,EAATA,SALkC;EAMlC+M,MAAAA,aAAa,EAAbA,aANkC;EAOlCmF,MAAAA,SAAS,EAATA;EAPkC,OAAtC;EAUD;;EAED,WAASR,qBAAT,GAAuC;EACrC,QAAIlN,QAAQ,CAAC8I,cAAb,EAA6B;EAC3B9I,MAAAA,QAAQ,CAAC8I,cAAT,CAAwBa,OAAxB;EACA3J,MAAAA,QAAQ,CAAC8I,cAAT,GAA0B,IAA1B;EACD;EACF;;EAED,WAASgF,KAAT,GAAuB;EAAA,QACd9K,QADc,GACFhD,QAAQ,CAACpC,KADP,CACdoF,QADc;EAGrB,QAAI+I,UAAJ,CAHqB;EAMrB;EACA;EACA;EACA;;EACA,QAAM/E,IAAI,GAAGwD,gBAAgB,EAA7B;;EAEA,QACGxK,QAAQ,CAACpC,KAAT,CAAe6F,WAAf,IAA8BT,QAAQ,KAAKD,YAAY,CAACC,QAAzD,IACAA,QAAQ,KAAK,QAFf,EAGE;EACA+I,MAAAA,UAAU,GAAG/E,IAAI,CAAC+E,UAAlB;EACD,KALD,MAKO;EACLA,MAAAA,UAAU,GAAGhS,sBAAsB,CAACiJ,QAAD,EAAW,CAACgE,IAAD,CAAX,CAAnC;EACD,KAnBoB;EAsBrB;;;EACA,QAAI,CAAC+E,UAAU,CAAC7E,QAAX,CAAoBP,MAApB,CAAL,EAAkC;EAChCoF,MAAAA,UAAU,CAACjU,WAAX,CAAuB6O,MAAvB;EACD;;EAEDsG,IAAAA,oBAAoB;EAEpB;;EACA,IAAa;EACX;EACA/L,MAAAA,QAAQ,CACNlB,QAAQ,CAACpC,KAAT,CAAe6F,WAAf,IACET,QAAQ,KAAKD,YAAY,CAACC,QAD5B,IAEEgE,IAAI,CAAC+G,kBAAL,KAA4BpH,MAHxB,EAIN,CACE,8DADF,EAEE,mEAFF,EAGE,0BAHF,EAIE,MAJF,EAKE,kEALF,EAME,mDANF,EAOE,MAPF,EAQE,oEARF,EASE,6DATF,EAUE,sBAVF,EAWE,MAXF,EAYE,wEAZF,EAaErG,IAbF,CAaO,GAbP,CAJM,CAAR;EAmBD;EACF;;EAED,WAASuM,mBAAT,GAAgD;EAC9C,WAAOpR,SAAS,CACdkL,MAAM,CAAClK,gBAAP,CAAwB,mBAAxB,CADc,CAAhB;EAGD;;EAED,WAAS2N,YAAT,CAAsB9M,KAAtB,EAA2C;EACzC0C,IAAAA,QAAQ,CAACmJ,kBAAT;;EAEA,QAAI7L,KAAJ,EAAW;EACT6M,MAAAA,UAAU,CAAC,WAAD,EAAc,CAACnK,QAAD,EAAW1C,KAAX,CAAd,CAAV;EACD;;EAEDqO,IAAAA,gBAAgB;EAEhB,QAAIvI,KAAK,GAAGsH,QAAQ,CAAC,IAAD,CAApB;;EATyC,gCAURL,0BAA0B,EAVlB;EAAA,QAUlC2D,UAVkC;EAAA,QAUtBC,UAVsB;;EAYzC,QAAI5O,YAAY,CAACC,OAAb,IAAwB0O,UAAU,KAAK,MAAvC,IAAiDC,UAArD,EAAiE;EAC/D7K,MAAAA,KAAK,GAAG6K,UAAR;EACD;;EAED,QAAI7K,KAAJ,EAAW;EACT0E,MAAAA,WAAW,GAAGvN,UAAU,CAAC,YAAM;EAC7ByF,QAAAA,QAAQ,CAACqJ,IAAT;EACD,OAFuB,EAErBjG,KAFqB,CAAxB;EAGD,KAJD,MAIO;EACLpD,MAAAA,QAAQ,CAACqJ,IAAT;EACD;EACF;;EAED,WAASsD,YAAT,CAAsBrP,KAAtB,EAA0C;EACxC0C,IAAAA,QAAQ,CAACmJ,kBAAT;EAEAgB,IAAAA,UAAU,CAAC,aAAD,EAAgB,CAACnK,QAAD,EAAW1C,KAAX,CAAhB,CAAV;;EAEA,QAAI,CAAC0C,QAAQ,CAACjD,KAAT,CAAemD,SAApB,EAA+B;EAC7BsL,MAAAA,mBAAmB;EAEnB;EACD,KATuC;EAYxC;EACA;EACA;;;EACA,QACExL,QAAQ,CAACpC,KAAT,CAAeiH,OAAf,CAAuB/K,OAAvB,CAA+B,YAA/B,KAAgD,CAAhD,IACAkG,QAAQ,CAACpC,KAAT,CAAeiH,OAAf,CAAuB/K,OAAvB,CAA+B,OAA/B,KAA2C,CAD3C,IAEA,CAAC,YAAD,EAAe,WAAf,EAA4BA,OAA5B,CAAoCwD,KAAK,CAAC3D,IAA1C,KAAmD,CAFnD,IAGAsO,kBAJF,EAKE;EACA;EACD;;EAED,QAAM7E,KAAK,GAAGsH,QAAQ,CAAC,KAAD,CAAtB;;EAEA,QAAItH,KAAJ,EAAW;EACT2E,MAAAA,WAAW,GAAGxN,UAAU,CAAC,YAAM;EAC7B,YAAIyF,QAAQ,CAACjD,KAAT,CAAemD,SAAnB,EAA8B;EAC5BF,UAAAA,QAAQ,CAACsJ,IAAT;EACD;EACF,OAJuB,EAIrBlG,KAJqB,CAAxB;EAKD,KAND,MAMO;EACL;EACA;EACA4E,MAAAA,0BAA0B,GAAGkG,qBAAqB,CAAC,YAAM;EACvDlO,QAAAA,QAAQ,CAACsJ,IAAT;EACD,OAFiD,CAAlD;EAGD;EACF,GA9vBS;EAiwBV;EACA;;;EACA,WAASE,MAAT,GAAwB;EACtBxJ,IAAAA,QAAQ,CAACjD,KAAT,CAAegM,SAAf,GAA2B,IAA3B;EACD;;EAED,WAASU,OAAT,GAAyB;EACvB;EACA;EACAzJ,IAAAA,QAAQ,CAACsJ,IAAT;EACAtJ,IAAAA,QAAQ,CAACjD,KAAT,CAAegM,SAAf,GAA2B,KAA3B;EACD;;EAED,WAASI,kBAAT,GAAoC;EAClC7O,IAAAA,YAAY,CAACwN,WAAD,CAAZ;EACAxN,IAAAA,YAAY,CAACyN,WAAD,CAAZ;EACAoG,IAAAA,oBAAoB,CAACnG,0BAAD,CAApB;EACD;;EAED,WAASoB,QAAT,CAAkBnE,YAAlB,EAAsD;EACpD;EACA,IAAa;EACX/D,MAAAA,QAAQ,CAAClB,QAAQ,CAACjD,KAAT,CAAeiM,WAAhB,EAA6B5I,uBAAuB,CAAC,UAAD,CAApD,CAAR;EACD;;EAED,QAAIJ,QAAQ,CAACjD,KAAT,CAAeiM,WAAnB,EAAgC;EAC9B;EACD;;EAEDmB,IAAAA,UAAU,CAAC,gBAAD,EAAmB,CAACnK,QAAD,EAAWiF,YAAX,CAAnB,CAAV;EAEAsH,IAAAA,eAAe;EAEf,QAAMlF,SAAS,GAAGrH,QAAQ,CAACpC,KAA3B;EACA,QAAM0J,SAAS,GAAGxB,aAAa,CAACvJ,SAAD,oBAC1ByD,QAAQ,CAACpC,KADiB,MAE1BqH,YAF0B;EAG7BzB,MAAAA,gBAAgB,EAAE;EAHW,OAA/B;EAMAxD,IAAAA,QAAQ,CAACpC,KAAT,GAAiB0J,SAAjB;EAEA0C,IAAAA,YAAY;;EAEZ,QAAI3C,SAAS,CAAC3D,mBAAV,KAAkC4D,SAAS,CAAC5D,mBAAhD,EAAqE;EACnE2H,MAAAA,gCAAgC;EAChC5C,MAAAA,oBAAoB,GAAGxO,QAAQ,CAC7ByO,WAD6B,EAE7BpB,SAAS,CAAC5D,mBAFmB,CAA/B;EAID,KA/BmD;;;EAkCpD,QAAI2D,SAAS,CAACvC,aAAV,IAA2B,CAACwC,SAAS,CAACxC,aAA1C,EAAyD;EACvD9J,MAAAA,gBAAgB,CAACqM,SAAS,CAACvC,aAAX,CAAhB,CAA0CnK,OAA1C,CAAkD,UAACqM,IAAD,EAAU;EAC1DA,QAAAA,IAAI,CAACO,eAAL,CAAqB,eAArB;EACD,OAFD;EAGD,KAJD,MAIO,IAAID,SAAS,CAACxC,aAAd,EAA6B;EAClCvI,MAAAA,SAAS,CAACgL,eAAV,CAA0B,eAA1B;EACD;;EAED0C,IAAAA,2BAA2B;EAC3BC,IAAAA,YAAY;;EAEZ,QAAI9C,QAAJ,EAAc;EACZA,MAAAA,QAAQ,CAACC,SAAD,EAAYC,SAAZ,CAAR;EACD;;EAED,QAAItH,QAAQ,CAAC8I,cAAb,EAA6B;EAC3BmE,MAAAA,oBAAoB,GADO;EAI3B;EACA;EACA;;EACAJ,MAAAA,mBAAmB,GAAGlS,OAAtB,CAA8B,UAACyT,YAAD,EAAkB;EAC9C;EACA;EACAF,QAAAA,qBAAqB,CAACE,YAAY,CAAC9R,MAAb,CAAqBwM,cAArB,CAAqCuF,WAAtC,CAArB;EACD,OAJD;EAKD;;EAEDlE,IAAAA,UAAU,CAAC,eAAD,EAAkB,CAACnK,QAAD,EAAWiF,YAAX,CAAlB,CAAV;EACD;;EAED,WAASwB,UAAT,CAAoBhE,OAApB,EAA4C;EAC1CzC,IAAAA,QAAQ,CAACoJ,QAAT,CAAkB;EAAC3G,MAAAA,OAAO,EAAPA;EAAD,KAAlB;EACD;;EAED,WAAS4G,IAAT,GAAsB;EACpB;EACA,IAAa;EACXnI,MAAAA,QAAQ,CAAClB,QAAQ,CAACjD,KAAT,CAAeiM,WAAhB,EAA6B5I,uBAAuB,CAAC,MAAD,CAApD,CAAR;EACD,KAJmB;;;EAOpB,QAAMkO,gBAAgB,GAAGtO,QAAQ,CAACjD,KAAT,CAAemD,SAAxC;EACA,QAAM8I,WAAW,GAAGhJ,QAAQ,CAACjD,KAAT,CAAeiM,WAAnC;EACA,QAAMuF,UAAU,GAAG,CAACvO,QAAQ,CAACjD,KAAT,CAAegM,SAAnC;EACA,QAAMyF,uBAAuB,GAC3BnP,YAAY,CAACC,OAAb,IAAwB,CAACU,QAAQ,CAACpC,KAAT,CAAegH,KAD1C;EAEA,QAAMvB,QAAQ,GAAGlK,uBAAuB,CACtC6G,QAAQ,CAACpC,KAAT,CAAeyF,QADuB,EAEtC,CAFsC,EAGtCN,YAAY,CAACM,QAHyB,CAAxC;;EAMA,QACEiL,gBAAgB,IAChBtF,WADA,IAEAuF,UAFA,IAGAC,uBAJF,EAKE;EACA;EACD,KAzBmB;EA4BpB;EACA;;;EACA,QAAIhE,gBAAgB,GAAGT,YAAnB,CAAgC,UAAhC,CAAJ,EAAiD;EAC/C;EACD;;EAEDI,IAAAA,UAAU,CAAC,QAAD,EAAW,CAACnK,QAAD,CAAX,EAAuB,KAAvB,CAAV;;EACA,QAAIA,QAAQ,CAACpC,KAAT,CAAeuG,MAAf,CAAsBnE,QAAtB,MAAoC,KAAxC,EAA+C;EAC7C;EACD;;EAEDA,IAAAA,QAAQ,CAACjD,KAAT,CAAemD,SAAf,GAA2B,IAA3B;;EAEA,QAAIqK,oBAAoB,EAAxB,EAA4B;EAC1B5D,MAAAA,MAAM,CAACtP,KAAP,CAAaoX,UAAb,GAA0B,SAA1B;EACD;;EAEDvE,IAAAA,YAAY;EACZyB,IAAAA,gBAAgB;;EAEhB,QAAI,CAAC3L,QAAQ,CAACjD,KAAT,CAAekM,SAApB,EAA+B;EAC7BtC,MAAAA,MAAM,CAACtP,KAAP,CAAaqX,UAAb,GAA0B,MAA1B;EACD,KAlDmB;EAqDpB;;;EACA,QAAInE,oBAAoB,EAAxB,EAA4B;EAAA,mCACHE,0BAA0B,EADvB;EAAA,UACnBxL,GADmB,0BACnBA,GADmB;EAAA,UACdwD,OADc,0BACdA,OADc;;EAE1B/F,MAAAA,qBAAqB,CAAC,CAACuC,GAAD,EAAMwD,OAAN,CAAD,EAAiB,CAAjB,CAArB;EACD;;EAED8F,IAAAA,aAAa,GAAG,yBAAY;EAC1B,UAAI,CAACvI,QAAQ,CAACjD,KAAT,CAAemD,SAAhB,IAA6BkI,mBAAjC,EAAsD;EACpD;EACD;;EAEDA,MAAAA,mBAAmB,GAAG,IAAtB,CAL0B;;EAQ1B,WAAKzB,MAAM,CAACgI,YAAZ;EAEAhI,MAAAA,MAAM,CAACtP,KAAP,CAAaqX,UAAb,GAA0B1O,QAAQ,CAACpC,KAAT,CAAe+F,cAAzC;;EAEA,UAAI4G,oBAAoB,MAAMvK,QAAQ,CAACpC,KAAT,CAAe2E,SAA7C,EAAwD;EAAA,qCAC/BkI,0BAA0B,EADK;EAAA,YAC/CxL,IAD+C,0BAC/CA,GAD+C;EAAA,YAC1CwD,QAD0C,0BAC1CA,OAD0C;;EAEtD/F,QAAAA,qBAAqB,CAAC,CAACuC,IAAD,EAAMwD,QAAN,CAAD,EAAiBY,QAAjB,CAArB;EACAvG,QAAAA,kBAAkB,CAAC,CAACmC,IAAD,EAAMwD,QAAN,CAAD,EAAiB,SAAjB,CAAlB;EACD;;EAEDuI,MAAAA,0BAA0B;EAC1Bf,MAAAA,2BAA2B;EAE3B/O,MAAAA,YAAY,CAAC0M,gBAAD,EAAmB5H,QAAnB,CAAZ;EAEAA,MAAAA,QAAQ,CAACjD,KAAT,CAAekM,SAAf,GAA2B,IAA3B;EACAkB,MAAAA,UAAU,CAAC,SAAD,EAAY,CAACnK,QAAD,CAAZ,CAAV;;EAEA,UAAIA,QAAQ,CAACpC,KAAT,CAAe2E,SAAf,IAA4BgI,oBAAoB,EAApD,EAAwD;EACtDyB,QAAAA,gBAAgB,CAAC3I,QAAD,EAAW,YAAM;EAC/BrD,UAAAA,QAAQ,CAACjD,KAAT,CAAemM,OAAf,GAAyB,IAAzB;EACAiB,UAAAA,UAAU,CAAC,SAAD,EAAY,CAACnK,QAAD,CAAZ,CAAV;EACD,SAHe,CAAhB;EAID;EACF,KAhCD;;EAkCA8N,IAAAA,KAAK;EACN;;EAED,WAASxE,IAAT,GAAsB;EACpB;EACA,IAAa;EACXpI,MAAAA,QAAQ,CAAClB,QAAQ,CAACjD,KAAT,CAAeiM,WAAhB,EAA6B5I,uBAAuB,CAAC,MAAD,CAApD,CAAR;EACD,KAJmB;;;EAOpB,QAAMwO,eAAe,GAAG,CAAC5O,QAAQ,CAACjD,KAAT,CAAemD,SAAxC;EACA,QAAM8I,WAAW,GAAGhJ,QAAQ,CAACjD,KAAT,CAAeiM,WAAnC;EACA,QAAMuF,UAAU,GAAG,CAACvO,QAAQ,CAACjD,KAAT,CAAegM,SAAnC;EACA,QAAM1F,QAAQ,GAAGlK,uBAAuB,CACtC6G,QAAQ,CAACpC,KAAT,CAAeyF,QADuB,EAEtC,CAFsC,EAGtCN,YAAY,CAACM,QAHyB,CAAxC;;EAMA,QAAIuL,eAAe,IAAI5F,WAAnB,IAAkCuF,UAAtC,EAAkD;EAChD;EACD;;EAEDpE,IAAAA,UAAU,CAAC,QAAD,EAAW,CAACnK,QAAD,CAAX,EAAuB,KAAvB,CAAV;;EACA,QAAIA,QAAQ,CAACpC,KAAT,CAAeqG,MAAf,CAAsBjE,QAAtB,MAAoC,KAAxC,EAA+C;EAC7C;EACD;;EAEDA,IAAAA,QAAQ,CAACjD,KAAT,CAAemD,SAAf,GAA2B,KAA3B;EACAF,IAAAA,QAAQ,CAACjD,KAAT,CAAemM,OAAf,GAAyB,KAAzB;EACAd,IAAAA,mBAAmB,GAAG,KAAtB;;EAEA,QAAImC,oBAAoB,EAAxB,EAA4B;EAC1B5D,MAAAA,MAAM,CAACtP,KAAP,CAAaoX,UAAb,GAA0B,QAA1B;EACD;;EAEDpD,IAAAA,gCAAgC;EAChCG,IAAAA,mBAAmB;EACnBtB,IAAAA,YAAY;;EAEZ,QAAIK,oBAAoB,EAAxB,EAA4B;EAAA,mCACHE,0BAA0B,EADvB;EAAA,UACnBxL,GADmB,0BACnBA,GADmB;EAAA,UACdwD,OADc,0BACdA,OADc;;EAG1B,UAAIzC,QAAQ,CAACpC,KAAT,CAAe2E,SAAnB,EAA8B;EAC5B7F,QAAAA,qBAAqB,CAAC,CAACuC,GAAD,EAAMwD,OAAN,CAAD,EAAiBY,QAAjB,CAArB;EACAvG,QAAAA,kBAAkB,CAAC,CAACmC,GAAD,EAAMwD,OAAN,CAAD,EAAiB,QAAjB,CAAlB;EACD;EACF;;EAEDuI,IAAAA,0BAA0B;EAC1Bf,IAAAA,2BAA2B;;EAE3B,QAAIjK,QAAQ,CAACpC,KAAT,CAAe2E,SAAnB,EAA8B;EAC5B,UAAIgI,oBAAoB,EAAxB,EAA4B;EAC1BqB,QAAAA,iBAAiB,CAACvI,QAAD,EAAWrD,QAAQ,CAAC0J,OAApB,CAAjB;EACD;EACF,KAJD,MAIO;EACL1J,MAAAA,QAAQ,CAAC0J,OAAT;EACD;EACF;;EAED,WAASH,qBAAT,CAA+BjM,KAA/B,EAAwD;EACtD;EACA,IAAa;EACX4D,MAAAA,QAAQ,CACNlB,QAAQ,CAACjD,KAAT,CAAeiM,WADT,EAEN5I,uBAAuB,CAAC,uBAAD,CAFjB,CAAR;EAID;;EAEDwI,IAAAA,GAAG,CAAClJ,gBAAJ,CAAqB,WAArB,EAAkC+I,oBAAlC;EACAvN,IAAAA,YAAY,CAACyM,kBAAD,EAAqBc,oBAArB,CAAZ;EACAA,IAAAA,oBAAoB,CAACnL,KAAD,CAApB;EACD;;EAED,WAASoM,OAAT,GAAyB;EACvB;EACA,IAAa;EACXxI,MAAAA,QAAQ,CAAClB,QAAQ,CAACjD,KAAT,CAAeiM,WAAhB,EAA6B5I,uBAAuB,CAAC,SAAD,CAApD,CAAR;EACD;;EAED,QAAIJ,QAAQ,CAACjD,KAAT,CAAemD,SAAnB,EAA8B;EAC5BF,MAAAA,QAAQ,CAACsJ,IAAT;EACD;;EAED,QAAI,CAACtJ,QAAQ,CAACjD,KAAT,CAAekM,SAApB,EAA+B;EAC7B;EACD;;EAEDiE,IAAAA,qBAAqB,GAdE;EAiBvB;EACA;;EACAL,IAAAA,mBAAmB,GAAGlS,OAAtB,CAA8B,UAACyT,YAAD,EAAkB;EAC9CA,MAAAA,YAAY,CAAC9R,MAAb,CAAqBoN,OAArB;EACD,KAFD;;EAIA,QAAI/C,MAAM,CAACoF,UAAX,EAAuB;EACrBpF,MAAAA,MAAM,CAACoF,UAAP,CAAkBvE,WAAlB,CAA8Bb,MAA9B;EACD;;EAEDiB,IAAAA,gBAAgB,GAAGA,gBAAgB,CAAC9M,MAAjB,CAAwB,UAAC+T,CAAD;EAAA,aAAOA,CAAC,KAAK7O,QAAb;EAAA,KAAxB,CAAnB;EAEAA,IAAAA,QAAQ,CAACjD,KAAT,CAAekM,SAAf,GAA2B,KAA3B;EACAkB,IAAAA,UAAU,CAAC,UAAD,EAAa,CAACnK,QAAD,CAAb,CAAV;EACD;;EAED,WAAS2J,OAAT,GAAyB;EACvB;EACA,IAAa;EACXzI,MAAAA,QAAQ,CAAClB,QAAQ,CAACjD,KAAT,CAAeiM,WAAhB,EAA6B5I,uBAAuB,CAAC,SAAD,CAApD,CAAR;EACD;;EAED,QAAIJ,QAAQ,CAACjD,KAAT,CAAeiM,WAAnB,EAAgC;EAC9B;EACD;;EAEDhJ,IAAAA,QAAQ,CAACmJ,kBAAT;EACAnJ,IAAAA,QAAQ,CAAC0J,OAAT;EAEA6C,IAAAA,eAAe;EAEf,WAAOhQ,SAAS,CAACD,MAAjB;EAEA0D,IAAAA,QAAQ,CAACjD,KAAT,CAAeiM,WAAf,GAA6B,IAA7B;EAEAmB,IAAAA,UAAU,CAAC,WAAD,EAAc,CAACnK,QAAD,CAAd,CAAV;EACD;EACF;;EC7lCD,SAAS8O,KAAT,CACEnN,OADF,EAEEoN,aAFF,EAGyB;EAAA,MADvBA,aACuB;EADvBA,IAAAA,aACuB,GADS,EACT;EAAA;;EACvB,MAAMvK,OAAO,GAAGzB,YAAY,CAACyB,OAAb,CAAqBvJ,MAArB,CAA4B8T,aAAa,CAACvK,OAAd,IAAyB,EAArD,CAAhB;EAEA;;EACA,EAAa;EACX9C,IAAAA,eAAe,CAACC,OAAD,CAAf;EACAuD,IAAAA,aAAa,CAAC6J,aAAD,EAAgBvK,OAAhB,CAAb;EACD;;EAEDrE,EAAAA,wBAAwB;EAExB,MAAMiF,WAA2B,qBAAO2J,aAAP;EAAsBvK,IAAAA,OAAO,EAAPA;EAAtB,IAAjC;EAEA,MAAMwK,QAAQ,GAAGxS,kBAAkB,CAACmF,OAAD,CAAnC;EAEA;;EACA,EAAa;EACX,QAAMsN,sBAAsB,GAAGhT,SAAS,CAACmJ,WAAW,CAAC3C,OAAb,CAAxC;EACA,QAAMyM,6BAA6B,GAAGF,QAAQ,CAAC7I,MAAT,GAAkB,CAAxD;EACAjF,IAAAA,QAAQ,CACN+N,sBAAsB,IAAIC,6BADpB,EAEN,CACE,oEADF,EAEE,mEAFF,EAGE,mEAHF,EAIE,MAJF,EAKE,qEALF,EAME,kDANF,EAOE,MAPF,EAQE,iCARF,EASE,2CATF,EAUE5O,IAVF,CAUO,GAVP,CAFM,CAAR;EAcD;;EAED,MAAM6O,SAAS,GAAGH,QAAQ,CAACnT,MAAT,CAChB,UAACC,GAAD,EAAMS,SAAN,EAAgC;EAC9B,QAAMyD,QAAQ,GAAGzD,SAAS,IAAIsL,WAAW,CAACtL,SAAD,EAAY6I,WAAZ,CAAzC;;EAEA,QAAIpF,QAAJ,EAAc;EACZlE,MAAAA,GAAG,CAACV,IAAJ,CAAS4E,QAAT;EACD;;EAED,WAAOlE,GAAP;EACD,GATe,EAUhB,EAVgB,CAAlB;EAaA,SAAOG,SAAS,CAAC0F,OAAD,CAAT,GAAqBwN,SAAS,CAAC,CAAD,CAA9B,GAAoCA,SAA3C;EACD;;EAEDL,KAAK,CAAC/L,YAAN,GAAqBA,YAArB;EACA+L,KAAK,CAAC9J,eAAN,GAAwBA,eAAxB;EACA8J,KAAK,CAACzP,YAAN,GAAqBA,YAArB;AAEA,EAEO,IAAM+P,OAAgB,GAAG,SAAnBA,OAAmB,QAGL;EAAA,gCAAP,EAAO;EAAA,MAFhBC,2BAEgB,QAFzBC,OAEyB;EAAA,MADzBjM,QACyB,QADzBA,QACyB;;EACzBuE,EAAAA,gBAAgB,CAACjN,OAAjB,CAAyB,UAACqF,QAAD,EAAc;EACrC,QAAIuP,UAAU,GAAG,KAAjB;;EAEA,QAAIF,2BAAJ,EAAiC;EAC/BE,MAAAA,UAAU,GAAGlT,kBAAkB,CAACgT,2BAAD,CAAlB,GACTrP,QAAQ,CAACzD,SAAT,KAAuB8S,2BADd,GAETrP,QAAQ,CAAC2G,MAAT,KAAqB0I,2BAAD,CAA0C1I,MAFlE;EAGD;;EAED,QAAI,CAAC4I,UAAL,EAAiB;EACf,UAAMC,gBAAgB,GAAGxP,QAAQ,CAACpC,KAAT,CAAeyF,QAAxC;EAEArD,MAAAA,QAAQ,CAACoJ,QAAT,CAAkB;EAAC/F,QAAAA,QAAQ,EAARA;EAAD,OAAlB;EACArD,MAAAA,QAAQ,CAACsJ,IAAT;;EAEA,UAAI,CAACtJ,QAAQ,CAACjD,KAAT,CAAeiM,WAApB,EAAiC;EAC/BhJ,QAAAA,QAAQ,CAACoJ,QAAT,CAAkB;EAAC/F,UAAAA,QAAQ,EAAEmM;EAAX,SAAlB;EACD;EACF;EACF,GAnBD;EAoBD,CAxBM;;ECzDP,IAAMC,eAAgC,GAAG,SAAnCA,eAAmC,CACvCC,cADuC,EAEvCX,aAFuC,EAGpC;EAAA,MADHA,aACG;EADHA,IAAAA,aACG,GADa,EACb;EAAA;;EACH;EACA,EAAa;EACXvN,IAAAA,SAAS,CACP,CAACjI,KAAK,CAACC,OAAN,CAAckW,cAAd,CADM,EAEP,CACE,oEADF,EAEE,uCAFF,EAGE3N,MAAM,CAAC2N,cAAD,CAHR,EAIEpP,IAJF,CAIO,GAJP,CAFO,CAAT;EAQD;;EAED,MAAIqP,iBAAiB,GAAGD,cAAxB;EACA,MAAIE,UAAmC,GAAG,EAA1C;EACA,MAAIjH,aAAJ;EACA,MAAIkH,SAAS,GAAGd,aAAa,CAACc,SAA9B;;EAEA,WAASC,aAAT,GAA+B;EAC7BF,IAAAA,UAAU,GAAGD,iBAAiB,CAAC9F,GAAlB,CAAsB,UAAC7J,QAAD;EAAA,aAAcA,QAAQ,CAACzD,SAAvB;EAAA,KAAtB,CAAb;EACD;;EAED,WAASwT,eAAT,CAAyBhH,SAAzB,EAAmD;EACjD4G,IAAAA,iBAAiB,CAAChV,OAAlB,CAA0B,UAACqF,QAAD,EAAc;EACtC,UAAI+I,SAAJ,EAAe;EACb/I,QAAAA,QAAQ,CAACwJ,MAAT;EACD,OAFD,MAEO;EACLxJ,QAAAA,QAAQ,CAACyJ,OAAT;EACD;EACF,KAND;EAOD;;EAEDsG,EAAAA,eAAe,CAAC,KAAD,CAAf;EACAD,EAAAA,aAAa;EAEb,MAAME,SAAiB,GAAG;EACxB9V,IAAAA,EADwB,gBACnB;EACH,aAAO;EACL6J,QAAAA,SADK,uBACa;EAChBgM,UAAAA,eAAe,CAAC,IAAD,CAAf;EACD,SAHI;EAIL1L,QAAAA,SAJK,qBAIKrE,QAJL,EAIe1C,KAJf,EAI4B;EAC/B,cAAMiO,MAAM,GAAGjO,KAAK,CAACqL,aAArB;EACA,cAAMtP,KAAK,GAAGuW,UAAU,CAAC9V,OAAX,CAAmByR,MAAnB,CAAd,CAF+B;;EAK/B,cAAIA,MAAM,KAAK5C,aAAf,EAA8B;EAC5B;EACD;;EAEDA,UAAAA,aAAa,GAAG4C,MAAhB;EAEA,cAAM0E,aAAa,GAAG,CAACJ,SAAS,IAAI,EAAd,EACnB5U,MADmB,CACZ,SADY,EAEnBY,MAFmB,CAEZ,UAACC,GAAD,EAAMkK,IAAN,EAAe;EACpBlK,YAAAA,GAAD,CAAakK,IAAb,IAAqB2J,iBAAiB,CAACtW,KAAD,CAAjB,CAAyBuE,KAAzB,CAA+BoI,IAA/B,CAArB;EACA,mBAAOlK,GAAP;EACD,WALmB,EAKjB,EALiB,CAAtB;EAOAkE,UAAAA,QAAQ,CAACoJ,QAAT,mBACK6G,aADL;EAEE3M,YAAAA,sBAAsB,EAAE;EAAA,qBAAMiI,MAAM,CAACuB,qBAAP,EAAN;EAAA;EAF1B;EAID;EA1BI,OAAP;EA4BD;EA9BuB,GAA1B;EAiCA,MAAM9M,QAAQ,GAAG8O,KAAK,CAAC9S,GAAG,EAAJ,oBACjBxB,gBAAgB,CAACuU,aAAD,EAAgB,CAAC,WAAD,CAAhB,CADC;EAEpBvK,IAAAA,OAAO,GAAGwL,SAAH,SAAkBjB,aAAa,CAACvK,OAAd,IAAyB,EAA3C,CAFa;EAGpBM,IAAAA,aAAa,EAAE8K;EAHK,KAAtB;EAMA,MAAMM,gBAAgB,GAAGlQ,QAAQ,CAACoJ,QAAlC;;EAEApJ,EAAAA,QAAQ,CAACoJ,QAAT,GAAoB,UAACxL,KAAD,EAAiB;EACnCiS,IAAAA,SAAS,GAAGjS,KAAK,CAACiS,SAAN,IAAmBA,SAA/B;EACAK,IAAAA,gBAAgB,CAACtS,KAAD,CAAhB;EACD,GAHD;;EAKAoC,EAAAA,QAAQ,CAACmQ,YAAT,GAAwB,UAACC,aAAD,EAAyB;EAC/CL,IAAAA,eAAe,CAAC,IAAD,CAAf;EAEAJ,IAAAA,iBAAiB,GAAGS,aAApB;EAEAL,IAAAA,eAAe,CAAC,KAAD,CAAf;EACAD,IAAAA,aAAa;EAEb9P,IAAAA,QAAQ,CAACoJ,QAAT,CAAkB;EAACtE,MAAAA,aAAa,EAAE8K;EAAhB,KAAlB;EACD,GATD;;EAWA,SAAO5P,QAAP;EACD,CAhGD;;ECLA,IAAMqQ,mBAAmB,GAAG;EAC1BC,EAAAA,SAAS,EAAE,YADe;EAE1BC,EAAAA,OAAO,EAAE,OAFiB;EAG1BC,EAAAA,KAAK,EAAE;EAHmB,CAA5B;EAMA;;;;;EAIA,SAASC,QAAT,CACE9O,OADF,EAEE/D,KAFF,EAGyB;EACvB;EACA,EAAa;EACX4D,IAAAA,SAAS,CACP,EAAE5D,KAAK,IAAIA,KAAK,CAAC2N,MAAjB,CADO,EAEP,CACE,4EADF,EAEE,kDAFF,EAGEjL,IAHF,CAGO,GAHP,CAFO,CAAT;EAOD;;EAED,MAAIkI,SAA2B,GAAG,EAAlC;EACA,MAAIkI,mBAA+B,GAAG,EAAtC;EAbuB,MAehBnF,MAfgB,GAeN3N,KAfM,CAehB2N,MAfgB;EAiBvB,MAAMoF,WAAW,GAAGnW,gBAAgB,CAACoD,KAAD,EAAQ,CAAC,QAAD,CAAR,CAApC;EACA,MAAMgT,WAAW,qBAAOD,WAAP;EAAoB9L,IAAAA,OAAO,EAAE,QAA7B;EAAuCD,IAAAA,KAAK,EAAE;EAA9C,IAAjB;EACA,MAAMiM,UAAU,qBAAOF,WAAP;EAAoBhM,IAAAA,YAAY,EAAE;EAAlC,IAAhB;EAEA,MAAMmM,WAAW,GAAGhC,KAAK,CAACnN,OAAD,EAAUiP,WAAV,CAAzB;EACA,MAAMG,qBAAqB,GAAG/V,gBAAgB,CAAC8V,WAAD,CAA9C;;EAEA,WAASzM,SAAT,CAAmB/G,KAAnB,EAAuC;EACrC,QAAI,CAACA,KAAK,CAACiO,MAAX,EAAmB;EACjB;EACD;;EAED,QAAMyF,UAAU,GAAI1T,KAAK,CAACiO,MAAP,CAA0B0F,OAA1B,CAAkC1F,MAAlC,CAAnB;;EAEA,QAAI,CAACyF,UAAL,EAAiB;EACf;EACD,KAToC;EAYrC;EACA;EACA;;;EACA,QAAMnM,OAAO,GACXmM,UAAU,CAACtL,YAAX,CAAwB,oBAAxB,KACA9H,KAAK,CAACiH,OADN,IAEA9B,YAAY,CAAC8B,OAHf,CAfqC;;EAqBrC,QAAImM,UAAU,CAAC1U,MAAf,EAAuB;EACrB;EACD;;EAED,QAAIgB,KAAK,CAAC3D,IAAN,KAAe,YAAf,IAA+B,OAAOkX,UAAU,CAACjM,KAAlB,KAA4B,SAA/D,EAA0E;EACxE;EACD;;EAED,QACEtH,KAAK,CAAC3D,IAAN,KAAe,YAAf,IACAkL,OAAO,CAAC/K,OAAR,CAAiBuW,mBAAD,CAA6B/S,KAAK,CAAC3D,IAAnC,CAAhB,CAFF,EAGE;EACA;EACD;;EAED,QAAMqG,QAAQ,GAAG8O,KAAK,CAACkC,UAAD,EAAaH,UAAb,CAAtB;;EAEA,QAAI7Q,QAAJ,EAAc;EACZ0Q,MAAAA,mBAAmB,GAAGA,mBAAmB,CAACzV,MAApB,CAA2B+E,QAA3B,CAAtB;EACD;EACF;;EAED,WAASiM,EAAT,CACEjF,IADF,EAEEkF,SAFF,EAGEC,OAHF,EAIEC,OAJF,EAKQ;EAAA,QADNA,OACM;EADNA,MAAAA,OACM,GADsB,KACtB;EAAA;;EACNpF,IAAAA,IAAI,CAACtH,gBAAL,CAAsBwM,SAAtB,EAAiCC,OAAjC,EAA0CC,OAA1C;EACA5D,IAAAA,SAAS,CAACpN,IAAV,CAAe;EAAC4L,MAAAA,IAAI,EAAJA,IAAD;EAAOkF,MAAAA,SAAS,EAATA,SAAP;EAAkBC,MAAAA,OAAO,EAAPA,OAAlB;EAA2BC,MAAAA,OAAO,EAAPA;EAA3B,KAAf;EACD;;EAED,WAAS8E,iBAAT,CAA2BlR,QAA3B,EAAqD;EAAA,QAC5CzD,SAD4C,GAC/ByD,QAD+B,CAC5CzD,SAD4C;EAGnD0P,IAAAA,EAAE,CAAC1P,SAAD,EAAY,YAAZ,EAA0B8H,SAA1B,CAAF;EACA4H,IAAAA,EAAE,CAAC1P,SAAD,EAAY,WAAZ,EAAyB8H,SAAzB,CAAF;EACA4H,IAAAA,EAAE,CAAC1P,SAAD,EAAY,SAAZ,EAAuB8H,SAAvB,CAAF;EACA4H,IAAAA,EAAE,CAAC1P,SAAD,EAAY,OAAZ,EAAqB8H,SAArB,CAAF;EACD;;EAED,WAAS8M,oBAAT,GAAsC;EACpC3I,IAAAA,SAAS,CAAC7N,OAAV,CAAkB,gBAAyD;EAAA,UAAvDqM,IAAuD,QAAvDA,IAAuD;EAAA,UAAjDkF,SAAiD,QAAjDA,SAAiD;EAAA,UAAtCC,OAAsC,QAAtCA,OAAsC;EAAA,UAA7BC,OAA6B,QAA7BA,OAA6B;EACzEpF,MAAAA,IAAI,CAACnH,mBAAL,CAAyBqM,SAAzB,EAAoCC,OAApC,EAA6CC,OAA7C;EACD,KAFD;EAGA5D,IAAAA,SAAS,GAAG,EAAZ;EACD;;EAED,WAAS4I,cAAT,CAAwBpR,QAAxB,EAAkD;EAChD,QAAMqR,eAAe,GAAGrR,QAAQ,CAAC2J,OAAjC;;EACA3J,IAAAA,QAAQ,CAAC2J,OAAT,GAAmB,UAAC2H,2BAAD,EAA8C;EAAA,UAA7CA,2BAA6C;EAA7CA,QAAAA,2BAA6C,GAAf,IAAe;EAAA;;EAC/D,UAAIA,2BAAJ,EAAiC;EAC/BZ,QAAAA,mBAAmB,CAAC/V,OAApB,CAA4B,UAACqF,QAAD,EAAc;EACxCA,UAAAA,QAAQ,CAAC2J,OAAT;EACD,SAFD;EAGD;;EAED+G,MAAAA,mBAAmB,GAAG,EAAtB;EAEAS,MAAAA,oBAAoB;EACpBE,MAAAA,eAAe;EAChB,KAXD;;EAaAH,IAAAA,iBAAiB,CAAClR,QAAD,CAAjB;EACD;;EAED+Q,EAAAA,qBAAqB,CAACpW,OAAtB,CAA8ByW,cAA9B;EAEA,SAAON,WAAP;EACD;;EChID,IAAM7O,WAAwB,GAAG;EAC/BqD,EAAAA,IAAI,EAAE,aADyB;EAE/BhM,EAAAA,YAAY,EAAE,KAFiB;EAG/BY,EAAAA,EAH+B,cAG5B8F,QAH4B,EAGlB;EAAA;;EACX;EACA,QAAI,2BAACA,QAAQ,CAACpC,KAAT,CAAe8G,MAAhB,qBAAC,sBAAuB+C,OAAxB,CAAJ,EAAqC;EACnC,MAAa;EACXjG,QAAAA,SAAS,CACPxB,QAAQ,CAACpC,KAAT,CAAeqE,WADR,EAEP,gEAFO,CAAT;EAID;;EAED,aAAO,EAAP;EACD;;EAXU,uBAaYyE,WAAW,CAAC1G,QAAQ,CAAC2G,MAAV,CAbvB;EAAA,QAaJ1H,GAbI,gBAaJA,GAbI;EAAA,QAaCwD,OAbD,gBAaCA,OAbD;;EAeX,QAAM0E,QAAQ,GAAGnH,QAAQ,CAACpC,KAAT,CAAeqE,WAAf,GACbsP,qBAAqB,EADR,GAEb,IAFJ;EAIA,WAAO;EACLzN,MAAAA,QADK,sBACY;EACf,YAAIqD,QAAJ,EAAc;EACZlI,UAAAA,GAAG,CAACpH,YAAJ,CAAiBsP,QAAjB,EAA2BlI,GAAG,CAAC2H,iBAA/B;EACA3H,UAAAA,GAAG,CAACxH,YAAJ,CAAiB,kBAAjB,EAAqC,EAArC;EACAwH,UAAAA,GAAG,CAAC5H,KAAJ,CAAUma,QAAV,GAAqB,QAArB;EAEAxR,UAAAA,QAAQ,CAACoJ,QAAT,CAAkB;EAAC5G,YAAAA,KAAK,EAAE,KAAR;EAAeD,YAAAA,SAAS,EAAE;EAA1B,WAAlB;EACD;EACF,OATI;EAUL2B,MAAAA,OAVK,qBAUW;EACd,YAAIiD,QAAJ,EAAc;EAAA,cACLtK,kBADK,GACiBoC,GAAG,CAAC5H,KADrB,CACLwF,kBADK;EAEZ,cAAMwG,QAAQ,GAAGoO,MAAM,CAAC5U,kBAAkB,CAAC6D,OAAnB,CAA2B,IAA3B,EAAiC,EAAjC,CAAD,CAAvB,CAFY;EAKZ;EACA;;EACA+B,UAAAA,OAAO,CAACpL,KAAR,CAAcqa,eAAd,GAAmCC,IAAI,CAACC,KAAL,CAAWvO,QAAQ,GAAG,EAAtB,CAAnC;EAEA8D,UAAAA,QAAQ,CAAC9P,KAAT,CAAewF,kBAAf,GAAoCA,kBAApC;EACAC,UAAAA,kBAAkB,CAAC,CAACqK,QAAD,CAAD,EAAa,SAAb,CAAlB;EACD;EACF,OAvBI;EAwBLhD,MAAAA,MAxBK,oBAwBU;EACb,YAAIgD,QAAJ,EAAc;EACZA,UAAAA,QAAQ,CAAC9P,KAAT,CAAewF,kBAAf,GAAoC,KAApC;EACD;EACF,OA5BI;EA6BLoH,MAAAA,MA7BK,oBA6BU;EACb,YAAIkD,QAAJ,EAAc;EACZrK,UAAAA,kBAAkB,CAAC,CAACqK,QAAD,CAAD,EAAa,QAAb,CAAlB;EACD;EACF;EAjCI,KAAP;EAmCD;EAzD8B,CAAjC;AA4DA;EAEA,SAASoK,qBAAT,GAAiD;EAC/C,MAAMpK,QAAQ,GAAGnL,GAAG,EAApB;EACAmL,EAAAA,QAAQ,CAACX,SAAT,GAAqB/N,cAArB;EACAqE,EAAAA,kBAAkB,CAAC,CAACqK,QAAD,CAAD,EAAa,QAAb,CAAlB;EACA,SAAOA,QAAP;EACD;;ECtED,IAAI0K,WAAW,GAAG;EAACtU,EAAAA,OAAO,EAAE,CAAV;EAAaC,EAAAA,OAAO,EAAE;EAAtB,CAAlB;EACA,IAAIsU,eAA2D,GAAG,EAAlE;;EAEA,SAASC,gBAAT,OAAgE;EAAA,MAArCxU,OAAqC,QAArCA,OAAqC;EAAA,MAA5BC,OAA4B,QAA5BA,OAA4B;EAC9DqU,EAAAA,WAAW,GAAG;EAACtU,IAAAA,OAAO,EAAPA,OAAD;EAAUC,IAAAA,OAAO,EAAPA;EAAV,GAAd;EACD;;EAED,SAASwU,sBAAT,CAAgCpJ,GAAhC,EAAqD;EACnDA,EAAAA,GAAG,CAAClJ,gBAAJ,CAAqB,WAArB,EAAkCqS,gBAAlC;EACD;;EAED,SAASE,yBAAT,CAAmCrJ,GAAnC,EAAwD;EACtDA,EAAAA,GAAG,CAAC/I,mBAAJ,CAAwB,WAAxB,EAAqCkS,gBAArC;EACD;;EAED,IAAM7P,YAA0B,GAAG;EACjCoD,EAAAA,IAAI,EAAE,cAD2B;EAEjChM,EAAAA,YAAY,EAAE,KAFmB;EAGjCY,EAAAA,EAHiC,cAG9B8F,QAH8B,EAGpB;EACX,QAAMzD,SAAS,GAAGyD,QAAQ,CAACzD,SAA3B;EACA,QAAMqM,GAAG,GAAG5L,gBAAgB,CAACgD,QAAQ,CAACpC,KAAT,CAAekH,aAAf,IAAgCvI,SAAjC,CAA5B;EAEA,QAAI2V,gBAAgB,GAAG,KAAvB;EACA,QAAIC,aAAa,GAAG,KAApB;EACA,QAAIC,WAAW,GAAG,IAAlB;EACA,QAAI/K,SAAS,GAAGrH,QAAQ,CAACpC,KAAzB;;EAEA,aAASyU,oBAAT,GAAyC;EACvC,aACErS,QAAQ,CAACpC,KAAT,CAAesE,YAAf,KAAgC,SAAhC,IAA6ClC,QAAQ,CAACjD,KAAT,CAAemD,SAD9D;EAGD;;EAED,aAASoS,WAAT,GAA6B;EAC3B1J,MAAAA,GAAG,CAAClJ,gBAAJ,CAAqB,WAArB,EAAkCgJ,WAAlC;EACD;;EAED,aAAS6J,cAAT,GAAgC;EAC9B3J,MAAAA,GAAG,CAAC/I,mBAAJ,CAAwB,WAAxB,EAAqC6I,WAArC;EACD;;EAED,aAAS8J,2BAAT,GAA6C;EAC3CN,MAAAA,gBAAgB,GAAG,IAAnB;EACAlS,MAAAA,QAAQ,CAACoJ,QAAT,CAAkB;EAAC9F,QAAAA,sBAAsB,EAAE;EAAzB,OAAlB;EACA4O,MAAAA,gBAAgB,GAAG,KAAnB;EACD;;EAED,aAASxJ,WAAT,CAAqBpL,KAArB,EAA8C;EAC5C;EACA;EACA,UAAMmV,qBAAqB,GAAGnV,KAAK,CAACiO,MAAN,GAC1BhP,SAAS,CAAC2K,QAAV,CAAmB5J,KAAK,CAACiO,MAAzB,CAD0B,GAE1B,IAFJ;EAH4C,UAMrCrJ,YANqC,GAMrBlC,QAAQ,CAACpC,KANY,CAMrCsE,YANqC;EAAA,UAOrC3E,OAPqC,GAOjBD,KAPiB,CAOrCC,OAPqC;EAAA,UAO5BC,OAP4B,GAOjBF,KAPiB,CAO5BE,OAP4B;EAS5C,UAAMkV,IAAI,GAAGnW,SAAS,CAACuQ,qBAAV,EAAb;EACA,UAAM6F,SAAS,GAAGpV,OAAO,GAAGmV,IAAI,CAAClU,IAAjC;EACA,UAAMoU,SAAS,GAAGpV,OAAO,GAAGkV,IAAI,CAACvU,GAAjC;;EAEA,UAAIsU,qBAAqB,IAAI,CAACzS,QAAQ,CAACpC,KAAT,CAAe6F,WAA7C,EAA0D;EACxDzD,QAAAA,QAAQ,CAACoJ,QAAT,CAAkB;EAChB9F,UAAAA,sBADgB,oCACS;EACvB,gBAAMoP,IAAI,GAAGnW,SAAS,CAACuQ,qBAAV,EAAb;EAEA,gBAAIrO,CAAC,GAAGlB,OAAR;EACA,gBAAIa,CAAC,GAAGZ,OAAR;;EAEA,gBAAI0E,YAAY,KAAK,SAArB,EAAgC;EAC9BzD,cAAAA,CAAC,GAAGiU,IAAI,CAAClU,IAAL,GAAYmU,SAAhB;EACAvU,cAAAA,CAAC,GAAGsU,IAAI,CAACvU,GAAL,GAAWyU,SAAf;EACD;;EAED,gBAAMzU,GAAG,GAAG+D,YAAY,KAAK,YAAjB,GAAgCwQ,IAAI,CAACvU,GAArC,GAA2CC,CAAvD;EACA,gBAAMO,KAAK,GAAGuD,YAAY,KAAK,UAAjB,GAA8BwQ,IAAI,CAAC/T,KAAnC,GAA2CF,CAAzD;EACA,gBAAMH,MAAM,GAAG4D,YAAY,KAAK,YAAjB,GAAgCwQ,IAAI,CAACpU,MAArC,GAA8CF,CAA7D;EACA,gBAAMI,IAAI,GAAG0D,YAAY,KAAK,UAAjB,GAA8BwQ,IAAI,CAAClU,IAAnC,GAA0CC,CAAvD;EAEA,mBAAO;EACLoU,cAAAA,KAAK,EAAElU,KAAK,GAAGH,IADV;EAELsU,cAAAA,MAAM,EAAExU,MAAM,GAAGH,GAFZ;EAGLA,cAAAA,GAAG,EAAHA,GAHK;EAILQ,cAAAA,KAAK,EAALA,KAJK;EAKLL,cAAAA,MAAM,EAANA,MALK;EAMLE,cAAAA,IAAI,EAAJA;EANK,aAAP;EAQD;EAzBe,SAAlB;EA2BD;EACF;;EAED,aAASuU,MAAT,GAAwB;EACtB,UAAI/S,QAAQ,CAACpC,KAAT,CAAesE,YAAnB,EAAiC;EAC/B4P,QAAAA,eAAe,CAAC1W,IAAhB,CAAqB;EAAC4E,UAAAA,QAAQ,EAARA,QAAD;EAAW4I,UAAAA,GAAG,EAAHA;EAAX,SAArB;EACAoJ,QAAAA,sBAAsB,CAACpJ,GAAD,CAAtB;EACD;EACF;;EAED,aAASe,OAAT,GAAyB;EACvBmI,MAAAA,eAAe,GAAGA,eAAe,CAAChX,MAAhB,CAChB,UAACkY,IAAD;EAAA,eAAUA,IAAI,CAAChT,QAAL,KAAkBA,QAA5B;EAAA,OADgB,CAAlB;;EAIA,UAAI8R,eAAe,CAAChX,MAAhB,CAAuB,UAACkY,IAAD;EAAA,eAAUA,IAAI,CAACpK,GAAL,KAAaA,GAAvB;EAAA,OAAvB,EAAmDzC,MAAnD,KAA8D,CAAlE,EAAqE;EACnE8L,QAAAA,yBAAyB,CAACrJ,GAAD,CAAzB;EACD;EACF;;EAED,WAAO;EACL9E,MAAAA,QAAQ,EAAEiP,MADL;EAELhP,MAAAA,SAAS,EAAE4F,OAFN;EAGL9F,MAAAA,cAHK,4BAGkB;EACrBwD,QAAAA,SAAS,GAAGrH,QAAQ,CAACpC,KAArB;EACD,OALI;EAMLgG,MAAAA,aANK,yBAMSqP,CANT,SAMkC;EAAA,YAArB/Q,YAAqB,SAArBA,YAAqB;;EACrC,YAAIgQ,gBAAJ,EAAsB;EACpB;EACD;;EAED,YACEhQ,YAAY,KAAKnG,SAAjB,IACAsL,SAAS,CAACnF,YAAV,KAA2BA,YAF7B,EAGE;EACAyH,UAAAA,OAAO;;EAEP,cAAIzH,YAAJ,EAAkB;EAChB6Q,YAAAA,MAAM;;EAEN,gBACE/S,QAAQ,CAACjD,KAAT,CAAekM,SAAf,IACA,CAACkJ,aADD,IAEA,CAACE,oBAAoB,EAHvB,EAIE;EACAC,cAAAA,WAAW;EACZ;EACF,WAVD,MAUO;EACLC,YAAAA,cAAc;EACdC,YAAAA,2BAA2B;EAC5B;EACF;EACF,OAhCI;EAiCLtO,MAAAA,OAjCK,qBAiCW;EACd,YAAIlE,QAAQ,CAACpC,KAAT,CAAesE,YAAnB,EAAiC;EAC/B,cAAIkQ,WAAJ,EAAiB;EACf1J,YAAAA,WAAW,CAACmJ,WAAD,CAAX;EACAO,YAAAA,WAAW,GAAG,KAAd;EACD;;EAED,cAAI,CAACD,aAAD,IAAkB,CAACE,oBAAoB,EAA3C,EAA+C;EAC7CC,YAAAA,WAAW;EACZ;EACF;EACF,OA5CI;EA6CLjO,MAAAA,SA7CK,qBA6CK4O,CA7CL,SA6CsB;EAAA,YAAbtZ,IAAa,SAAbA,IAAa;EACzBwY,QAAAA,aAAa,GAAGxY,IAAI,KAAK,OAAzB;EACD,OA/CI;EAgDLqK,MAAAA,QAhDK,sBAgDY;EACf,YAAIhE,QAAQ,CAACpC,KAAT,CAAesE,YAAnB,EAAiC;EAC/BsQ,UAAAA,2BAA2B;EAC3BD,UAAAA,cAAc;EACdH,UAAAA,WAAW,GAAG,IAAd;EACD;EACF;EAtDI,KAAP;EAwDD;EArJgC,CAAnC;;ECbA,SAASc,QAAT,CAAkBtV,KAAlB,EAAgCuV,QAAhC,EAA8E;EAAA;;EAC5E,SAAO;EACL1O,IAAAA,aAAa,oBACR7G,KAAK,CAAC6G,aADE;EAEXiJ,MAAAA,SAAS,YACJ,CAAC,yBAAA9P,KAAK,CAAC6G,aAAN,0CAAqBiJ,SAArB,KAAkC,EAAnC,EAAuC5S,MAAvC,CACD;EAAA,YAAEwK,IAAF,QAAEA,IAAF;EAAA,eAAYA,IAAI,KAAK6N,QAAQ,CAAC7N,IAA9B;EAAA,OADC,CADI,GAIP6N,QAJO;EAFE;EADR,GAAP;EAWD;;EAED,IAAMhR,iBAAoC,GAAG;EAC3CmD,EAAAA,IAAI,EAAE,mBADqC;EAE3ChM,EAAAA,YAAY,EAAE,KAF6B;EAG3CY,EAAAA,EAH2C,cAGxC8F,QAHwC,EAG9B;EAAA,QACJzD,SADI,GACSyD,QADT,CACJzD,SADI;;EAGX,aAASwM,SAAT,GAA8B;EAC5B,aAAO,CAAC,CAAC/I,QAAQ,CAACpC,KAAT,CAAeuE,iBAAxB;EACD;;EAED,QAAI3G,SAAJ;EACA,QAAI4X,eAAe,GAAG,CAAC,CAAvB;EACA,QAAIlB,gBAAgB,GAAG,KAAvB;EAEA,QAAMiB,QAAgD,GAAG;EACvD7N,MAAAA,IAAI,EAAE,wBADiD;EAEvDgI,MAAAA,OAAO,EAAE,IAF8C;EAGvDC,MAAAA,KAAK,EAAE,YAHgD;EAIvDrT,MAAAA,EAJuD,qBAI3C;EAAA,YAAR6C,KAAQ,SAARA,KAAQ;;EACV,YAAIgM,SAAS,EAAb,EAAiB;EACf,cAAIvN,SAAS,KAAKuB,KAAK,CAACvB,SAAxB,EAAmC;EACjCwE,YAAAA,QAAQ,CAACoJ,QAAT,CAAkB;EAChB9F,cAAAA,sBAAsB,EAAE;EAAA,uBACtBA,uBAAsB,CAACvG,KAAK,CAACvB,SAAP,CADA;EAAA;EADR,aAAlB;EAID;;EAEDA,UAAAA,SAAS,GAAGuB,KAAK,CAACvB,SAAlB;EACD;EACF;EAfsD,KAAzD;;EAkBA,aAAS8H,uBAAT,CAAgC9H,SAAhC,EAAkE;EAChE,aAAO6X,2BAA2B,CAChC9X,gBAAgB,CAACC,SAAD,CADgB,EAEhCe,SAAS,CAACuQ,qBAAV,EAFgC,EAGhCrR,SAAS,CAACc,SAAS,CAAC+W,cAAV,EAAD,CAHuB,EAIhCF,eAJgC,CAAlC;EAMD;;EAED,aAASG,gBAAT,CAA0BtO,YAA1B,EAA8D;EAC5DiN,MAAAA,gBAAgB,GAAG,IAAnB;EACAlS,MAAAA,QAAQ,CAACoJ,QAAT,CAAkBnE,YAAlB;EACAiN,MAAAA,gBAAgB,GAAG,KAAnB;EACD;;EAED,aAASsB,WAAT,GAA6B;EAC3B,UAAI,CAACtB,gBAAL,EAAuB;EACrBqB,QAAAA,gBAAgB,CAACL,QAAQ,CAAClT,QAAQ,CAACpC,KAAV,EAAiBuV,QAAjB,CAAT,CAAhB;EACD;EACF;;EAED,WAAO;EACLrP,MAAAA,QAAQ,EAAE0P,WADL;EAEL5P,MAAAA,aAAa,EAAE4P,WAFV;EAGLnP,MAAAA,SAHK,qBAGK4O,CAHL,EAGQ3V,KAHR,EAGqB;EACxB,YAAIlB,YAAY,CAACkB,KAAD,CAAhB,EAAyB;EACvB,cAAMmW,KAAK,GAAGhY,SAAS,CAACuE,QAAQ,CAACzD,SAAT,CAAmB+W,cAAnB,EAAD,CAAvB;EACA,cAAMI,UAAU,GAAGD,KAAK,CAAC1M,IAAN,CACjB,UAAC2L,IAAD;EAAA,mBACEA,IAAI,CAAClU,IAAL,GAAY,CAAZ,IAAiBlB,KAAK,CAACC,OAAvB,IACAmV,IAAI,CAAC/T,KAAL,GAAa,CAAb,IAAkBrB,KAAK,CAACC,OADxB,IAEAmV,IAAI,CAACvU,GAAL,GAAW,CAAX,IAAgBb,KAAK,CAACE,OAFtB,IAGAkV,IAAI,CAACpU,MAAL,GAAc,CAAd,IAAmBhB,KAAK,CAACE,OAJ3B;EAAA,WADiB,CAAnB;EAQA4V,UAAAA,eAAe,GAAGK,KAAK,CAAC3Z,OAAN,CAAc4Z,UAAd,CAAlB;EACD;EACF,OAhBI;EAiBLpP,MAAAA,WAjBK,yBAiBe;EAClB8O,QAAAA,eAAe,GAAG,CAAC,CAAnB;EACD;EAnBI,KAAP;EAqBD;EA1E0C,CAA7C;AA6EA,EAEO,SAASC,2BAAT,CACLM,oBADK,EAELC,YAFK,EAGLC,WAHK,EAILT,eAJK,EAKO;EACZ;EACA,MAAIS,WAAW,CAAC1N,MAAZ,GAAqB,CAArB,IAA0BwN,oBAAoB,KAAK,IAAvD,EAA6D;EAC3D,WAAOC,YAAP;EACD,GAJW;;;EAOZ,MACEC,WAAW,CAAC1N,MAAZ,KAAuB,CAAvB,IACAiN,eAAe,IAAI,CADnB,IAEAS,WAAW,CAAC,CAAD,CAAX,CAAerV,IAAf,GAAsBqV,WAAW,CAAC,CAAD,CAAX,CAAelV,KAHvC,EAIE;EACA,WAAOkV,WAAW,CAACT,eAAD,CAAX,IAAgCQ,YAAvC;EACD;;EAED,UAAQD,oBAAR;EACE,SAAK,KAAL;EACA,SAAK,QAAL;EAAe;EACb,YAAMG,SAAS,GAAGD,WAAW,CAAC,CAAD,CAA7B;EACA,YAAME,QAAQ,GAAGF,WAAW,CAACA,WAAW,CAAC1N,MAAZ,GAAqB,CAAtB,CAA5B;EACA,YAAM6N,KAAK,GAAGL,oBAAoB,KAAK,KAAvC;EAEA,YAAMxV,GAAG,GAAG2V,SAAS,CAAC3V,GAAtB;EACA,YAAMG,MAAM,GAAGyV,QAAQ,CAACzV,MAAxB;EACA,YAAME,IAAI,GAAGwV,KAAK,GAAGF,SAAS,CAACtV,IAAb,GAAoBuV,QAAQ,CAACvV,IAA/C;EACA,YAAMG,KAAK,GAAGqV,KAAK,GAAGF,SAAS,CAACnV,KAAb,GAAqBoV,QAAQ,CAACpV,KAAjD;EACA,YAAMkU,KAAK,GAAGlU,KAAK,GAAGH,IAAtB;EACA,YAAMsU,MAAM,GAAGxU,MAAM,GAAGH,GAAxB;EAEA,eAAO;EAACA,UAAAA,GAAG,EAAHA,GAAD;EAAMG,UAAAA,MAAM,EAANA,MAAN;EAAcE,UAAAA,IAAI,EAAJA,IAAd;EAAoBG,UAAAA,KAAK,EAALA,KAApB;EAA2BkU,UAAAA,KAAK,EAALA,KAA3B;EAAkCC,UAAAA,MAAM,EAANA;EAAlC,SAAP;EACD;;EACD,SAAK,MAAL;EACA,SAAK,OAAL;EAAc;EACZ,YAAMmB,OAAO,GAAGtC,IAAI,CAACuC,GAAL,OAAAvC,IAAI,EAAQkC,WAAW,CAAChK,GAAZ,CAAgB,UAAC4J,KAAD;EAAA,iBAAWA,KAAK,CAACjV,IAAjB;EAAA,SAAhB,CAAR,CAApB;EACA,YAAM2V,QAAQ,GAAGxC,IAAI,CAACyC,GAAL,OAAAzC,IAAI,EAAQkC,WAAW,CAAChK,GAAZ,CAAgB,UAAC4J,KAAD;EAAA,iBAAWA,KAAK,CAAC9U,KAAjB;EAAA,SAAhB,CAAR,CAArB;EACA,YAAM0V,YAAY,GAAGR,WAAW,CAAC/Y,MAAZ,CAAmB,UAAC4X,IAAD;EAAA,iBACtCiB,oBAAoB,KAAK,MAAzB,GACIjB,IAAI,CAAClU,IAAL,KAAcyV,OADlB,GAEIvB,IAAI,CAAC/T,KAAL,KAAewV,QAHmB;EAAA,SAAnB,CAArB;EAMA,YAAMhW,IAAG,GAAGkW,YAAY,CAAC,CAAD,CAAZ,CAAgBlW,GAA5B;EACA,YAAMG,OAAM,GAAG+V,YAAY,CAACA,YAAY,CAAClO,MAAb,GAAsB,CAAvB,CAAZ,CAAsC7H,MAArD;EACA,YAAME,KAAI,GAAGyV,OAAb;EACA,YAAMtV,MAAK,GAAGwV,QAAd;;EACA,YAAMtB,MAAK,GAAGlU,MAAK,GAAGH,KAAtB;;EACA,YAAMsU,OAAM,GAAGxU,OAAM,GAAGH,IAAxB;;EAEA,eAAO;EAACA,UAAAA,GAAG,EAAHA,IAAD;EAAMG,UAAAA,MAAM,EAANA,OAAN;EAAcE,UAAAA,IAAI,EAAJA,KAAd;EAAoBG,UAAAA,KAAK,EAALA,MAApB;EAA2BkU,UAAAA,KAAK,EAALA,MAA3B;EAAkCC,UAAAA,MAAM,EAANA;EAAlC,SAAP;EACD;;EACD;EAAS;EACP,eAAOc,YAAP;EACD;EArCH;EAuCD;;EC1JD,IAAMxR,MAAc,GAAG;EACrBkD,EAAAA,IAAI,EAAE,QADe;EAErBhM,EAAAA,YAAY,EAAE,KAFO;EAGrBY,EAAAA,EAHqB,cAGlB8F,QAHkB,EAGR;EAAA,QACJzD,SADI,GACiByD,QADjB,CACJzD,SADI;EAAA,QACOoK,MADP,GACiB3G,QADjB,CACO2G,MADP;;EAGX,aAAS2N,YAAT,GAA2D;EACzD,aAAOtU,QAAQ,CAAC8I,cAAT,GACH9I,QAAQ,CAAC8I,cAAT,CAAwB/L,KAAxB,CAA8BiS,QAA9B,CAAuCzS,SADpC,GAEHA,SAFJ;EAGD;;EAED,aAASgY,WAAT,CAAqBnb,KAArB,EAA6D;EAC3D,aAAO4G,QAAQ,CAACpC,KAAT,CAAewE,MAAf,KAA0B,IAA1B,IAAkCpC,QAAQ,CAACpC,KAAT,CAAewE,MAAf,KAA0BhJ,KAAnE;EACD;;EAED,QAAIob,WAA8B,GAAG,IAArC;EACA,QAAIC,WAA8B,GAAG,IAArC;;EAEA,aAASC,cAAT,GAAgC;EAC9B,UAAMC,cAAc,GAAGJ,WAAW,CAAC,WAAD,CAAX,GACnBD,YAAY,GAAGxH,qBAAf,EADmB,GAEnB,IAFJ;EAGA,UAAM8H,cAAc,GAAGL,WAAW,CAAC,QAAD,CAAX,GACnB5N,MAAM,CAACmG,qBAAP,EADmB,GAEnB,IAFJ;;EAIA,UACG6H,cAAc,IAAIE,iBAAiB,CAACL,WAAD,EAAcG,cAAd,CAApC,IACCC,cAAc,IAAIC,iBAAiB,CAACJ,WAAD,EAAcG,cAAd,CAFtC,EAGE;EACA,YAAI5U,QAAQ,CAAC8I,cAAb,EAA6B;EAC3B9I,UAAAA,QAAQ,CAAC8I,cAAT,CAAwBgM,MAAxB;EACD;EACF;;EAEDN,MAAAA,WAAW,GAAGG,cAAd;EACAF,MAAAA,WAAW,GAAGG,cAAd;;EAEA,UAAI5U,QAAQ,CAACjD,KAAT,CAAekM,SAAnB,EAA8B;EAC5BiF,QAAAA,qBAAqB,CAACwG,cAAD,CAArB;EACD;EACF;;EAED,WAAO;EACLxQ,MAAAA,OADK,qBACW;EACd,YAAIlE,QAAQ,CAACpC,KAAT,CAAewE,MAAnB,EAA2B;EACzBsS,UAAAA,cAAc;EACf;EACF;EALI,KAAP;EAOD;EAnDoB,CAAvB;AAsDA;EAEA,SAASG,iBAAT,CACEE,KADF,EAEEC,KAFF,EAGW;EACT,MAAID,KAAK,IAAIC,KAAb,EAAoB;EAClB,WACED,KAAK,CAAC5W,GAAN,KAAc6W,KAAK,CAAC7W,GAApB,IACA4W,KAAK,CAACpW,KAAN,KAAgBqW,KAAK,CAACrW,KADtB,IAEAoW,KAAK,CAACzW,MAAN,KAAiB0W,KAAK,CAAC1W,MAFvB,IAGAyW,KAAK,CAACvW,IAAN,KAAewW,KAAK,CAACxW,IAJvB;EAMD;;EAED,SAAO,IAAP;EACD;;EC5DD,IAAIzG,SAAJ,EAAe;EACbZ,EAAAA,SAAS,CAACC,GAAD,CAAT;EACD;;EAED0X,KAAK,CAAC9J,eAAN,CAAsB;EACpBR,EAAAA,OAAO,EAAE,CAACvC,WAAD,EAAcC,YAAd,EAA4BC,iBAA5B,EAA+CC,MAA/C,CADW;EAEpBsC,EAAAA,MAAM,EAANA;EAFoB,CAAtB;EAKAoK,KAAK,CAACW,eAAN,GAAwBA,eAAxB;EACAX,KAAK,CAAC2B,QAAN,GAAiBA,QAAjB;EACA3B,KAAK,CAACM,OAAN,GAAgBA,OAAhB;EACAN,KAAK,CAACmG,UAAN,GAAmB3c,WAAnB;;;;;;;;"}