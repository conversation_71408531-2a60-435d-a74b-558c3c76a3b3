<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Pager language settings
return [
    'pageNavigation'         => 'Page navigation',
    'first'                  => 'First',
    'previous'               => 'Previous',
    'next'                   => 'Next',
    'last'                   => 'Last',
    'older'                  => 'Older',
    'newer'                  => 'Newer',
    'invalidTemplate'        => '"{0}" is not a valid Pager template.',
    'invalidPaginationGroup' => '"{0}" is not a valid Pagination group.',
];
