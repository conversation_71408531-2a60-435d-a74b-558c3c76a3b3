<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Cast language settings
return [
    'baseCastMissing'        => 'The "{0}" class must inherit the "CodeIgniter\Entity\Cast\BaseCast" class.',
    'invalidCastMethod'      => 'The "{0}" is invalid cast method, valid methods are: ["get", "set"].',
    'invalidTimestamp'       => 'Type casting "timestamp" expects a correct timestamp.',
    'jsonErrorCtrlChar'      => 'Unexpected control character found.',
    'jsonErrorDepth'         => 'Maximum stack depth exceeded.',
    'jsonErrorStateMismatch' => 'Underflow or the modes mismatch.',
    'jsonErrorSyntax'        => 'Syntax error, malformed JSON.',
    'jsonErrorUnknown'       => 'Unknown error.',
    'jsonErrorUtf8'          => 'Malformed UTF-8 characters, possibly incorrectly encoded.',
];
