{"version": 3, "file": "tippy-bundle.umd.min.js", "sources": ["../src/browser.ts", "../src/constants.ts", "../src/utils.ts", "../src/dom-utils.ts", "../src/bindGlobalEventListeners.ts", "../src/props.ts", "../src/template.ts", "../src/createTippy.ts", "../src/index.ts", "../src/addons/delegate.ts", "../src/plugins/animateFill.ts", "../src/plugins/followCursor.ts", "../src/plugins/inlinePositioning.ts", "../src/plugins/sticky.ts", "../build/bundle-umd.js", "../src/css.ts", "../src/addons/createSingleton.ts"], "sourcesContent": ["export const isBrowser =\n  typeof window !== 'undefined' && typeof document !== 'undefined';\n\nconst ua = isBrowser ? navigator.userAgent : '';\n\nexport const isIE = /MSIE |Trident\\//.test(ua);\n", "export const ROUND_ARROW =\n  '<svg width=\"16\" height=\"6\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z\"></svg>';\n\nexport const BOX_CLASS = `__NAMESPACE_PREFIX__-box`;\nexport const CONTENT_CLASS = `__NAMESPACE_PREFIX__-content`;\nexport const BACKDROP_CLASS = `__NAMESPACE_PREFIX__-backdrop`;\nexport const ARROW_CLASS = `__NAMESPACE_PREFIX__-arrow`;\nexport const SVG_ARROW_CLASS = `__NAMESPACE_PREFIX__-svg-arrow`;\n\nexport const TOUCH_OPTIONS = {passive: true, capture: true};\n", "import {BasePlacement, Placement} from './types';\n\nexport function hasOwnProperty(obj: object, key: string): boolean {\n  return {}.hasOwnProperty.call(obj, key);\n}\n\nexport function getValueAtIndexOrReturn<T>(\n  value: T | [T | null, T | null],\n  index: number,\n  defaultValue: T | [T, T]\n): T {\n  if (Array.isArray(value)) {\n    const v = value[index];\n    return v == null\n      ? Array.isArray(defaultValue)\n        ? defaultValue[index]\n        : defaultValue\n      : v;\n  }\n\n  return value;\n}\n\nexport function isType(value: any, type: string): boolean {\n  const str = {}.toString.call(value);\n  return str.indexOf('[object') === 0 && str.indexOf(`${type}]`) > -1;\n}\n\nexport function invokeWithArgsOrReturn(value: any, args: any[]): any {\n  return typeof value === 'function' ? value(...args) : value;\n}\n\nexport function debounce<T>(\n  fn: (arg: T) => void,\n  ms: number\n): (arg: T) => void {\n  // Avoid wrapping in `setTimeout` if ms is 0 anyway\n  if (ms === 0) {\n    return fn;\n  }\n\n  let timeout: any;\n\n  return (arg): void => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => {\n      fn(arg);\n    }, ms);\n  };\n}\n\nexport function removeProperties<T>(obj: T, keys: string[]): Partial<T> {\n  const clone = {...obj};\n  keys.forEach((key) => {\n    delete (clone as any)[key];\n  });\n  return clone;\n}\n\nexport function splitBySpaces(value: string): string[] {\n  return value.split(/\\s+/).filter(Boolean);\n}\n\nexport function normalizeToArray<T>(value: T | T[]): T[] {\n  return ([] as T[]).concat(value);\n}\n\nexport function pushIfUnique<T>(arr: T[], value: T): void {\n  if (arr.indexOf(value) === -1) {\n    arr.push(value);\n  }\n}\n\nexport function appendPxIfNumber(value: string | number): string {\n  return typeof value === 'number' ? `${value}px` : value;\n}\n\nexport function unique<T>(arr: T[]): T[] {\n  return arr.filter((item, index) => arr.indexOf(item) === index);\n}\n\nexport function getNumber(value: string | number): number {\n  return typeof value === 'number' ? value : parseFloat(value);\n}\n\nexport function getBasePlacement(placement: Placement): BasePlacement {\n  return placement.split('-')[0] as BasePlacement;\n}\n\nexport function arrayFrom(value: ArrayLike<any>): any[] {\n  return [].slice.call(value);\n}\n\nexport function removeUndefinedProps(\n  obj: Record<string, unknown>\n): Partial<Record<string, unknown>> {\n  return Object.keys(obj).reduce((acc, key) => {\n    if (obj[key] !== undefined) {\n      (acc as any)[key] = obj[key];\n    }\n\n    return acc;\n  }, {});\n}\n", "import {ReferenceElement, Targets} from './types';\nimport {PopperTreeData} from './types-internal';\nimport {arrayFrom, isType, normalizeToArray, getBasePlacement} from './utils';\n\nexport function div(): HTMLDivElement {\n  return document.createElement('div');\n}\n\nexport function isElement(value: unknown): value is Element | DocumentFragment {\n  return ['Element', 'Fragment'].some((type) => isType(value, type));\n}\n\nexport function isNodeList(value: unknown): value is NodeList {\n  return isType(value, 'NodeList');\n}\n\nexport function isMouseEvent(value: unknown): value is MouseEvent {\n  return isType(value, 'MouseEvent');\n}\n\nexport function isReferenceElement(value: any): value is ReferenceElement {\n  return !!(value && value._tippy && value._tippy.reference === value);\n}\n\nexport function getArrayOfElements(value: Targets): Element[] {\n  if (isElement(value)) {\n    return [value];\n  }\n\n  if (isNodeList(value)) {\n    return arrayFrom(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return arrayFrom(document.querySelectorAll(value));\n}\n\nexport function setTransitionDuration(\n  els: (HTMLDivElement | null)[],\n  value: number\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.style.transitionDuration = `${value}ms`;\n    }\n  });\n}\n\nexport function setVisibilityState(\n  els: (HTMLDivElement | null)[],\n  state: 'visible' | 'hidden'\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.setAttribute('data-state', state);\n    }\n  });\n}\n\nexport function getOwnerDocument(\n  elementOrElements: Element | Element[]\n): Document {\n  const [element] = normalizeToArray(elementOrElements);\n  return element ? element.ownerDocument || document : document;\n}\n\nexport function isCursorOutsideInteractiveBorder(\n  popperTreeData: PopperTreeData[],\n  event: MouseEvent\n): boolean {\n  const {clientX, clientY} = event;\n\n  return popperTreeData.every(({popperRect, popperState, props}) => {\n    const {interactiveBorder} = props;\n    const basePlacement = getBasePlacement(popperState.placement);\n    const offsetData = popperState.modifiersData.offset;\n\n    if (!offsetData) {\n      return true;\n    }\n\n    const topDistance = basePlacement === 'bottom' ? offsetData.top!.y : 0;\n    const bottomDistance = basePlacement === 'top' ? offsetData.bottom!.y : 0;\n    const leftDistance = basePlacement === 'right' ? offsetData.left!.x : 0;\n    const rightDistance = basePlacement === 'left' ? offsetData.right!.x : 0;\n\n    const exceedsTop =\n      popperRect.top - clientY + topDistance > interactiveBorder;\n    const exceedsBottom =\n      clientY - popperRect.bottom - bottomDistance > interactiveBorder;\n    const exceedsLeft =\n      popperRect.left - clientX + leftDistance > interactiveBorder;\n    const exceedsRight =\n      clientX - popperRect.right - rightDistance > interactiveBorder;\n\n    return exceedsTop || exceedsBottom || exceedsLeft || exceedsRight;\n  });\n}\n\nexport function updateTransitionEndListener(\n  box: HTMLDivElement,\n  action: 'add' | 'remove',\n  listener: (event: TransitionEvent) => void\n): void {\n  const method = `${action}EventListener` as\n    | 'addEventListener'\n    | 'removeEventListener';\n\n  // some browsers apparently support `transition` (unprefixed) but only fire\n  // `webkitTransitionEnd`...\n  ['transitionend', 'webkitTransitionEnd'].forEach((event) => {\n    box[method](event, listener as EventListener);\n  });\n}\n", "import {TOUCH_OPTIONS} from './constants';\nimport {isReferenceElement} from './dom-utils';\n\nexport const currentInput = {isTouch: false};\nlet lastMouseMoveTime = 0;\n\n/**\n * When a `touchstart` event is fired, it's assumed the user is using touch\n * input. We'll bind a `mousemove` event listener to listen for mouse input in\n * the future. This way, the `isTouch` property is fully dynamic and will handle\n * hybrid devices that use a mix of touch + mouse input.\n */\nexport function onDocumentTouchStart(): void {\n  if (currentInput.isTouch) {\n    return;\n  }\n\n  currentInput.isTouch = true;\n\n  if (window.performance) {\n    document.addEventListener('mousemove', onDocumentMouseMove);\n  }\n}\n\n/**\n * When two `mousemove` event are fired consecutively within 20ms, it's assumed\n * the user is using mouse input again. `mousemove` can fire on touch devices as\n * well, but very rarely that quickly.\n */\nexport function onDocumentMouseMove(): void {\n  const now = performance.now();\n\n  if (now - lastMouseMoveTime < 20) {\n    currentInput.isTouch = false;\n\n    document.removeEventListener('mousemove', onDocumentMouseMove);\n  }\n\n  lastMouseMoveTime = now;\n}\n\n/**\n * When an element is in focus and has a tippy, leaving the tab/window and\n * returning causes it to show again. For mouse users this is unexpected, but\n * for keyboard use it makes sense.\n * TODO: find a better technique to solve this problem\n */\nexport function onWindowBlur(): void {\n  const activeElement = document.activeElement as HTMLElement | null;\n\n  if (isReferenceElement(activeElement)) {\n    const instance = activeElement._tippy!;\n\n    if (activeElement.blur && !instance.state.isVisible) {\n      activeElement.blur();\n    }\n  }\n}\n\nexport default function bindGlobalEventListeners(): void {\n  document.addEventListener('touchstart', onDocumentTouchStart, TOUCH_OPTIONS);\n  window.addEventListener('blur', onWindowBlur);\n}\n", "import {DefaultProps, Plugin, Props, ReferenceElement, Tippy} from './types';\nimport {\n  hasOwnProperty,\n  removeProperties,\n  invokeWithArgsOrReturn,\n} from './utils';\nimport {warnWhen} from './validation';\n\nconst pluginProps = {\n  animateFill: false,\n  followCursor: false,\n  inlinePositioning: false,\n  sticky: false,\n};\n\nconst renderProps = {\n  allowHTML: false,\n  animation: 'fade',\n  arrow: true,\n  content: '',\n  inertia: false,\n  maxWidth: 350,\n  role: 'tooltip',\n  theme: '',\n  zIndex: 9999,\n};\n\nexport const defaultProps: DefaultProps = {\n  appendTo: () => document.body,\n  aria: {\n    content: 'auto',\n    expanded: 'auto',\n  },\n  delay: 0,\n  duration: [300, 250],\n  getReferenceClientRect: null,\n  hideOnClick: true,\n  ignoreAttributes: false,\n  interactive: false,\n  interactiveBorder: 2,\n  interactiveDebounce: 0,\n  moveTransition: '',\n  offset: [0, 10],\n  onAfterUpdate() {},\n  onBeforeUpdate() {},\n  onCreate() {},\n  onDestroy() {},\n  onHidden() {},\n  onHide() {},\n  onMount() {},\n  onShow() {},\n  onShown() {},\n  onTrigger() {},\n  onUntrigger() {},\n  onClickOutside() {},\n  placement: 'top',\n  plugins: [],\n  popperOptions: {},\n  render: null,\n  showOnCreate: false,\n  touch: true,\n  trigger: 'mouseenter focus',\n  triggerTarget: null,\n  ...pluginProps,\n  ...renderProps,\n};\n\nconst defaultKeys = Object.keys(defaultProps);\n\nexport const setDefaultProps: Tippy['setDefaultProps'] = (partialProps) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateProps(partialProps, []);\n  }\n\n  const keys = Object.keys(partialProps) as Array<keyof DefaultProps>;\n  keys.forEach((key) => {\n    (defaultProps as any)[key] = partialProps[key];\n  });\n};\n\nexport function getExtendedPassedProps(\n  passedProps: Partial<Props> & Record<string, unknown>\n): Partial<Props> {\n  const plugins = passedProps.plugins || [];\n  const pluginProps = plugins.reduce<Record<string, unknown>>((acc, plugin) => {\n    const {name, defaultValue} = plugin;\n\n    if (name) {\n      acc[name] =\n        passedProps[name] !== undefined ? passedProps[name] : defaultValue;\n    }\n\n    return acc;\n  }, {});\n\n  return {\n    ...passedProps,\n    ...pluginProps,\n  };\n}\n\nexport function getDataAttributeProps(\n  reference: ReferenceElement,\n  plugins: Plugin[]\n): Record<string, unknown> {\n  const propKeys = plugins\n    ? Object.keys(getExtendedPassedProps({...defaultProps, plugins}))\n    : defaultKeys;\n\n  const props = propKeys.reduce(\n    (acc: Partial<Props> & Record<string, unknown>, key) => {\n      const valueAsString = (\n        reference.getAttribute(`data-tippy-${key}`) || ''\n      ).trim();\n\n      if (!valueAsString) {\n        return acc;\n      }\n\n      if (key === 'content') {\n        acc[key] = valueAsString;\n      } else {\n        try {\n          acc[key] = JSON.parse(valueAsString);\n        } catch (e) {\n          acc[key] = valueAsString;\n        }\n      }\n\n      return acc;\n    },\n    {}\n  );\n\n  return props;\n}\n\nexport function evaluateProps(\n  reference: ReferenceElement,\n  props: Props\n): Props {\n  const out = {\n    ...props,\n    content: invokeWithArgsOrReturn(props.content, [reference]),\n    ...(props.ignoreAttributes\n      ? {}\n      : getDataAttributeProps(reference, props.plugins)),\n  };\n\n  out.aria = {\n    ...defaultProps.aria,\n    ...out.aria,\n  };\n\n  out.aria = {\n    expanded:\n      out.aria.expanded === 'auto' ? props.interactive : out.aria.expanded,\n    content:\n      out.aria.content === 'auto'\n        ? props.interactive\n          ? null\n          : 'describedby'\n        : out.aria.content,\n  };\n\n  return out;\n}\n\nexport function validateProps(\n  partialProps: Partial<Props> = {},\n  plugins: Plugin[] = []\n): void {\n  const keys = Object.keys(partialProps) as Array<keyof Props>;\n  keys.forEach((prop) => {\n    const nonPluginProps = removeProperties(\n      defaultProps,\n      Object.keys(pluginProps)\n    );\n\n    let didPassUnknownProp = !hasOwnProperty(nonPluginProps, prop);\n\n    // Check if the prop exists in `plugins`\n    if (didPassUnknownProp) {\n      didPassUnknownProp =\n        plugins.filter((plugin) => plugin.name === prop).length === 0;\n    }\n\n    warnWhen(\n      didPassUnknownProp,\n      [\n        `\\`${prop}\\``,\n        \"is not a valid prop. You may have spelled it incorrectly, or if it's\",\n        'a plugin, forgot to pass it in an array as props.plugins.',\n        '\\n\\n',\n        'All props: https://atomiks.github.io/tippyjs/v6/all-props/\\n',\n        'Plugins: https://atomiks.github.io/tippyjs/v6/plugins/',\n      ].join(' ')\n    );\n  });\n}\n", "import {\n  ARROW_CLASS,\n  BACKDROP_CLASS,\n  BOX_CLASS,\n  CONTENT_CLASS,\n  SVG_ARROW_CLASS,\n} from './constants';\nimport {div, isElement} from './dom-utils';\nimport {Instance, PopperElement, Props} from './types';\nimport {PopperChildren} from './types-internal';\nimport {arrayFrom} from './utils';\n\n// Firefox extensions don't allow .innerHTML = \"...\" property. This tricks it.\nconst innerHTML = (): 'innerHTML' => 'innerHTML';\n\nfunction dangerouslySetInnerHTML(element: Element, html: string): void {\n  element[innerHTML()] = html;\n}\n\nfunction createArrowElement(value: Props['arrow']): HTMLDivElement {\n  const arrow = div();\n\n  if (value === true) {\n    arrow.className = ARROW_CLASS;\n  } else {\n    arrow.className = SVG_ARROW_CLASS;\n\n    if (isElement(value)) {\n      arrow.appendChild(value);\n    } else {\n      dangerouslySetInnerHTML(arrow, value as string);\n    }\n  }\n\n  return arrow;\n}\n\nexport function setContent(content: HTMLDivElement, props: Props): void {\n  if (isElement(props.content)) {\n    dangerouslySetInnerHTML(content, '');\n    content.appendChild(props.content);\n  } else if (typeof props.content !== 'function') {\n    if (props.allowHTML) {\n      dangerouslySetInnerHTML(content, props.content);\n    } else {\n      content.textContent = props.content;\n    }\n  }\n}\n\nexport function getChildren(popper: PopperElement): PopperChildren {\n  const box = popper.firstElementChild as HTMLDivElement;\n  const boxChildren = arrayFrom(box.children);\n\n  return {\n    box,\n    content: boxChildren.find((node) => node.classList.contains(CONTENT_CLASS)),\n    arrow: boxChildren.find(\n      (node) =>\n        node.classList.contains(ARROW_CLASS) ||\n        node.classList.contains(SVG_ARROW_CLASS)\n    ),\n    backdrop: boxChildren.find((node) =>\n      node.classList.contains(BACKDROP_CLASS)\n    ),\n  };\n}\n\nexport function render(\n  instance: Instance\n): {\n  popper: PopperElement;\n  onUpdate?: (prevProps: Props, nextProps: Props) => void;\n} {\n  const popper = div();\n\n  const box = div();\n  box.className = BOX_CLASS;\n  box.setAttribute('data-state', 'hidden');\n  box.setAttribute('tabindex', '-1');\n\n  const content = div();\n  content.className = CONTENT_CLASS;\n  content.setAttribute('data-state', 'hidden');\n\n  setContent(content, instance.props);\n\n  popper.appendChild(box);\n  box.appendChild(content);\n\n  onUpdate(instance.props, instance.props);\n\n  function onUpdate(prevProps: Props, nextProps: Props): void {\n    const {box, content, arrow} = getChildren(popper);\n\n    if (nextProps.theme) {\n      box.setAttribute('data-theme', nextProps.theme);\n    } else {\n      box.removeAttribute('data-theme');\n    }\n\n    if (typeof nextProps.animation === 'string') {\n      box.setAttribute('data-animation', nextProps.animation);\n    } else {\n      box.removeAttribute('data-animation');\n    }\n\n    if (nextProps.inertia) {\n      box.setAttribute('data-inertia', '');\n    } else {\n      box.removeAttribute('data-inertia');\n    }\n\n    box.style.maxWidth =\n      typeof nextProps.maxWidth === 'number'\n        ? `${nextProps.maxWidth}px`\n        : nextProps.maxWidth;\n\n    if (nextProps.role) {\n      box.setAttribute('role', nextProps.role);\n    } else {\n      box.removeAttribute('role');\n    }\n\n    if (\n      prevProps.content !== nextProps.content ||\n      prevProps.allowHTML !== nextProps.allowHTML\n    ) {\n      setContent(content, instance.props);\n    }\n\n    if (nextProps.arrow) {\n      if (!arrow) {\n        box.appendChild(createArrowElement(nextProps.arrow));\n      } else if (prevProps.arrow !== nextProps.arrow) {\n        box.removeChild(arrow);\n        box.appendChild(createArrowElement(nextProps.arrow));\n      }\n    } else if (arrow) {\n      box.removeChild(arrow!);\n    }\n  }\n\n  return {\n    popper,\n    onUpdate,\n  };\n}\n\n// Runtime check to identify if the render function is the default one; this\n// way we can apply default CSS transitions logic and it can be tree-shaken away\nrender.$$tippy = true;\n", "import {createPopper, StrictModifiers, Modifier} from '@popperjs/core';\nimport {currentInput} from './bindGlobalEventListeners';\nimport {isIE} from './browser';\nimport {TOUCH_OPTIONS} from './constants';\nimport {\n  div,\n  getOwnerDocument,\n  isCursorOutsideInteractiveBorder,\n  isMouseEvent,\n  setTransitionDuration,\n  setVisibilityState,\n  updateTransitionEndListener,\n} from './dom-utils';\nimport {defaultProps, evaluateProps, getExtendedPassedProps} from './props';\nimport {getChildren} from './template';\nimport {\n  Content,\n  Instance,\n  LifecycleHooks,\n  PopperElement,\n  Props,\n  ReferenceElement,\n} from './types';\nimport {ListenerObject, PopperTreeData, PopperChildren} from './types-internal';\nimport {\n  arrayFrom,\n  debounce,\n  getValueAtIndexOrReturn,\n  invokeWithArgsOrReturn,\n  normalizeToArray,\n  pushIfUnique,\n  splitBySpaces,\n  unique,\n  removeUndefinedProps,\n} from './utils';\nimport {createMemoryLeakWarning, errorWhen, warnWhen} from './validation';\n\nlet idCounter = 1;\nlet mouseMoveListeners: ((event: MouseEvent) => void)[] = [];\n\n// Used by `hideAll()`\nexport let mountedInstances: Instance[] = [];\n\nexport default function createTippy(\n  reference: ReferenceElement,\n  passedProps: Partial<Props>\n): Instance {\n  const props = evaluateProps(reference, {\n    ...defaultProps,\n    ...getExtendedPassedProps(removeUndefinedProps(passedProps)),\n  });\n\n  // ===========================================================================\n  // 🔒 Private members\n  // ===========================================================================\n  let showTimeout: any;\n  let hideTimeout: any;\n  let scheduleHideAnimationFrame: number;\n  let isVisibleFromClick = false;\n  let didHideDueToDocumentMouseDown = false;\n  let didTouchMove = false;\n  let ignoreOnFirstUpdate = false;\n  let lastTriggerEvent: Event | undefined;\n  let currentTransitionEndListener: (event: TransitionEvent) => void;\n  let onFirstUpdate: () => void;\n  let listeners: ListenerObject[] = [];\n  let debouncedOnMouseMove = debounce(onMouseMove, props.interactiveDebounce);\n  let currentTarget: Element;\n  const doc = getOwnerDocument(props.triggerTarget || reference);\n\n  // ===========================================================================\n  // 🔑 Public members\n  // ===========================================================================\n  const id = idCounter++;\n  const popperInstance = null;\n  const plugins = unique(props.plugins);\n\n  const state = {\n    // Is the instance currently enabled?\n    isEnabled: true,\n    // Is the tippy currently showing and not transitioning out?\n    isVisible: false,\n    // Has the instance been destroyed?\n    isDestroyed: false,\n    // Is the tippy currently mounted to the DOM?\n    isMounted: false,\n    // Has the tippy finished transitioning in?\n    isShown: false,\n  };\n\n  const instance: Instance = {\n    // properties\n    id,\n    reference,\n    popper: div(),\n    popperInstance,\n    props,\n    state,\n    plugins,\n    // methods\n    clearDelayTimeouts,\n    setProps,\n    setContent,\n    show,\n    hide,\n    hideWithInteractivity,\n    enable,\n    disable,\n    unmount,\n    destroy,\n  };\n\n  // TODO: Investigate why this early return causes a TDZ error in the tests —\n  // it doesn't seem to happen in the browser\n  /* istanbul ignore if */\n  if (!props.render) {\n    if (__DEV__) {\n      errorWhen(true, 'render() function has not been supplied.');\n    }\n\n    return instance;\n  }\n\n  // ===========================================================================\n  // Initial mutations\n  // ===========================================================================\n  const {popper, onUpdate} = props.render(instance);\n\n  popper.setAttribute('data-__NAMESPACE_PREFIX__-root', '');\n  popper.id = `__NAMESPACE_PREFIX__-${instance.id}`;\n\n  instance.popper = popper;\n  reference._tippy = instance;\n  popper._tippy = instance;\n\n  const pluginsHooks = plugins.map((plugin) => plugin.fn(instance));\n  const hasAriaExpanded = reference.hasAttribute('aria-expanded');\n\n  addListeners();\n  handleAriaExpandedAttribute();\n  handleStyles();\n\n  invokeHook('onCreate', [instance]);\n\n  if (props.showOnCreate) {\n    scheduleShow();\n  }\n\n  // Prevent a tippy with a delay from hiding if the cursor left then returned\n  // before it started hiding\n  popper.addEventListener('mouseenter', () => {\n    if (instance.props.interactive && instance.state.isVisible) {\n      instance.clearDelayTimeouts();\n    }\n  });\n\n  popper.addEventListener('mouseleave', (event) => {\n    if (\n      instance.props.interactive &&\n      instance.props.trigger.indexOf('mouseenter') >= 0\n    ) {\n      doc.addEventListener('mousemove', debouncedOnMouseMove);\n      debouncedOnMouseMove(event);\n    }\n  });\n\n  return instance;\n\n  // ===========================================================================\n  // 🔒 Private methods\n  // ===========================================================================\n  function getNormalizedTouchSettings(): [string | boolean, number] {\n    const {touch} = instance.props;\n    return Array.isArray(touch) ? touch : [touch, 0];\n  }\n\n  function getIsCustomTouchBehavior(): boolean {\n    return getNormalizedTouchSettings()[0] === 'hold';\n  }\n\n  function getIsDefaultRenderFn(): boolean {\n    // @ts-ignore\n    return !!instance.props.render?.$$tippy;\n  }\n\n  function getCurrentTarget(): Element {\n    return currentTarget || reference;\n  }\n\n  function getDefaultTemplateChildren(): PopperChildren {\n    return getChildren(popper);\n  }\n\n  function getDelay(isShow: boolean): number {\n    // For touch or keyboard input, force `0` delay for UX reasons\n    // Also if the instance is mounted but not visible (transitioning out),\n    // ignore delay\n    if (\n      (instance.state.isMounted && !instance.state.isVisible) ||\n      currentInput.isTouch ||\n      (lastTriggerEvent && lastTriggerEvent.type === 'focus')\n    ) {\n      return 0;\n    }\n\n    return getValueAtIndexOrReturn(\n      instance.props.delay,\n      isShow ? 0 : 1,\n      defaultProps.delay\n    );\n  }\n\n  function handleStyles(): void {\n    popper.style.pointerEvents =\n      instance.props.interactive && instance.state.isVisible ? '' : 'none';\n    popper.style.zIndex = `${instance.props.zIndex}`;\n  }\n\n  function invokeHook(\n    hook: keyof LifecycleHooks,\n    args: [Instance, any?],\n    shouldInvokePropsHook = true\n  ): void {\n    pluginsHooks.forEach((pluginHooks) => {\n      if (pluginHooks[hook]) {\n        pluginHooks[hook]!(...args);\n      }\n    });\n\n    if (shouldInvokePropsHook) {\n      instance.props[hook](...args);\n    }\n  }\n\n  function handleAriaContentAttribute(): void {\n    const {aria} = instance.props;\n\n    if (!aria.content) {\n      return;\n    }\n\n    const attr = `aria-${aria.content}`;\n    const id = popper.id;\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      const currentValue = node.getAttribute(attr);\n\n      if (instance.state.isVisible) {\n        node.setAttribute(attr, currentValue ? `${currentValue} ${id}` : id);\n      } else {\n        const nextValue = currentValue && currentValue.replace(id, '').trim();\n\n        if (nextValue) {\n          node.setAttribute(attr, nextValue);\n        } else {\n          node.removeAttribute(attr);\n        }\n      }\n    });\n  }\n\n  function handleAriaExpandedAttribute(): void {\n    if (hasAriaExpanded || !instance.props.aria.expanded) {\n      return;\n    }\n\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      if (instance.props.interactive) {\n        node.setAttribute(\n          'aria-expanded',\n          instance.state.isVisible && node === getCurrentTarget()\n            ? 'true'\n            : 'false'\n        );\n      } else {\n        node.removeAttribute('aria-expanded');\n      }\n    });\n  }\n\n  function cleanupInteractiveMouseListeners(): void {\n    doc.removeEventListener('mousemove', debouncedOnMouseMove);\n    mouseMoveListeners = mouseMoveListeners.filter(\n      (listener) => listener !== debouncedOnMouseMove\n    );\n  }\n\n  function onDocumentPress(event: MouseEvent | TouchEvent): void {\n    // Moved finger to scroll instead of an intentional tap outside\n    if (currentInput.isTouch) {\n      if (didTouchMove || event.type === 'mousedown') {\n        return;\n      }\n    }\n\n    // Clicked on interactive popper\n    if (\n      instance.props.interactive &&\n      popper.contains(event.target as Element)\n    ) {\n      return;\n    }\n\n    // Clicked on the event listeners target\n    if (getCurrentTarget().contains(event.target as Element)) {\n      if (currentInput.isTouch) {\n        return;\n      }\n\n      if (\n        instance.state.isVisible &&\n        instance.props.trigger.indexOf('click') >= 0\n      ) {\n        return;\n      }\n    } else {\n      invokeHook('onClickOutside', [instance, event]);\n    }\n\n    if (instance.props.hideOnClick === true) {\n      isVisibleFromClick = false;\n      instance.clearDelayTimeouts();\n      instance.hide();\n\n      // `mousedown` event is fired right before `focus` if pressing the\n      // currentTarget. This lets a tippy with `focus` trigger know that it\n      // should not show\n      didHideDueToDocumentMouseDown = true;\n      setTimeout(() => {\n        didHideDueToDocumentMouseDown = false;\n      });\n\n      // The listener gets added in `scheduleShow()`, but this may be hiding it\n      // before it shows, and hide()'s early bail-out behavior can prevent it\n      // from being cleaned up\n      if (!instance.state.isMounted) {\n        removeDocumentPress();\n      }\n    }\n  }\n\n  function onTouchMove(): void {\n    didTouchMove = true;\n  }\n\n  function onTouchStart(): void {\n    didTouchMove = false;\n  }\n\n  function addDocumentPress(): void {\n    doc.addEventListener('mousedown', onDocumentPress, true);\n    doc.addEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.addEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.addEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function removeDocumentPress(): void {\n    doc.removeEventListener('mousedown', onDocumentPress, true);\n    doc.removeEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.removeEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.removeEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function onTransitionedOut(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, () => {\n      if (\n        !instance.state.isVisible &&\n        popper.parentNode &&\n        popper.parentNode.contains(popper)\n      ) {\n        callback();\n      }\n    });\n  }\n\n  function onTransitionedIn(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, callback);\n  }\n\n  function onTransitionEnd(duration: number, callback: () => void): void {\n    const box = getDefaultTemplateChildren().box;\n\n    function listener(event: TransitionEvent): void {\n      if (event.target === box) {\n        updateTransitionEndListener(box, 'remove', listener);\n        callback();\n      }\n    }\n\n    // Make callback synchronous if duration is 0\n    // `transitionend` won't fire otherwise\n    if (duration === 0) {\n      return callback();\n    }\n\n    updateTransitionEndListener(box, 'remove', currentTransitionEndListener);\n    updateTransitionEndListener(box, 'add', listener);\n\n    currentTransitionEndListener = listener;\n  }\n\n  function on(\n    eventType: string,\n    handler: EventListener,\n    options: boolean | object = false\n  ): void {\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach((node) => {\n      node.addEventListener(eventType, handler, options);\n      listeners.push({node, eventType, handler, options});\n    });\n  }\n\n  function addListeners(): void {\n    if (getIsCustomTouchBehavior()) {\n      on('touchstart', onTrigger, {passive: true});\n      on('touchend', onMouseLeave as EventListener, {passive: true});\n    }\n\n    splitBySpaces(instance.props.trigger).forEach((eventType) => {\n      if (eventType === 'manual') {\n        return;\n      }\n\n      on(eventType, onTrigger);\n\n      switch (eventType) {\n        case 'mouseenter':\n          on('mouseleave', onMouseLeave as EventListener);\n          break;\n        case 'focus':\n          on(isIE ? 'focusout' : 'blur', onBlurOrFocusOut as EventListener);\n          break;\n        case 'focusin':\n          on('focusout', onBlurOrFocusOut as EventListener);\n          break;\n      }\n    });\n  }\n\n  function removeListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function onTrigger(event: Event): void {\n    let shouldScheduleClickHide = false;\n\n    if (\n      !instance.state.isEnabled ||\n      isEventListenerStopped(event) ||\n      didHideDueToDocumentMouseDown\n    ) {\n      return;\n    }\n\n    const wasFocused = lastTriggerEvent?.type === 'focus';\n\n    lastTriggerEvent = event;\n    currentTarget = event.currentTarget as Element;\n\n    handleAriaExpandedAttribute();\n\n    if (!instance.state.isVisible && isMouseEvent(event)) {\n      // If scrolling, `mouseenter` events can be fired if the cursor lands\n      // over a new target, but `mousemove` events don't get fired. This\n      // causes interactive tooltips to get stuck open until the cursor is\n      // moved\n      mouseMoveListeners.forEach((listener) => listener(event));\n    }\n\n    // Toggle show/hide when clicking click-triggered tooltips\n    if (\n      event.type === 'click' &&\n      (instance.props.trigger.indexOf('mouseenter') < 0 ||\n        isVisibleFromClick) &&\n      instance.props.hideOnClick !== false &&\n      instance.state.isVisible\n    ) {\n      shouldScheduleClickHide = true;\n    } else {\n      scheduleShow(event);\n    }\n\n    if (event.type === 'click') {\n      isVisibleFromClick = !shouldScheduleClickHide;\n    }\n\n    if (shouldScheduleClickHide && !wasFocused) {\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseMove(event: MouseEvent): void {\n    const target = event.target as Node;\n    const isCursorOverReferenceOrPopper =\n      reference.contains(target) || popper.contains(target);\n\n    if (event.type === 'mousemove' && isCursorOverReferenceOrPopper) {\n      return;\n    }\n\n    const popperTreeData = getNestedPopperTree()\n      .concat(popper)\n      .map((popper) => {\n        const instance = popper._tippy!;\n        const state = instance.popperInstance?.state;\n\n        if (state) {\n          return {\n            popperRect: popper.getBoundingClientRect(),\n            popperState: state,\n            props,\n          };\n        }\n\n        return null;\n      })\n      .filter(Boolean) as PopperTreeData[];\n\n    if (isCursorOutsideInteractiveBorder(popperTreeData, event)) {\n      cleanupInteractiveMouseListeners();\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseLeave(event: MouseEvent): void {\n    const shouldBail =\n      isEventListenerStopped(event) ||\n      (instance.props.trigger.indexOf('click') >= 0 && isVisibleFromClick);\n\n    if (shouldBail) {\n      return;\n    }\n\n    if (instance.props.interactive) {\n      instance.hideWithInteractivity(event);\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function onBlurOrFocusOut(event: FocusEvent): void {\n    if (\n      instance.props.trigger.indexOf('focusin') < 0 &&\n      event.target !== getCurrentTarget()\n    ) {\n      return;\n    }\n\n    // If focus was moved to within the popper\n    if (\n      instance.props.interactive &&\n      event.relatedTarget &&\n      popper.contains(event.relatedTarget as Element)\n    ) {\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function isEventListenerStopped(event: Event): boolean {\n    return currentInput.isTouch\n      ? getIsCustomTouchBehavior() !== event.type.indexOf('touch') >= 0\n      : false;\n  }\n\n  function createPopperInstance(): void {\n    destroyPopperInstance();\n\n    const {\n      popperOptions,\n      placement,\n      offset,\n      getReferenceClientRect,\n      moveTransition,\n    } = instance.props;\n\n    const arrow = getIsDefaultRenderFn() ? getChildren(popper).arrow : null;\n\n    const computedReference = getReferenceClientRect\n      ? {\n          getBoundingClientRect: getReferenceClientRect,\n          contextElement:\n            getReferenceClientRect.contextElement || getCurrentTarget(),\n        }\n      : reference;\n\n    const tippyModifier: Modifier<'$$tippy', {}> = {\n      name: '$$tippy',\n      enabled: true,\n      phase: 'beforeWrite',\n      requires: ['computeStyles'],\n      fn({state}) {\n        if (getIsDefaultRenderFn()) {\n          const {box} = getDefaultTemplateChildren();\n\n          ['placement', 'reference-hidden', 'escaped'].forEach((attr) => {\n            if (attr === 'placement') {\n              box.setAttribute('data-placement', state.placement);\n            } else {\n              if (state.attributes.popper[`data-popper-${attr}`]) {\n                box.setAttribute(`data-${attr}`, '');\n              } else {\n                box.removeAttribute(`data-${attr}`);\n              }\n            }\n          });\n\n          state.attributes.popper = {};\n        }\n      },\n    };\n\n    type TippyModifier = Modifier<'$$tippy', {}>;\n    type ExtendedModifiers = StrictModifiers | Partial<TippyModifier>;\n\n    const modifiers: Array<ExtendedModifiers> = [\n      {\n        name: 'offset',\n        options: {\n          offset,\n        },\n      },\n      {\n        name: 'preventOverflow',\n        options: {\n          padding: {\n            top: 2,\n            bottom: 2,\n            left: 5,\n            right: 5,\n          },\n        },\n      },\n      {\n        name: 'flip',\n        options: {\n          padding: 5,\n        },\n      },\n      {\n        name: 'computeStyles',\n        options: {\n          adaptive: !moveTransition,\n        },\n      },\n      tippyModifier,\n    ];\n\n    if (getIsDefaultRenderFn() && arrow) {\n      modifiers.push({\n        name: 'arrow',\n        options: {\n          element: arrow,\n          padding: 3,\n        },\n      });\n    }\n\n    modifiers.push(...(popperOptions?.modifiers || []));\n\n    instance.popperInstance = createPopper<ExtendedModifiers>(\n      computedReference,\n      popper,\n      {\n        ...popperOptions,\n        placement,\n        onFirstUpdate,\n        modifiers,\n      }\n    );\n  }\n\n  function destroyPopperInstance(): void {\n    if (instance.popperInstance) {\n      instance.popperInstance.destroy();\n      instance.popperInstance = null;\n    }\n  }\n\n  function mount(): void {\n    const {appendTo} = instance.props;\n\n    let parentNode: any;\n\n    // By default, we'll append the popper to the triggerTargets's parentNode so\n    // it's directly after the reference element so the elements inside the\n    // tippy can be tabbed to\n    // If there are clipping issues, the user can specify a different appendTo\n    // and ensure focus management is handled correctly manually\n    const node = getCurrentTarget();\n\n    if (\n      (instance.props.interactive && appendTo === defaultProps.appendTo) ||\n      appendTo === 'parent'\n    ) {\n      parentNode = node.parentNode;\n    } else {\n      parentNode = invokeWithArgsOrReturn(appendTo, [node]);\n    }\n\n    // The popper element needs to exist on the DOM before its position can be\n    // updated as Popper needs to read its dimensions\n    if (!parentNode.contains(popper)) {\n      parentNode.appendChild(popper);\n    }\n\n    createPopperInstance();\n\n    /* istanbul ignore else */\n    if (__DEV__) {\n      // Accessibility check\n      warnWhen(\n        instance.props.interactive &&\n          appendTo === defaultProps.appendTo &&\n          node.nextElementSibling !== popper,\n        [\n          'Interactive tippy element may not be accessible via keyboard',\n          'navigation because it is not directly after the reference element',\n          'in the DOM source order.',\n          '\\n\\n',\n          'Using a wrapper <div> or <span> tag around the reference element',\n          'solves this by creating a new parentNode context.',\n          '\\n\\n',\n          'Specifying `appendTo: document.body` silences this warning, but it',\n          'assumes you are using a focus management solution to handle',\n          'keyboard navigation.',\n          '\\n\\n',\n          'See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity',\n        ].join(' ')\n      );\n    }\n  }\n\n  function getNestedPopperTree(): PopperElement[] {\n    return arrayFrom(\n      popper.querySelectorAll('[data-__NAMESPACE_PREFIX__-root]')\n    );\n  }\n\n  function scheduleShow(event?: Event): void {\n    instance.clearDelayTimeouts();\n\n    if (event) {\n      invokeHook('onTrigger', [instance, event]);\n    }\n\n    addDocumentPress();\n\n    let delay = getDelay(true);\n    const [touchValue, touchDelay] = getNormalizedTouchSettings();\n\n    if (currentInput.isTouch && touchValue === 'hold' && touchDelay) {\n      delay = touchDelay;\n    }\n\n    if (delay) {\n      showTimeout = setTimeout(() => {\n        instance.show();\n      }, delay);\n    } else {\n      instance.show();\n    }\n  }\n\n  function scheduleHide(event: Event): void {\n    instance.clearDelayTimeouts();\n\n    invokeHook('onUntrigger', [instance, event]);\n\n    if (!instance.state.isVisible) {\n      removeDocumentPress();\n\n      return;\n    }\n\n    // For interactive tippies, scheduleHide is added to a document.body handler\n    // from onMouseLeave so must intercept scheduled hides from mousemove/leave\n    // events when trigger contains mouseenter and click, and the tip is\n    // currently shown as a result of a click.\n    if (\n      instance.props.trigger.indexOf('mouseenter') >= 0 &&\n      instance.props.trigger.indexOf('click') >= 0 &&\n      ['mouseleave', 'mousemove'].indexOf(event.type) >= 0 &&\n      isVisibleFromClick\n    ) {\n      return;\n    }\n\n    const delay = getDelay(false);\n\n    if (delay) {\n      hideTimeout = setTimeout(() => {\n        if (instance.state.isVisible) {\n          instance.hide();\n        }\n      }, delay);\n    } else {\n      // Fixes a `transitionend` problem when it fires 1 frame too\n      // late sometimes, we don't want hide() to be called.\n      scheduleHideAnimationFrame = requestAnimationFrame(() => {\n        instance.hide();\n      });\n    }\n  }\n\n  // ===========================================================================\n  // 🔑 Public methods\n  // ===========================================================================\n  function enable(): void {\n    instance.state.isEnabled = true;\n  }\n\n  function disable(): void {\n    // Disabling the instance should also hide it\n    // https://github.com/atomiks/tippy.js-react/issues/106\n    instance.hide();\n    instance.state.isEnabled = false;\n  }\n\n  function clearDelayTimeouts(): void {\n    clearTimeout(showTimeout);\n    clearTimeout(hideTimeout);\n    cancelAnimationFrame(scheduleHideAnimationFrame);\n  }\n\n  function setProps(partialProps: Partial<Props>): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('setProps'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    invokeHook('onBeforeUpdate', [instance, partialProps]);\n\n    removeListeners();\n\n    const prevProps = instance.props;\n    const nextProps = evaluateProps(reference, {\n      ...instance.props,\n      ...partialProps,\n      ignoreAttributes: true,\n    });\n\n    instance.props = nextProps;\n\n    addListeners();\n\n    if (prevProps.interactiveDebounce !== nextProps.interactiveDebounce) {\n      cleanupInteractiveMouseListeners();\n      debouncedOnMouseMove = debounce(\n        onMouseMove,\n        nextProps.interactiveDebounce\n      );\n    }\n\n    // Ensure stale aria-expanded attributes are removed\n    if (prevProps.triggerTarget && !nextProps.triggerTarget) {\n      normalizeToArray(prevProps.triggerTarget).forEach((node) => {\n        node.removeAttribute('aria-expanded');\n      });\n    } else if (nextProps.triggerTarget) {\n      reference.removeAttribute('aria-expanded');\n    }\n\n    handleAriaExpandedAttribute();\n    handleStyles();\n\n    if (onUpdate) {\n      onUpdate(prevProps, nextProps);\n    }\n\n    if (instance.popperInstance) {\n      createPopperInstance();\n\n      // Fixes an issue with nested tippies if they are all getting re-rendered,\n      // and the nested ones get re-rendered first.\n      // https://github.com/atomiks/tippyjs-react/issues/177\n      // TODO: find a cleaner / more efficient solution(!)\n      getNestedPopperTree().forEach((nestedPopper) => {\n        // React (and other UI libs likely) requires a rAF wrapper as it flushes\n        // its work in one\n        requestAnimationFrame(nestedPopper._tippy!.popperInstance!.forceUpdate);\n      });\n    }\n\n    invokeHook('onAfterUpdate', [instance, partialProps]);\n  }\n\n  function setContent(content: Content): void {\n    instance.setProps({content});\n  }\n\n  function show(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('show'));\n    }\n\n    // Early bail-out\n    const isAlreadyVisible = instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const isTouchAndTouchDisabled =\n      currentInput.isTouch && !instance.props.touch;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      0,\n      defaultProps.duration\n    );\n\n    if (\n      isAlreadyVisible ||\n      isDestroyed ||\n      isDisabled ||\n      isTouchAndTouchDisabled\n    ) {\n      return;\n    }\n\n    // Normalize `disabled` behavior across browsers.\n    // Firefox allows events on disabled elements, but Chrome doesn't.\n    // Using a wrapper element (i.e. <span>) is recommended.\n    if (getCurrentTarget().hasAttribute('disabled')) {\n      return;\n    }\n\n    invokeHook('onShow', [instance], false);\n    if (instance.props.onShow(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = true;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'visible';\n    }\n\n    handleStyles();\n    addDocumentPress();\n\n    if (!instance.state.isMounted) {\n      popper.style.transition = 'none';\n    }\n\n    // If flipping to the opposite side after hiding at least once, the\n    // animation will use the wrong placement without resetting the duration\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n      setTransitionDuration([box, content], 0);\n    }\n\n    onFirstUpdate = (): void => {\n      if (!instance.state.isVisible || ignoreOnFirstUpdate) {\n        return;\n      }\n\n      ignoreOnFirstUpdate = true;\n\n      // reflow\n      void popper.offsetHeight;\n\n      popper.style.transition = instance.props.moveTransition;\n\n      if (getIsDefaultRenderFn() && instance.props.animation) {\n        const {box, content} = getDefaultTemplateChildren();\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'visible');\n      }\n\n      handleAriaContentAttribute();\n      handleAriaExpandedAttribute();\n\n      pushIfUnique(mountedInstances, instance);\n\n      instance.state.isMounted = true;\n      invokeHook('onMount', [instance]);\n\n      if (instance.props.animation && getIsDefaultRenderFn()) {\n        onTransitionedIn(duration, () => {\n          instance.state.isShown = true;\n          invokeHook('onShown', [instance]);\n        });\n      }\n    };\n\n    mount();\n  }\n\n  function hide(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hide'));\n    }\n\n    // Early bail-out\n    const isAlreadyHidden = !instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      1,\n      defaultProps.duration\n    );\n\n    if (isAlreadyHidden || isDestroyed || isDisabled) {\n      return;\n    }\n\n    invokeHook('onHide', [instance], false);\n    if (instance.props.onHide(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = false;\n    instance.state.isShown = false;\n    ignoreOnFirstUpdate = false;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'hidden';\n    }\n\n    cleanupInteractiveMouseListeners();\n    removeDocumentPress();\n    handleStyles();\n\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n\n      if (instance.props.animation) {\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'hidden');\n      }\n    }\n\n    handleAriaContentAttribute();\n    handleAriaExpandedAttribute();\n\n    if (instance.props.animation) {\n      if (getIsDefaultRenderFn()) {\n        onTransitionedOut(duration, instance.unmount);\n      }\n    } else {\n      instance.unmount();\n    }\n  }\n\n  function hideWithInteractivity(event: MouseEvent): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(\n        instance.state.isDestroyed,\n        createMemoryLeakWarning('hideWithInteractivity')\n      );\n    }\n\n    doc.addEventListener('mousemove', debouncedOnMouseMove);\n    pushIfUnique(mouseMoveListeners, debouncedOnMouseMove);\n    debouncedOnMouseMove(event);\n  }\n\n  function unmount(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('unmount'));\n    }\n\n    if (instance.state.isVisible) {\n      instance.hide();\n    }\n\n    if (!instance.state.isMounted) {\n      return;\n    }\n\n    destroyPopperInstance();\n\n    // If a popper is not interactive, it will be appended outside the popper\n    // tree by default. This seems mainly for interactive tippies, but we should\n    // find a workaround if possible\n    getNestedPopperTree().forEach((nestedPopper) => {\n      nestedPopper._tippy!.unmount();\n    });\n\n    if (popper.parentNode) {\n      popper.parentNode.removeChild(popper);\n    }\n\n    mountedInstances = mountedInstances.filter((i) => i !== instance);\n\n    instance.state.isMounted = false;\n    invokeHook('onHidden', [instance]);\n  }\n\n  function destroy(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('destroy'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    instance.clearDelayTimeouts();\n    instance.unmount();\n\n    removeListeners();\n\n    delete reference._tippy;\n\n    instance.state.isDestroyed = true;\n\n    invokeHook('onDestroy', [instance]);\n  }\n}\n", "import bindGlobalEventListeners, {\n  currentInput,\n} from './bindGlobalEventListeners';\nimport createTippy, {mountedInstances} from './createTippy';\nimport {getArrayOfElements, isElement, isReferenceElement} from './dom-utils';\nimport {defaultProps, setDefaultProps, validateProps} from './props';\nimport {HideAll, HideAllOptions, Instance, Props, Targets} from './types';\nimport {validateTargets, warnWhen} from './validation';\n\nfunction tippy(\n  targets: Targets,\n  optionalProps: Partial<Props> = {}\n): Instance | Instance[] {\n  const plugins = defaultProps.plugins.concat(optionalProps.plugins || []);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateTargets(targets);\n    validateProps(optionalProps, plugins);\n  }\n\n  bindGlobalEventListeners();\n\n  const passedProps: Partial<Props> = {...optionalProps, plugins};\n\n  const elements = getArrayOfElements(targets);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    const isSingleContentElement = isElement(passedProps.content);\n    const isMoreThanOneReferenceElement = elements.length > 1;\n    warnWhen(\n      isSingleContentElement && isMoreThanOneReferenceElement,\n      [\n        'tippy() was passed an Element as the `content` prop, but more than',\n        'one tippy instance was created by this invocation. This means the',\n        'content element will only be appended to the last tippy instance.',\n        '\\n\\n',\n        'Instead, pass the .innerHTML of the element, or use a function that',\n        'returns a cloned version of the element instead.',\n        '\\n\\n',\n        '1) content: element.innerHTML\\n',\n        '2) content: () => element.cloneNode(true)',\n      ].join(' ')\n    );\n  }\n\n  const instances = elements.reduce<Instance[]>(\n    (acc, reference): Instance[] => {\n      const instance = reference && createTippy(reference, passedProps);\n\n      if (instance) {\n        acc.push(instance);\n      }\n\n      return acc;\n    },\n    []\n  );\n\n  return isElement(targets) ? instances[0] : instances;\n}\n\ntippy.defaultProps = defaultProps;\ntippy.setDefaultProps = setDefaultProps;\ntippy.currentInput = currentInput;\n\nexport default tippy;\n\nexport const hideAll: HideAll = ({\n  exclude: excludedReferenceOrInstance,\n  duration,\n}: HideAllOptions = {}) => {\n  mountedInstances.forEach((instance) => {\n    let isExcluded = false;\n\n    if (excludedReferenceOrInstance) {\n      isExcluded = isReferenceElement(excludedReferenceOrInstance)\n        ? instance.reference === excludedReferenceOrInstance\n        : instance.popper === (excludedReferenceOrInstance as Instance).popper;\n    }\n\n    if (!isExcluded) {\n      const originalDuration = instance.props.duration;\n\n      instance.setProps({duration});\n      instance.hide();\n\n      if (!instance.state.isDestroyed) {\n        instance.setProps({duration: originalDuration});\n      }\n    }\n  });\n};\n", "import tippy from '..';\nimport {defaultProps} from '../props';\nimport {Instance, Props, Targets} from '../types';\nimport {ListenerObject} from '../types-internal';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\n\nconst BUBBLING_EVENTS_MAP = {\n  mouseover: 'mouseenter',\n  focusin: 'focus',\n  click: 'click',\n};\n\n/**\n * Creates a delegate instance that controls the creation of tippy instances\n * for child elements (`target` CSS selector).\n */\nfunction delegate(\n  targets: Targets,\n  props: Partial<Props> & {target: string}\n): Instance | Instance[] {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !(props && props.target),\n      [\n        'You must specity a `target` prop indicating a CSS selector string matching',\n        'the target elements that should receive a tippy.',\n      ].join(' ')\n    );\n  }\n\n  let listeners: ListenerObject[] = [];\n  let childTippyInstances: Instance[] = [];\n\n  const {target} = props;\n\n  const nativeProps = removeProperties(props, ['target']);\n  const parentProps = {...nativeProps, trigger: 'manual', touch: false};\n  const childProps = {...nativeProps, showOnCreate: true};\n\n  const returnValue = tippy(targets, parentProps);\n  const normalizedReturnValue = normalizeToArray(returnValue);\n\n  function onTrigger(event: Event): void {\n    if (!event.target) {\n      return;\n    }\n\n    const targetNode = (event.target as Element).closest(target);\n\n    if (!targetNode) {\n      return;\n    }\n\n    // Get relevant trigger with fallbacks:\n    // 1. Check `data-tippy-trigger` attribute on target node\n    // 2. Fallback to `trigger` passed to `delegate()`\n    // 3. Fallback to `defaultProps.trigger`\n    const trigger =\n      targetNode.getAttribute('data-tippy-trigger') ||\n      props.trigger ||\n      defaultProps.trigger;\n\n    // @ts-ignore\n    if (targetNode._tippy) {\n      return;\n    }\n\n    if (event.type === 'touchstart' && typeof childProps.touch === 'boolean') {\n      return;\n    }\n\n    if (\n      event.type !== 'touchstart' &&\n      trigger.indexOf((BUBBLING_EVENTS_MAP as any)[event.type])\n    ) {\n      return;\n    }\n\n    const instance = tippy(targetNode, childProps);\n\n    if (instance) {\n      childTippyInstances = childTippyInstances.concat(instance);\n    }\n  }\n\n  function on(\n    node: Element,\n    eventType: string,\n    handler: EventListener,\n    options: object | boolean = false\n  ): void {\n    node.addEventListener(eventType, handler, options);\n    listeners.push({node, eventType, handler, options});\n  }\n\n  function addEventListeners(instance: Instance): void {\n    const {reference} = instance;\n\n    on(reference, 'touchstart', onTrigger);\n    on(reference, 'mouseover', onTrigger);\n    on(reference, 'focusin', onTrigger);\n    on(reference, 'click', onTrigger);\n  }\n\n  function removeEventListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function applyMutations(instance: Instance): void {\n    const originalDestroy = instance.destroy;\n    instance.destroy = (shouldDestroyChildInstances = true): void => {\n      if (shouldDestroyChildInstances) {\n        childTippyInstances.forEach((instance) => {\n          instance.destroy();\n        });\n      }\n\n      childTippyInstances = [];\n\n      removeEventListeners();\n      originalDestroy();\n    };\n\n    addEventListeners(instance);\n  }\n\n  normalizedReturnValue.forEach(applyMutations);\n\n  return returnValue;\n}\n\nexport default delegate;\n", "import {BACKDROP_CLASS} from '../constants';\nimport {div, setVisibilityState} from '../dom-utils';\nimport {getChildren} from '../template';\nimport {AnimateFill} from '../types';\nimport {errorWhen} from '../validation';\n\nconst animateFill: AnimateFill = {\n  name: 'animateFill',\n  defaultValue: false,\n  fn(instance) {\n    // @ts-ignore\n    if (!instance.props.render?.$$tippy) {\n      if (__DEV__) {\n        errorWhen(\n          instance.props.animateFill,\n          'The `animateFill` plugin requires the default render function.'\n        );\n      }\n\n      return {};\n    }\n\n    const {box, content} = getChildren(instance.popper);\n\n    const backdrop = instance.props.animateFill\n      ? createBackdropElement()\n      : null;\n\n    return {\n      onCreate(): void {\n        if (backdrop) {\n          box.insertBefore(backdrop, box.firstElementChild!);\n          box.setAttribute('data-animatefill', '');\n          box.style.overflow = 'hidden';\n\n          instance.setProps({arrow: false, animation: 'shift-away'});\n        }\n      },\n      onMount(): void {\n        if (backdrop) {\n          const {transitionDuration} = box.style;\n          const duration = Number(transitionDuration.replace('ms', ''));\n\n          // The content should fade in after the backdrop has mostly filled the\n          // tooltip element. `clip-path` is the other alternative but is not\n          // well-supported and is buggy on some devices.\n          content.style.transitionDelay = `${Math.round(duration / 10)}ms`;\n\n          backdrop.style.transitionDuration = transitionDuration;\n          setVisibilityState([backdrop], 'visible');\n        }\n      },\n      onShow(): void {\n        if (backdrop) {\n          backdrop.style.transitionDuration = '0ms';\n        }\n      },\n      onHide(): void {\n        if (backdrop) {\n          setVisibilityState([backdrop], 'hidden');\n        }\n      },\n    };\n  },\n};\n\nexport default animateFill;\n\nfunction createBackdropElement(): HTMLDivElement {\n  const backdrop = div();\n  backdrop.className = BACKDROP_CLASS;\n  setVisibilityState([backdrop], 'hidden');\n  return backdrop;\n}\n", "import {getOwnerDocument} from '../dom-utils';\nimport {FollowCursor, Instance} from '../types';\n\nlet mouseCoords = {clientX: 0, clientY: 0};\nlet activeInstances: Array<{instance: Instance; doc: Document}> = [];\n\nfunction storeMouseCoords({clientX, clientY}: MouseEvent): void {\n  mouseCoords = {clientX, clientY};\n}\n\nfunction addMouseCoordsListener(doc: Document): void {\n  doc.addEventListener('mousemove', storeMouseCoords);\n}\n\nfunction removeMouseCoordsListener(doc: Document): void {\n  doc.removeEventListener('mousemove', storeMouseCoords);\n}\n\nconst followCursor: FollowCursor = {\n  name: 'followCursor',\n  defaultValue: false,\n  fn(instance) {\n    const reference = instance.reference;\n    const doc = getOwnerDocument(instance.props.triggerTarget || reference);\n\n    let isInternalUpdate = false;\n    let wasFocusEvent = false;\n    let isUnmounted = true;\n    let prevProps = instance.props;\n\n    function getIsInitialBehavior(): boolean {\n      return (\n        instance.props.followCursor === 'initial' && instance.state.isVisible\n      );\n    }\n\n    function addListener(): void {\n      doc.addEventListener('mousemove', onMouseMove);\n    }\n\n    function removeListener(): void {\n      doc.removeEventListener('mousemove', onMouseMove);\n    }\n\n    function unsetGetReferenceClientRect(): void {\n      isInternalUpdate = true;\n      instance.setProps({getReferenceClientRect: null});\n      isInternalUpdate = false;\n    }\n\n    function onMouseMove(event: MouseEvent): void {\n      // If the instance is interactive, avoid updating the position unless it's\n      // over the reference element\n      const isCursorOverReference = event.target\n        ? reference.contains(event.target as Node)\n        : true;\n      const {followCursor} = instance.props;\n      const {clientX, clientY} = event;\n\n      const rect = reference.getBoundingClientRect();\n      const relativeX = clientX - rect.left;\n      const relativeY = clientY - rect.top;\n\n      if (isCursorOverReference || !instance.props.interactive) {\n        instance.setProps({\n          getReferenceClientRect() {\n            const rect = reference.getBoundingClientRect();\n\n            let x = clientX;\n            let y = clientY;\n\n            if (followCursor === 'initial') {\n              x = rect.left + relativeX;\n              y = rect.top + relativeY;\n            }\n\n            const top = followCursor === 'horizontal' ? rect.top : y;\n            const right = followCursor === 'vertical' ? rect.right : x;\n            const bottom = followCursor === 'horizontal' ? rect.bottom : y;\n            const left = followCursor === 'vertical' ? rect.left : x;\n\n            return {\n              width: right - left,\n              height: bottom - top,\n              top,\n              right,\n              bottom,\n              left,\n            };\n          },\n        });\n      }\n    }\n\n    function create(): void {\n      if (instance.props.followCursor) {\n        activeInstances.push({instance, doc});\n        addMouseCoordsListener(doc);\n      }\n    }\n\n    function destroy(): void {\n      activeInstances = activeInstances.filter(\n        (data) => data.instance !== instance\n      );\n\n      if (activeInstances.filter((data) => data.doc === doc).length === 0) {\n        removeMouseCoordsListener(doc);\n      }\n    }\n\n    return {\n      onCreate: create,\n      onDestroy: destroy,\n      onBeforeUpdate(): void {\n        prevProps = instance.props;\n      },\n      onAfterUpdate(_, {followCursor}): void {\n        if (isInternalUpdate) {\n          return;\n        }\n\n        if (\n          followCursor !== undefined &&\n          prevProps.followCursor !== followCursor\n        ) {\n          destroy();\n\n          if (followCursor) {\n            create();\n\n            if (\n              instance.state.isMounted &&\n              !wasFocusEvent &&\n              !getIsInitialBehavior()\n            ) {\n              addListener();\n            }\n          } else {\n            removeListener();\n            unsetGetReferenceClientRect();\n          }\n        }\n      },\n      onMount(): void {\n        if (instance.props.followCursor) {\n          if (isUnmounted) {\n            onMouseMove(mouseCoords as MouseEvent);\n            isUnmounted = false;\n          }\n\n          if (!wasFocusEvent && !getIsInitialBehavior()) {\n            addListener();\n          }\n        }\n      },\n      onTrigger(_, {type}): void {\n        wasFocusEvent = type === 'focus';\n      },\n      onHidden(): void {\n        if (instance.props.followCursor) {\n          unsetGetReferenceClientRect();\n          removeListener();\n          isUnmounted = true;\n        }\n      },\n    };\n  },\n};\n\nexport default followCursor;\n", "import {Modifier, Placement} from '@popperjs/core';\nimport {isMouseEvent} from '../dom-utils';\nimport {BasePlacement, InlinePositioning, Props} from '../types';\nimport {arrayFrom, getBasePlacement} from '../utils';\n\nfunction getProps(props: Props, modifier: Modifier<any, any>): Partial<Props> {\n  return {\n    popperOptions: {\n      ...props.popperOptions,\n      modifiers: [\n        ...(props.popperOptions?.modifiers || []).filter(\n          ({name}) => name !== modifier.name\n        ),\n        modifier,\n      ],\n    },\n  };\n}\n\nconst inlinePositioning: InlinePositioning = {\n  name: 'inlinePositioning',\n  defaultValue: false,\n  fn(instance) {\n    const {reference} = instance;\n\n    function isEnabled(): boolean {\n      return !!instance.props.inlinePositioning;\n    }\n\n    let placement: Placement;\n    let cursorRectIndex = -1;\n    let isInternalUpdate = false;\n\n    const modifier: Modifier<'tippyInlinePositioning', {}> = {\n      name: 'tippyInlinePositioning',\n      enabled: true,\n      phase: 'afterWrite',\n      fn({state}) {\n        if (isEnabled()) {\n          if (placement !== state.placement) {\n            instance.setProps({\n              getReferenceClientRect: () =>\n                getReferenceClientRect(state.placement),\n            });\n          }\n\n          placement = state.placement;\n        }\n      },\n    };\n\n    function getReferenceClientRect(placement: Placement): ClientRect {\n      return getInlineBoundingClientRect(\n        getBasePlacement(placement),\n        reference.getBoundingClientRect(),\n        arrayFrom(reference.getClientRects()),\n        cursorRectIndex\n      );\n    }\n\n    function setInternalProps(partialProps: Partial<Props>): void {\n      isInternalUpdate = true;\n      instance.setProps(partialProps);\n      isInternalUpdate = false;\n    }\n\n    function addModifier(): void {\n      if (!isInternalUpdate) {\n        setInternalProps(getProps(instance.props, modifier));\n      }\n    }\n\n    return {\n      onCreate: addModifier,\n      onAfterUpdate: addModifier,\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          const rects = arrayFrom(instance.reference.getClientRects());\n          const cursorRect = rects.find(\n            (rect) =>\n              rect.left - 2 <= event.clientX &&\n              rect.right + 2 >= event.clientX &&\n              rect.top - 2 <= event.clientY &&\n              rect.bottom + 2 >= event.clientY\n          );\n\n          cursorRectIndex = rects.indexOf(cursorRect);\n        }\n      },\n      onUntrigger(): void {\n        cursorRectIndex = -1;\n      },\n    };\n  },\n};\n\nexport default inlinePositioning;\n\nexport function getInlineBoundingClientRect(\n  currentBasePlacement: BasePlacement | null,\n  boundingRect: ClientRect,\n  clientRects: ClientRect[],\n  cursorRectIndex: number\n): ClientRect {\n  // Not an inline element, or placement is not yet known\n  if (clientRects.length < 2 || currentBasePlacement === null) {\n    return boundingRect;\n  }\n\n  // There are two rects and they are disjoined\n  if (\n    clientRects.length === 2 &&\n    cursorRectIndex >= 0 &&\n    clientRects[0].left > clientRects[1].right\n  ) {\n    return clientRects[cursorRectIndex] || boundingRect;\n  }\n\n  switch (currentBasePlacement) {\n    case 'top':\n    case 'bottom': {\n      const firstRect = clientRects[0];\n      const lastRect = clientRects[clientRects.length - 1];\n      const isTop = currentBasePlacement === 'top';\n\n      const top = firstRect.top;\n      const bottom = lastRect.bottom;\n      const left = isTop ? firstRect.left : lastRect.left;\n      const right = isTop ? firstRect.right : lastRect.right;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    case 'left':\n    case 'right': {\n      const minLeft = Math.min(...clientRects.map((rects) => rects.left));\n      const maxRight = Math.max(...clientRects.map((rects) => rects.right));\n      const measureRects = clientRects.filter((rect) =>\n        currentBasePlacement === 'left'\n          ? rect.left === minLeft\n          : rect.right === maxRight\n      );\n\n      const top = measureRects[0].top;\n      const bottom = measureRects[measureRects.length - 1].bottom;\n      const left = minLeft;\n      const right = maxRight;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    default: {\n      return boundingRect;\n    }\n  }\n}\n", "import {VirtualElement} from '@popperjs/core';\nimport {ReferenceElement, Sticky} from '../types';\n\nconst sticky: Sticky = {\n  name: 'sticky',\n  defaultValue: false,\n  fn(instance) {\n    const {reference, popper} = instance;\n\n    function getReference(): ReferenceElement | VirtualElement {\n      return instance.popperInstance\n        ? instance.popperInstance.state.elements.reference\n        : reference;\n    }\n\n    function shouldCheck(value: 'reference' | 'popper'): boolean {\n      return instance.props.sticky === true || instance.props.sticky === value;\n    }\n\n    let prevRefRect: ClientRect | null = null;\n    let prevPopRect: ClientRect | null = null;\n\n    function updatePosition(): void {\n      const currentRefRect = shouldCheck('reference')\n        ? getReference().getBoundingClientRect()\n        : null;\n      const currentPopRect = shouldCheck('popper')\n        ? popper.getBoundingClientRect()\n        : null;\n\n      if (\n        (currentRefRect && areRectsDifferent(prevRefRect, currentRefRect)) ||\n        (currentPopRect && areRectsDifferent(prevPopRect, currentPopRect))\n      ) {\n        if (instance.popperInstance) {\n          instance.popperInstance.update();\n        }\n      }\n\n      prevRefRect = currentRefRect;\n      prevPopRect = currentPopRect;\n\n      if (instance.state.isMounted) {\n        requestAnimationFrame(updatePosition);\n      }\n    }\n\n    return {\n      onMount(): void {\n        if (instance.props.sticky) {\n          updatePosition();\n        }\n      },\n    };\n  },\n};\n\nexport default sticky;\n\nfunction areRectsDifferent(\n  rectA: ClientRect | null,\n  rectB: ClientRect | null\n): boolean {\n  if (rectA && rectB) {\n    return (\n      rectA.top !== rectB.top ||\n      rectA.right !== rectB.right ||\n      rectA.bottom !== rectB.bottom ||\n      rectA.left !== rectB.left\n    );\n  }\n\n  return true;\n}\n", "import css from '../dist/tippy.css';\nimport {injectCSS} from '../src/css';\nimport {isBrowser} from '../src/browser';\nimport tippy, {hideAll} from '../src';\nimport createSingleton from '../src/addons/createSingleton';\nimport delegate from '../src/addons/delegate';\nimport animateFill from '../src/plugins/animateFill';\nimport followCursor from '../src/plugins/followCursor';\nimport inlinePositioning from '../src/plugins/inlinePositioning';\nimport sticky from '../src/plugins/sticky';\nimport {ROUND_ARROW} from '../src/constants';\nimport {render} from '../src/template';\n\nif (isBrowser) {\n  injectCSS(css);\n}\n\ntippy.setDefaultProps({\n  plugins: [animateFill, followCursor, inlinePositioning, sticky],\n  render,\n});\n\ntippy.createSingleton = createSingleton;\ntippy.delegate = delegate;\ntippy.hideAll = hideAll;\ntippy.roundArrow = ROUND_ARROW;\n\nexport default tippy;\n", "export function injectCSS(css: string): void {\n  const style = document.createElement('style');\n  style.textContent = css;\n  style.setAttribute('data-__NAMESPACE_PREFIX__-stylesheet', '');\n  const head = document.head;\n  const firstStyleOrLinkTag = document.querySelector('head>style,head>link');\n\n  if (firstStyleOrLinkTag) {\n    head.insertBefore(style, firstStyleOrLinkTag);\n  } else {\n    head.appendChild(style);\n  }\n}\n", "import tippy from '..';\nimport {div} from '../dom-utils';\nimport {\n  C<PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  CreateSingletonProps,\n  ReferenceElement,\n  CreateSingletonInstance,\n} from '../types';\nimport {removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\n\nconst createSingleton: CreateSingleton = (\n  tippyInstances,\n  optionalProps = {}\n) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !Array.isArray(tippyInstances),\n      [\n        'The first argument passed to createSingleton() must be an array of',\n        'tippy instances. The passed value was',\n        String(tippyInstances),\n      ].join(' ')\n    );\n  }\n\n  let mutTippyInstances = tippyInstances;\n  let references: Array<ReferenceElement> = [];\n  let currentTarget: Element;\n  let overrides = optionalProps.overrides;\n\n  function setReferences(): void {\n    references = mutTippyInstances.map((instance) => instance.reference);\n  }\n\n  function enableInstances(isEnabled: boolean): void {\n    mutTippyInstances.forEach((instance) => {\n      if (isEnabled) {\n        instance.enable();\n      } else {\n        instance.disable();\n      }\n    });\n  }\n\n  enableInstances(false);\n  setReferences();\n\n  const singleton: Plugin = {\n    fn() {\n      return {\n        onDestroy(): void {\n          enableInstances(true);\n        },\n        onTrigger(instance, event): void {\n          const target = event.currentTarget as Element;\n          const index = references.indexOf(target);\n\n          // bail-out\n          if (target === currentTarget) {\n            return;\n          }\n\n          currentTarget = target;\n\n          const overrideProps = (overrides || [])\n            .concat('content')\n            .reduce((acc, prop) => {\n              (acc as any)[prop] = mutTippyInstances[index].props[prop];\n              return acc;\n            }, {});\n\n          instance.setProps({\n            ...overrideProps,\n            getReferenceClientRect: () => target.getBoundingClientRect(),\n          });\n        },\n      };\n    },\n  };\n\n  const instance = tippy(div(), {\n    ...removeProperties(optionalProps, ['overrides']),\n    plugins: [singleton, ...(optionalProps.plugins || [])],\n    triggerTarget: references,\n  }) as CreateSingletonInstance<CreateSingletonProps>;\n\n  const originalSetProps = instance.setProps;\n\n  instance.setProps = (props): void => {\n    overrides = props.overrides || overrides;\n    originalSetProps(props);\n  };\n\n  instance.setInstances = (nextInstances): void => {\n    enableInstances(true);\n\n    mutTippyInstances = nextInstances;\n\n    enableInstances(false);\n    setReferences();\n\n    instance.setProps({triggerTarget: references});\n  };\n\n  return instance;\n};\n\nexport default createSingleton;\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "window", "document", "ua", "navigator", "userAgent", "isIE", "test", "TOUCH_OPTIONS", "passive", "capture", "getValueAtIndexOrReturn", "value", "index", "defaultValue", "Array", "isArray", "v", "isType", "type", "str", "toString", "call", "indexOf", "invokeWithArgsOrReturn", "args", "debounce", "fn", "ms", "arg", "clearTimeout", "timeout", "setTimeout", "removeProperties", "obj", "keys", "clone", "for<PERSON>ach", "key", "normalizeToArray", "concat", "pushIfUnique", "arr", "push", "getBasePlacement", "placement", "split", "arrayFrom", "slice", "div", "createElement", "isElement", "some", "isMouseEvent", "isReferenceElement", "_tippy", "reference", "getArrayOfElements", "isNodeList", "querySelectorAll", "setTransitionDuration", "els", "el", "style", "transitionDuration", "setVisibilityState", "state", "setAttribute", "getOwnerDocument", "elementOrElements", "element", "ownerDocument", "updateTransitionEndListener", "box", "action", "listener", "method", "event", "currentInput", "is<PERSON><PERSON>ch", "lastMouseMoveTime", "onDocumentTouchStart", "performance", "addEventListener", "onDocumentMouseMove", "now", "removeEventListener", "onWindowBlur", "activeElement", "instance", "blur", "isVisible", "defaultProps", "appendTo", "body", "aria", "content", "expanded", "delay", "duration", "getReferenceClientRect", "hideOnClick", "ignoreAttributes", "interactive", "interactiveBorder", "interactiveDebounce", "moveTransition", "offset", "onAfterUpdate", "onBeforeUpdate", "onCreate", "onDestroy", "onHidden", "onHide", "onMount", "onShow", "onShown", "onTrigger", "onUntrigger", "onClickOutside", "plugins", "popperOptions", "render", "showOnCreate", "touch", "trigger", "triggerTarget", "animateFill", "followCursor", "inlinePositioning", "sticky", "allowHTML", "animation", "arrow", "inertia", "max<PERSON><PERSON><PERSON>", "role", "theme", "zIndex", "defaultKeys", "Object", "getExtendedPassedProps", "passedProps", "pluginProps", "reduce", "acc", "plugin", "name", "undefined", "evaluateProps", "props", "out", "valueAsString", "getAttribute", "trim", "JSON", "parse", "e", "getDataAttributeProps", "dangerouslySetInnerHTML", "html", "createArrowElement", "className", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "popper", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxChildren", "children", "find", "node", "classList", "contains", "backdrop", "onUpdate", "prevProps", "nextProps", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "$$tippy", "idCounter", "mouseMoveListeners", "mountedInstances", "createTippy", "showTimeout", "hideTimeout", "scheduleHideAnimationFrame", "lastTriggerEvent", "currentTransitionEndListener", "onFirstUpdate", "currentTarget", "isVisibleFromClick", "didHideDueToDocumentMouseDown", "didTouchMove", "ignoreOnFirstUpdate", "listeners", "debouncedOnMouseMove", "onMouseMove", "doc", "id", "filter", "item", "popperInstance", "isEnabled", "isDestroyed", "isMounted", "isShown", "clearDelayTimeouts", "cancelAnimationFrame", "setProps", "partialProps", "invokeHook", "removeListeners", "addListeners", "cleanupInteractiveMouseListeners", "handleAriaExpandedAttribute", "handleStyles", "createPopperInstance", "getNestedPopperTree", "nestedPopper", "requestAnimationFrame", "forceUpdate", "show", "isAlreadyVisible", "isDisabled", "isTouchAndTouchDisabled", "getC<PERSON>rentTarget", "hasAttribute", "getIsDefaultRenderFn", "visibility", "addDocumentPress", "transition", "getDefaultTemplateChildren", "offsetHeight", "handleAriaContentAttribute", "callback", "onTransitionEnd", "onTransitionedIn", "parentNode", "mount", "hide", "isAlreadyHidden", "removeDocumentPress", "onTransitionedOut", "unmount", "hideWithInteractivity", "enable", "disable", "destroyPopperInstance", "i", "destroy", "pluginsHooks", "map", "hasAriaExpanded", "scheduleShow", "getNormalizedTouchSettings", "getIsCustomTouchBehavior", "_instance$props$rende", "get<PERSON>elay", "isShow", "pointerEvents", "hook", "shouldInvokePropsHook", "pluginHooks", "attr", "currentValue", "nextValue", "replace", "onDocumentPress", "target", "onTouchMove", "onTouchStart", "on", "eventType", "handler", "options", "onMouseLeave", "Boolean", "onBlurOrFocusOut", "shouldScheduleClickHide", "isEventListenerStopped", "wasFocused", "scheduleHide", "isCursorOverReferenceOrPopper", "popperTreeData", "clientX", "clientY", "every", "popperRect", "popperState", "basePlacement", "offsetData", "modifiersData", "topDistance", "top", "y", "bottomDistance", "bottom", "leftDistance", "left", "x", "rightDistance", "right", "exceedsTop", "exceedsBottom", "exceedsLeft", "exceedsRight", "isCursorOutsideInteractiveBorder", "_instance$popperInsta", "getBoundingClientRect", "relatedTarget", "computedReference", "contextElement", "modifiers", "padding", "adaptive", "enabled", "phase", "requires", "attributes", "createPopper", "touchValue", "touchDelay", "tippy", "targets", "optionalProps", "instances", "setDefaultProps", "BUBBLING_EVENTS_MAP", "mouseover", "focusin", "click", "createBackdropElement", "insertBefore", "overflow", "Number", "transitionDelay", "Math", "round", "mouseCoords", "activeInstances", "storeMouseCoords", "isInternalUpdate", "wasFocusEvent", "isUnmounted", "getIsInitialBehavior", "addListener", "removeListener", "unsetGetReferenceClientRect", "isCursorOverReference", "rect", "relativeX", "relativeY", "width", "height", "create", "addMouseCoordsListener", "data", "length", "removeMouseCoordsListener", "_", "cursorRectIndex", "modifier", "currentBasePlacement", "boundingRect", "clientRects", "firstRect", "lastRect", "isTop", "minLeft", "min", "rects", "maxRight", "max", "measureRects", "getInlineBoundingClientRect", "getClientRects", "addModifier", "getProps", "cursorRect", "<PERSON><PERSON><PERSON><PERSON>", "prevRefRect", "prevPopRect", "updatePosition", "currentRefRect", "elements", "currentPopRect", "areRectsDifferent", "update", "rectA", "rectB", "css", "head", "firstStyleOrLinkTag", "querySelector", "injectCSS", "createSingleton", "tippyInstances", "mutTippyInstances", "references", "overrides", "setReferences", "enableInstances", "singleton", "overrideProps", "prop", "originalSetProps", "setInstances", "nextInstances", "delegate", "childTippyInstances", "nativeProps", "parentProps", "childProps", "returnValue", "targetNode", "closest", "original<PERSON><PERSON>roy", "shouldDestroyChildInstances", "addEventListeners", "hide<PERSON>ll", "excludedReferenceOrInstance", "exclude", "isExcluded", "originalDuration", "roundArrow"], "mappings": "iPAAO,IAAMA,EACO,oBAAXC,QAA8C,oBAAbC,SAEpCC,EAAKH,EAAYI,UAAUC,UAAY,GAEhCC,EAAO,kBAAkBC,KAAKJ,GCI9BK,EAAgB,CAACC,SAAS,EAAMC,SAAS,YCHtCC,EACdC,EACAC,EACAC,MAEIC,MAAMC,QAAQJ,GAAQ,KAClBK,EAAIL,EAAMC,UACJ,MAALI,EACHF,MAAMC,QAAQF,GACZA,EAAaD,GACbC,EACFG,SAGCL,EAGF,SAASM,EAAON,EAAYO,OAC3BC,EAAM,GAAGC,SAASC,KAAKV,UACK,IAA3BQ,EAAIG,QAAQ,YAAoBH,EAAIG,QAAWJ,QAAY,EAG7D,SAASK,EAAuBZ,EAAYa,SACzB,mBAAVb,EAAuBA,eAASa,GAAQb,EAGjD,SAASc,EACdC,EACAC,UAGW,IAAPA,EACKD,EAKF,SAACE,GACNC,aAAaC,GACbA,EAAUC,YAAW,WACnBL,EAAGE,KACFD,QANDG,EAUC,SAASE,EAAoBC,EAAQC,OACpCC,mBAAYF,UAClBC,EAAKE,SAAQ,SAACC,UACJF,EAAcE,MAEjBF,EAOF,SAASG,EAAoB3B,SAC1B,GAAW4B,OAAO5B,GAGrB,SAAS6B,EAAgBC,EAAU9B,IACZ,IAAxB8B,EAAInB,QAAQX,IACd8B,EAAIC,KAAK/B,GAgBN,SAASgC,EAAiBC,UACxBA,EAAUC,MAAM,KAAK,GAGvB,SAASC,EAAUnC,SACjB,GAAGoC,MAAM1B,KAAKV,YCtFPqC,WACP/C,SAASgD,cAAc,OAGzB,SAASC,EAAUvC,SACjB,CAAC,UAAW,YAAYwC,MAAK,SAACjC,UAASD,EAAON,EAAOO,MAOvD,SAASkC,EAAazC,UACpBM,EAAON,EAAO,cAGhB,SAAS0C,EAAmB1C,YACvBA,IAASA,EAAM2C,QAAU3C,EAAM2C,OAAOC,YAAc5C,GAGzD,SAAS6C,EAAmB7C,UAC7BuC,EAAUvC,GACL,CAACA,GAdL,SAAoBA,UAClBM,EAAON,EAAO,YAgBjB8C,CAAW9C,GACNmC,EAAUnC,GAGfG,MAAMC,QAAQJ,GACTA,EAGFmC,EAAU7C,SAASyD,iBAAiB/C,IAGtC,SAASgD,EACdC,EACAjD,GAEAiD,EAAIxB,SAAQ,SAACyB,GACPA,IACFA,EAAGC,MAAMC,mBAAwBpD,WAKhC,SAASqD,EACdJ,EACAK,GAEAL,EAAIxB,SAAQ,SAACyB,GACPA,GACFA,EAAGK,aAAa,aAAcD,MAK7B,SAASE,EACdC,OAEOC,EAAW/B,EAAiB8B,aAC5BC,GAAUA,EAAQC,eAA4BrE,SAoChD,SAASsE,EACdC,EACAC,EACAC,OAEMC,EAAYF,mBAMjB,gBAAiB,uBAAuBrC,SAAQ,SAACwC,GAChDJ,EAAIG,GAAQC,EAAOF,UC/GVG,EAAe,CAACC,SAAS,GAClCC,EAAoB,EAQjB,SAASC,IACVH,EAAaC,UAIjBD,EAAaC,SAAU,EAEnB9E,OAAOiF,aACThF,SAASiF,iBAAiB,YAAaC,IASpC,SAASA,QACRC,EAAMH,YAAYG,MAEpBA,EAAML,EAAoB,KAC5BF,EAAaC,SAAU,EAEvB7E,SAASoF,oBAAoB,YAAaF,IAG5CJ,EAAoBK,EASf,SAASE,QACRC,EAAgBtF,SAASsF,iBAE3BlC,EAAmBkC,GAAgB,KAC/BC,EAAWD,EAAcjC,OAE3BiC,EAAcE,OAASD,EAASvB,MAAMyB,WACxCH,EAAcE,YC3BPE,iBACXC,SAAU,kBAAM3F,SAAS4F,MACzBC,KAAM,CACJC,QAAS,OACTC,SAAU,QAEZC,MAAO,EACPC,SAAU,CAAC,IAAK,KAChBC,uBAAwB,KACxBC,aAAa,EACbC,kBAAkB,EAClBC,aAAa,EACbC,kBAAmB,EACnBC,oBAAqB,EACrBC,eAAgB,GAChBC,OAAQ,CAAC,EAAG,IACZC,2BACAC,4BACAC,sBACAC,uBACAC,sBACAC,oBACAC,qBACAC,oBACAC,qBACAC,uBACAC,yBACAC,4BACA1E,UAAW,MACX2E,QAAS,GACTC,cAAe,GACfC,OAAQ,KACRC,cAAc,EACdC,OAAO,EACPC,QAAS,mBACTC,cAAe,MAtDG,CAClBC,aAAa,EACbC,cAAc,EACdC,mBAAmB,EACnBC,QAAQ,MAGU,CAClBC,WAAW,EACXC,UAAW,OACXC,OAAO,EACPrC,QAAS,GACTsC,SAAS,EACTC,SAAU,IACVC,KAAM,UACNC,MAAO,GACPC,OAAQ,OA2CJC,EAAcC,OAAOzG,KAAKyD,GAczB,SAASiD,EACdC,OAGMC,GADUD,EAAYtB,SAAW,IACXwB,QAAgC,SAACC,EAAKC,OACzDC,EAAsBD,EAAtBC,KAAMrI,EAAgBoI,EAAhBpI,oBAETqI,IACFF,EAAIE,QACoBC,IAAtBN,EAAYK,GAAsBL,EAAYK,GAAQrI,GAGnDmI,IACN,4BAGEH,KACAC,GAwCA,SAASM,EACd7F,EACA8F,OAEMC,mBACDD,GACHtD,QAASxE,EAAuB8H,EAAMtD,QAAS,CAACxC,KAC5C8F,EAAMhD,iBACN,GA5CD,SACL9C,EACAgE,UAEiBA,EACboB,OAAOzG,KAAK0G,mBAA2BjD,GAAc4B,QAAAA,MACrDmB,GAEmBK,QACrB,SAACC,EAA+C3G,OACxCkH,GACJhG,EAAUiG,2BAA2BnH,IAAU,IAC/CoH,WAEGF,SACIP,KAGG,YAAR3G,EACF2G,EAAI3G,GAAOkH,WAGTP,EAAI3G,GAAOqH,KAAKC,MAAMJ,GACtB,MAAOK,GACPZ,EAAI3G,GAAOkH,SAIRP,IAET,IAeIa,CAAsBtG,EAAW8F,EAAM9B,iBAG7C+B,EAAIxD,sBACCH,EAAaG,QACbwD,EAAIxD,MAGTwD,EAAIxD,KAAO,CACTE,SACwB,SAAtBsD,EAAIxD,KAAKE,SAAsBqD,EAAM/C,YAAcgD,EAAIxD,KAAKE,SAC9DD,QACuB,SAArBuD,EAAIxD,KAAKC,QACLsD,EAAM/C,YACJ,KACA,cACFgD,EAAIxD,KAAKC,SAGVuD,ECvJT,SAASQ,EAAwBzF,EAAkB0F,GACjD1F,EAAO,UAAgB0F,EAGzB,SAASC,EAAmBrJ,OACpByH,EAAQpF,WAEA,IAAVrC,EACFyH,EAAM6B,yBAEN7B,EAAM6B,4BAEF/G,EAAUvC,GACZyH,EAAM8B,YAAYvJ,GAElBmJ,EAAwB1B,EAAOzH,IAI5ByH,EAGF,SAAS+B,EAAWpE,EAAyBsD,GAC9CnG,EAAUmG,EAAMtD,UAClB+D,EAAwB/D,EAAS,IACjCA,EAAQmE,YAAYb,EAAMtD,UACQ,mBAAlBsD,EAAMtD,UAClBsD,EAAMnB,UACR4B,EAAwB/D,EAASsD,EAAMtD,SAEvCA,EAAQqE,YAAcf,EAAMtD,SAK3B,SAASsE,EAAYC,OACpB9F,EAAM8F,EAAOC,kBACbC,EAAc1H,EAAU0B,EAAIiG,gBAE3B,CACLjG,IAAAA,EACAuB,QAASyE,EAAYE,MAAK,SAACC,UAASA,EAAKC,UAAUC,6BACnDzC,MAAOoC,EAAYE,MACjB,SAACC,UACCA,EAAKC,UAAUC,yBACfF,EAAKC,UAAUC,+BAEnBC,SAAUN,EAAYE,MAAK,SAACC,UAC1BA,EAAKC,UAAUC,+BAKd,SAASpD,EACdjC,OAKM8E,EAAStH,IAETwB,EAAMxB,IACZwB,EAAIyF,sBACJzF,EAAIN,aAAa,aAAc,UAC/BM,EAAIN,aAAa,WAAY,UAEvB6B,EAAU/C,aAWP+H,EAASC,EAAkBC,SACJZ,EAAYC,GAAnC9F,IAAAA,IAAKuB,IAAAA,QAASqC,IAAAA,MAEjB6C,EAAUzC,MACZhE,EAAIN,aAAa,aAAc+G,EAAUzC,OAEzChE,EAAI0G,gBAAgB,cAGa,iBAAxBD,EAAU9C,UACnB3D,EAAIN,aAAa,iBAAkB+G,EAAU9C,WAE7C3D,EAAI0G,gBAAgB,kBAGlBD,EAAU5C,QACZ7D,EAAIN,aAAa,eAAgB,IAEjCM,EAAI0G,gBAAgB,gBAGtB1G,EAAIV,MAAMwE,SACsB,iBAAvB2C,EAAU3C,SACV2C,EAAU3C,cACb2C,EAAU3C,SAEZ2C,EAAU1C,KACZ/D,EAAIN,aAAa,OAAQ+G,EAAU1C,MAEnC/D,EAAI0G,gBAAgB,QAIpBF,EAAUjF,UAAYkF,EAAUlF,SAChCiF,EAAU9C,YAAc+C,EAAU/C,WAElCiC,EAAWpE,EAASP,EAAS6D,OAG3B4B,EAAU7C,MACPA,EAEM4C,EAAU5C,QAAU6C,EAAU7C,QACvC5D,EAAI2G,YAAY/C,GAChB5D,EAAI0F,YAAYF,EAAmBiB,EAAU7C,SAH7C5D,EAAI0F,YAAYF,EAAmBiB,EAAU7C,QAKtCA,GACT5D,EAAI2G,YAAY/C,UAzDpBrC,EAAQkE,0BACRlE,EAAQ7B,aAAa,aAAc,UAEnCiG,EAAWpE,EAASP,EAAS6D,OAE7BiB,EAAOJ,YAAY1F,GACnBA,EAAI0F,YAAYnE,GAEhBgF,EAASvF,EAAS6D,MAAO7D,EAAS6D,OAqD3B,CACLiB,OAAAA,EACAS,SAAAA,GAMJtD,EAAO2D,SAAU,EClHjB,IAAIC,EAAY,EACZC,EAAsD,GAG/CC,EAA+B,GAE3B,SAASC,EACtBjI,EACAsF,OLiDA5G,EKvCIwJ,EACAC,EACAC,EAKAC,EACAC,EACAC,EAGAC,ELUoBtJ,EK9BlB4G,EAAQD,EAAc7F,mBACvBoC,KACAiD,GL6CL3G,EK7CiD4G,EL+C1CF,OAAOzG,KAAKD,GAAK8G,QAAO,SAACC,EAAK3G,eAClB8G,IAAblH,EAAII,KACL2G,EAAY3G,GAAOJ,EAAII,IAGnB2G,IACN,QK5CCgD,GAAqB,EACrBC,GAAgC,EAChCC,GAAe,EACfC,GAAsB,EAItBC,EAA8B,GAC9BC,EAAuB5K,EAAS6K,GAAajD,EAAM7C,qBAEjD+F,EAAMpI,EAAiBkF,EAAMxB,eAAiBtE,GAK9CiJ,EAAKnB,IAEL9D,GLEkB9E,EKFD4G,EAAM9B,SLGlBkF,QAAO,SAACC,EAAM9L,UAAU6B,EAAInB,QAAQoL,KAAU9L,KKYnD4E,EAAqB,CAEzBgH,GAAAA,EACAjJ,UAAAA,EACA+G,OAAQtH,IACR2J,eArBqB,KAsBrBtD,MAAAA,EACApF,MApBY,CAEZ2I,WAAW,EAEXlH,WAAW,EAEXmH,aAAa,EAEbC,WAAW,EAEXC,SAAS,GAWTxF,QAAAA,EAEAyF,8BAytBAnL,aAAa4J,GACb5J,aAAa6J,GACbuB,qBAAqBtB,IA1tBrBuB,kBA6tBgBC,MAMZ3H,EAASvB,MAAM4I,mBAInBO,GAAW,iBAAkB,CAAC5H,EAAU2H,IAExCE,SAEMrC,EAAYxF,EAAS6D,MACrB4B,EAAY7B,EAAc7F,mBAC3BiC,EAAS6D,SACT8D,GACH9G,kBAAkB,KAGpBb,EAAS6D,MAAQ4B,EAEjBqC,KAEItC,EAAUxE,sBAAwByE,EAAUzE,sBAC9C+G,KACAlB,EAAuB5K,EACrB6K,GACArB,EAAUzE,sBAKVwE,EAAUnD,gBAAkBoD,EAAUpD,cACxCvF,EAAiB0I,EAAUnD,eAAezF,SAAQ,SAACuI,GACjDA,EAAKO,gBAAgB,oBAEdD,EAAUpD,eACnBtE,EAAU2H,gBAAgB,iBAG5BsC,KACAC,KAEI1C,GACFA,EAASC,EAAWC,GAGlBzF,EAASmH,iBACXe,KAMAC,KAAsBvL,SAAQ,SAACwL,GAG7BC,sBAAsBD,EAAatK,OAAQqJ,eAAgBmB,iBAI/DV,GAAW,gBAAiB,CAAC5H,EAAU2H,KA3xBvChD,oBA8xBkBpE,GAClBP,EAAS0H,SAAS,CAACnH,QAAAA,KA9xBnBgI,oBAwyBMC,EAAmBxI,EAASvB,MAAMyB,UAClCmH,EAAcrH,EAASvB,MAAM4I,YAC7BoB,GAAczI,EAASvB,MAAM2I,UAC7BsB,EACJrJ,EAAaC,UAAYU,EAAS6D,MAAM1B,MACpCzB,EAAWxF,EACf8E,EAAS6D,MAAMnD,SACf,EACAP,EAAaO,aAIb8H,GACAnB,GACAoB,GACAC,YAQEC,KAAmBC,aAAa,sBAIpChB,GAAW,SAAU,CAAC5H,IAAW,IACO,IAApCA,EAAS6D,MAAMnC,OAAO1B,UAI1BA,EAASvB,MAAMyB,WAAY,EAEvB2I,MACF/D,EAAOxG,MAAMwK,WAAa,WAG5Bb,KACAc,KAEK/I,EAASvB,MAAM6I,YAClBxC,EAAOxG,MAAM0K,WAAa,WAKxBH,IAAwB,OACHI,KAAhBjK,IAAAA,IAAKuB,IAAAA,QACZpC,EAAsB,CAACa,EAAKuB,GAAU,GAGxC+F,EAAgB,cACTtG,EAASvB,MAAMyB,YAAayG,MAIjCA,GAAsB,EAGjB7B,EAAOoE,aAEZpE,EAAOxG,MAAM0K,WAAahJ,EAAS6D,MAAM5C,eAErC4H,KAA0B7I,EAAS6D,MAAMlB,UAAW,OAC/BsG,KAAhBjK,IAAAA,IAAKuB,IAAAA,QACZpC,EAAsB,CAACa,EAAKuB,GAAUG,GACtClC,EAAmB,CAACQ,EAAKuB,GAAU,WAGrC4I,KACAnB,KAEAhL,EAAa+I,EAAkB/F,GAE/BA,EAASvB,MAAM6I,WAAY,EAC3BM,GAAW,UAAW,CAAC5H,IAEnBA,EAAS6D,MAAMlB,WAAakG,cAnmBVnI,EAAkB0I,GAC1CC,GAAgB3I,EAAU0I,GAmmBtBE,CAAiB5I,GAAU,WACzBV,EAASvB,MAAM8I,SAAU,EACzBK,GAAW,UAAW,CAAC5H,wBA7SzBuJ,EAFGnJ,EAAYJ,EAAS6D,MAArBzD,SASD+E,EAAOwD,KAMXY,EAHCvJ,EAAS6D,MAAM/C,aAAeV,IAAaD,EAAaC,UAC5C,WAAbA,EAEa+E,EAAKoE,WAELxN,EAAuBqE,EAAU,CAAC+E,IAK5CoE,EAAWlE,SAASP,IACvByE,EAAW7E,YAAYI,GAGzBoD,KA0RAsB,IA73BAC,oBAu4BMC,GAAmB1J,EAASvB,MAAMyB,UAClCmH,EAAcrH,EAASvB,MAAM4I,YAC7BoB,GAAczI,EAASvB,MAAM2I,UAC7B1G,EAAWxF,EACf8E,EAAS6D,MAAMnD,SACf,EACAP,EAAaO,aAGXgJ,GAAmBrC,GAAeoB,YAItCb,GAAW,SAAU,CAAC5H,IAAW,IACO,IAApCA,EAAS6D,MAAMrC,OAAOxB,UAI1BA,EAASvB,MAAMyB,WAAY,EAC3BF,EAASvB,MAAM8I,SAAU,EACzBZ,GAAsB,EAElBkC,MACF/D,EAAOxG,MAAMwK,WAAa,aAG5Bf,KACA4B,KACA1B,KAEIY,IAAwB,OACHI,KAAhBjK,IAAAA,IAAKuB,IAAAA,QAERP,EAAS6D,MAAMlB,YACjBxE,EAAsB,CAACa,EAAKuB,GAAUG,GACtClC,EAAmB,CAACQ,EAAKuB,GAAU,WAIvC4I,KACAnB,KAEIhI,EAAS6D,MAAMlB,UACbkG,cA5qBmBnI,EAAkB0I,GAC3CC,GAAgB3I,GAAU,YAErBV,EAASvB,MAAMyB,WAChB4E,EAAOyE,YACPzE,EAAOyE,WAAWlE,SAASP,IAE3BsE,OAsqBAQ,CAAkBlJ,EAAUV,EAAS6J,SAGvC7J,EAAS6J,WAr7BXC,+BAy7B6B1K,GAS7B2H,EAAIrH,iBAAiB,YAAamH,GAClC7J,EAAa8I,EAAoBe,GACjCA,EAAqBzH,IAn8BrB2K,kBAwsBA/J,EAASvB,MAAM2I,WAAY,GAvsB3B4C,mBA6sBAhK,EAASyJ,OACTzJ,EAASvB,MAAM2I,WAAY,GA7sB3ByC,mBA08BI7J,EAASvB,MAAMyB,WACjBF,EAASyJ,WAGNzJ,EAASvB,MAAM6I,iBAIpB2C,KAKA9B,KAAsBvL,SAAQ,SAACwL,GAC7BA,EAAatK,OAAQ+L,aAGnB/E,EAAOyE,YACTzE,EAAOyE,WAAW5D,YAAYb,GAGhCiB,EAAmBA,EAAiBkB,QAAO,SAACiD,UAAMA,IAAMlK,KAExDA,EAASvB,MAAM6I,WAAY,EAC3BM,GAAW,WAAY,CAAC5H,KAj+BxBmK,sBA0+BInK,EAASvB,MAAM4I,mBAInBrH,EAASwH,qBACTxH,EAAS6J,UAEThC,YAEO9J,EAAUD,OAEjBkC,EAASvB,MAAM4I,aAAc,EAE7BO,GAAW,YAAa,CAAC5H,UAj/BtB6D,EAAM5B,cAKFjC,QAMkB6D,EAAM5B,OAAOjC,GAAjC8E,IAAAA,OAAQS,IAAAA,SAEfT,EAAOpG,aAAa,kBAAkC,IACtDoG,EAAOkC,YAA6BhH,EAASgH,GAE7ChH,EAAS8E,OAASA,EAClB/G,EAAUD,OAASkC,EACnB8E,EAAOhH,OAASkC,MAEVoK,EAAerI,EAAQsI,KAAI,SAAC5G,UAAWA,EAAOvH,GAAG8D,MACjDsK,EAAkBvM,EAAU6K,aAAa,wBAE/Cd,KACAE,KACAC,KAEAL,GAAW,WAAY,CAAC5H,IAEpB6D,EAAM3B,cACRqI,KAKFzF,EAAOpF,iBAAiB,cAAc,WAChCM,EAAS6D,MAAM/C,aAAed,EAASvB,MAAMyB,WAC/CF,EAASwH,wBAIb1C,EAAOpF,iBAAiB,cAAc,SAACN,GAEnCY,EAAS6D,MAAM/C,aACfd,EAAS6D,MAAMzB,QAAQtG,QAAQ,eAAiB,IAEhDiL,EAAIrH,iBAAiB,YAAamH,GAClCA,EAAqBzH,OAIlBY,WAKEwK,QACArI,EAASnC,EAAS6D,MAAlB1B,aACA7G,MAAMC,QAAQ4G,GAASA,EAAQ,CAACA,EAAO,YAGvCsI,UACoC,SAApCD,IAA6B,YAG7B3B,4BAEE7I,EAAS6D,MAAM5B,eAAfyI,EAAuB9E,kBAGzB+C,YACApC,GAAiBxI,WAGjBkL,YACApE,EAAYC,YAGZ6F,GAASC,UAKb5K,EAASvB,MAAM6I,YAActH,EAASvB,MAAMyB,WAC7Cb,EAAaC,SACZ8G,GAA8C,UAA1BA,EAAiB1K,KAE/B,EAGFR,EACL8E,EAAS6D,MAAMpD,MACfmK,EAAS,EAAI,EACbzK,EAAaM,gBAIRwH,KACPnD,EAAOxG,MAAMuM,cACX7K,EAAS6D,MAAM/C,aAAed,EAASvB,MAAMyB,UAAY,GAAK,OAChE4E,EAAOxG,MAAM2E,UAAYjD,EAAS6D,MAAMZ,gBAGjC2E,GACPkD,EACA9O,EACA+O,mBAAAA,IAAAA,GAAwB,GAExBX,EAAaxN,SAAQ,SAACoO,GAChBA,EAAYF,IACdE,EAAYF,gBAAU9O,MAItB+O,OACF/K,EAAS6D,OAAMiH,WAAS9O,YAInBmN,SACA7I,EAAQN,EAAS6D,MAAjBvD,QAEFA,EAAKC,aAIJ0K,UAAe3K,EAAKC,QACpByG,EAAKlC,EAAOkC,GACJlK,EAAiBkD,EAAS6D,MAAMxB,eAAiBtE,GAEzDnB,SAAQ,SAACuI,OACP+F,EAAe/F,EAAKnB,aAAaiH,MAEnCjL,EAASvB,MAAMyB,UACjBiF,EAAKzG,aAAauM,EAAMC,EAAkBA,MAAgBlE,EAAOA,OAC5D,KACCmE,EAAYD,GAAgBA,EAAaE,QAAQpE,EAAI,IAAI/C,OAE3DkH,EACFhG,EAAKzG,aAAauM,EAAME,GAExBhG,EAAKO,gBAAgBuF,iBAMpBjD,MACHsC,GAAoBtK,EAAS6D,MAAMvD,KAAKE,UAI9B1D,EAAiBkD,EAAS6D,MAAMxB,eAAiBtE,GAEzDnB,SAAQ,SAACuI,GACTnF,EAAS6D,MAAM/C,YACjBqE,EAAKzG,aACH,gBACAsB,EAASvB,MAAMyB,WAAaiF,IAASwD,KACjC,OACA,SAGNxD,EAAKO,gBAAgB,6BAKlBqC,KACPhB,EAAIlH,oBAAoB,YAAagH,GACrCf,EAAqBA,EAAmBmB,QACtC,SAAC/H,UAAaA,IAAa2H,cAItBwE,GAAgBjM,QAEnBC,EAAaC,UACXoH,GAA+B,cAAftH,EAAM1D,OAO1BsE,EAAS6D,MAAM/C,aACfgE,EAAOO,SAASjG,EAAMkM,aAMpB3C,KAAmBtD,SAASjG,EAAMkM,QAAoB,IACpDjM,EAAaC,kBAKfU,EAASvB,MAAMyB,WACfF,EAAS6D,MAAMzB,QAAQtG,QAAQ,UAAY,cAK7C8L,GAAW,iBAAkB,CAAC5H,EAAUZ,KAGP,IAA/BY,EAAS6D,MAAMjD,cACjB4F,GAAqB,EACrBxG,EAASwH,qBACTxH,EAASyJ,OAKThD,GAAgC,EAChClK,YAAW,WACTkK,GAAgC,KAM7BzG,EAASvB,MAAM6I,WAClBqC,gBAKG4B,KACP7E,GAAe,WAGR8E,KACP9E,GAAe,WAGRqC,KACPhC,EAAIrH,iBAAiB,YAAa2L,IAAiB,GACnDtE,EAAIrH,iBAAiB,WAAY2L,GAAiBtQ,GAClDgM,EAAIrH,iBAAiB,aAAc8L,GAAczQ,GACjDgM,EAAIrH,iBAAiB,YAAa6L,GAAaxQ,YAGxC4O,KACP5C,EAAIlH,oBAAoB,YAAawL,IAAiB,GACtDtE,EAAIlH,oBAAoB,WAAYwL,GAAiBtQ,GACrDgM,EAAIlH,oBAAoB,aAAc2L,GAAczQ,GACpDgM,EAAIlH,oBAAoB,YAAa0L,GAAaxQ,YAmB3CsO,GAAgB3I,EAAkB0I,OACnCpK,EAAMiK,KAA6BjK,aAEhCE,EAASE,GACZA,EAAMkM,SAAWtM,IACnBD,EAA4BC,EAAK,SAAUE,GAC3CkK,QAMa,IAAb1I,SACK0I,IAGTrK,EAA4BC,EAAK,SAAUqH,GAC3CtH,EAA4BC,EAAK,MAAOE,GAExCmH,EAA+BnH,WAGxBuM,GACPC,EACAC,EACAC,YAAAA,IAAAA,GAA4B,GAEd9O,EAAiBkD,EAAS6D,MAAMxB,eAAiBtE,GACzDnB,SAAQ,SAACuI,GACbA,EAAKzF,iBAAiBgM,EAAWC,EAASC,GAC1ChF,EAAU1J,KAAK,CAACiI,KAAAA,EAAMuG,UAAAA,EAAWC,QAAAA,EAASC,QAAAA,gBAIrC9D,KLrWJ,IAAuB3M,EKsWtBsP,MACFgB,GAAG,aAAc7J,GAAW,CAAC5G,SAAS,IACtCyQ,GAAG,WAAYI,GAA+B,CAAC7Q,SAAS,MLxWhCG,EK2WZ6E,EAAS6D,MAAMzB,QL1WxBjH,EAAMkC,MAAM,OAAO4J,OAAO6E,UK0WOlP,SAAQ,SAAC8O,MAC3B,WAAdA,SAIJD,GAAGC,EAAW9J,IAEN8J,OACD,aACHD,GAAG,aAAcI,cAEd,QACHJ,GAAG5Q,EAAO,WAAa,OAAQkR,cAE5B,UACHN,GAAG,WAAYM,iBAMdlE,KACPjB,EAAUhK,SAAQ,gBAAEuI,IAAAA,KAAMuG,IAAAA,UAAWC,IAAAA,QAASC,IAAAA,QAC5CzG,EAAKtF,oBAAoB6L,EAAWC,EAASC,MAE/ChF,EAAY,YAGLhF,GAAUxC,SACb4M,GAA0B,KAG3BhM,EAASvB,MAAM2I,YAChB6E,GAAuB7M,KACvBqH,OAKIyF,EAAwC,oBAA3B9F,YAAkB1K,MAErC0K,EAAmBhH,EACnBmH,EAAgBnH,EAAMmH,cAEtByB,MAEKhI,EAASvB,MAAMyB,WAAatC,EAAawB,IAK5C0G,EAAmBlJ,SAAQ,SAACsC,UAAaA,EAASE,MAKnC,UAAfA,EAAM1D,OACLsE,EAAS6D,MAAMzB,QAAQtG,QAAQ,cAAgB,GAC9C0K,KAC6B,IAA/BxG,EAAS6D,MAAMjD,aACfZ,EAASvB,MAAMyB,UAEf8L,GAA0B,EAE1BzB,GAAanL,GAGI,UAAfA,EAAM1D,OACR8K,GAAsBwF,GAGpBA,IAA4BE,GAC9BC,GAAa/M,aAIR0H,GAAY1H,OACbkM,EAASlM,EAAMkM,OACfc,EACJrO,EAAUsH,SAASiG,IAAWxG,EAAOO,SAASiG,GAE7B,cAAflM,EAAM1D,MAAwB0Q,GJlb/B,SACLC,EACAjN,OAEOkN,EAAoBlN,EAApBkN,QAASC,EAAWnN,EAAXmN,eAETF,EAAeG,OAAM,gBAAEC,IAAAA,WAAYC,IAAAA,YACjC3L,IAD8C8C,MAC9C9C,kBACD4L,EAAgBxP,EAAiBuP,EAAYtP,WAC7CwP,EAAaF,EAAYG,cAAc3L,WAExC0L,SACI,MAGHE,EAAgC,WAAlBH,EAA6BC,EAAWG,IAAKC,EAAI,EAC/DC,EAAmC,QAAlBN,EAA0BC,EAAWM,OAAQF,EAAI,EAClEG,EAAiC,UAAlBR,EAA4BC,EAAWQ,KAAMC,EAAI,EAChEC,EAAkC,SAAlBX,EAA2BC,EAAWW,MAAOF,EAAI,EAEjEG,EACJf,EAAWM,IAAMR,EAAUO,EAAc/L,EACrC0M,EACJlB,EAAUE,EAAWS,OAASD,EAAiBlM,EAC3C2M,EACJjB,EAAWW,KAAOd,EAAUa,EAAepM,EACvC4M,EACJrB,EAAUG,EAAWc,MAAQD,EAAgBvM,SAExCyM,GAAcC,GAAiBC,GAAeC,KI2ajDC,CAlBmBzF,KACpBpL,OAAO+H,GACPuF,KAAI,SAACvF,SAEErG,WADWqG,EAAOhH,OACDqJ,uBAAT0G,EAAyBpP,aAEnCA,EACK,CACLgO,WAAY3H,EAAOgJ,wBACnBpB,YAAajO,EACboF,MAAAA,GAIG,QAERoD,OAAO6E,SAE2C1M,KACnD2I,KACAoE,GAAa/M,aAIRyM,GAAazM,GAElB6M,GAAuB7M,IACtBY,EAAS6D,MAAMzB,QAAQtG,QAAQ,UAAY,GAAK0K,IAM/CxG,EAAS6D,MAAM/C,YACjBd,EAAS8J,sBAAsB1K,GAIjC+M,GAAa/M,aAGN2M,GAAiB3M,GAEtBY,EAAS6D,MAAMzB,QAAQtG,QAAQ,WAAa,GAC5CsD,EAAMkM,SAAW3C,MAOjB3I,EAAS6D,MAAM/C,aACf1B,EAAM2O,eACNjJ,EAAOO,SAASjG,EAAM2O,gBAKxB5B,GAAa/M,YAGN6M,GAAuB7M,WACvBC,EAAaC,SAChBmL,MAA+BrL,EAAM1D,KAAKI,QAAQ,UAAY,WAI3DoM,KACP+B,WAQIjK,EAAS6D,MALX7B,IAAAA,cACA5E,IAAAA,UACA8D,IAAAA,OACAP,IAAAA,uBACAM,IAAAA,eAGI2B,EAAQiG,IAAyBhE,EAAYC,GAAQlC,MAAQ,KAE7DoL,EAAoBrN,EACtB,CACEmN,sBAAuBnN,EACvBsN,eACEtN,EAAuBsN,gBAAkBtF,MAE7C5K,EA+BEmQ,EAAsC,CAC1C,CACExK,KAAM,SACNkI,QAAS,CACP1K,OAAAA,IAGJ,CACEwC,KAAM,kBACNkI,QAAS,CACPuC,QAAS,CACPpB,IAAK,EACLG,OAAQ,EACRE,KAAM,EACNG,MAAO,KAIb,CACE7J,KAAM,OACNkI,QAAS,CACPuC,QAAS,IAGb,CACEzK,KAAM,gBACNkI,QAAS,CACPwC,UAAWnN,IAxD8B,CAC7CyC,KAAM,UACN2K,SAAS,EACTC,MAAO,cACPC,SAAU,CAAC,iBACXrS,mBAAIuC,IAAAA,SACEoK,IAAwB,KACnB7J,EAAOiK,KAAPjK,KAEN,YAAa,mBAAoB,WAAWpC,SAAQ,SAACqO,GACvC,cAATA,EACFjM,EAAIN,aAAa,iBAAkBD,EAAMrB,WAErCqB,EAAM+P,WAAW1J,sBAAsBmG,GACzCjM,EAAIN,qBAAqBuM,EAAQ,IAEjCjM,EAAI0G,wBAAwBuF,MAKlCxM,EAAM+P,WAAW1J,OAAS,OAyC5B+D,KAA0BjG,GAC5BsL,EAAUhR,KAAK,CACbwG,KAAM,QACNkI,QAAS,CACP/M,QAAS+D,EACTuL,QAAS,KAKfD,EAAUhR,WAAVgR,SAAmBlM,SAAAA,EAAekM,YAAa,IAE/ClO,EAASmH,eAAiBsH,eACxBT,EACAlJ,mBAEK9C,GACH5E,UAAAA,EACAkJ,cAAAA,EACA4H,UAAAA,cAKGjE,KACHjK,EAASmH,iBACXnH,EAASmH,eAAegD,UACxBnK,EAASmH,eAAiB,eA0DrBgB,YACA7K,EACLwH,EAAO5G,iBAAiB,+BAInBqM,GAAanL,GACpBY,EAASwH,qBAELpI,GACFwI,GAAW,YAAa,CAAC5H,EAAUZ,IAGrC2J,SAEItI,EAAQkK,IAAS,KACYH,IAA1BkE,OAAYC,OAEftP,EAAaC,SAA0B,SAAfoP,GAAyBC,IACnDlO,EAAQkO,GAGNlO,EACFwF,EAAc1J,YAAW,WACvByD,EAASuI,SACR9H,GAEHT,EAASuI,gBAIJ4D,GAAa/M,MACpBY,EAASwH,qBAETI,GAAW,cAAe,CAAC5H,EAAUZ,IAEhCY,EAASvB,MAAMyB,gBAWlBF,EAAS6D,MAAMzB,QAAQtG,QAAQ,eAAiB,GAChDkE,EAAS6D,MAAMzB,QAAQtG,QAAQ,UAAY,GAC3C,CAAC,aAAc,aAAaA,QAAQsD,EAAM1D,OAAS,GACnD8K,QAKI/F,EAAQkK,IAAS,GAEnBlK,EACFyF,EAAc3J,YAAW,WACnByD,EAASvB,MAAMyB,WACjBF,EAASyJ,SAEVhJ,GAIH0F,EAA6BkC,uBAAsB,WACjDrI,EAASyJ,gBA9BXE,MClwBN,SAASiF,EACPC,EACAC,YAAAA,IAAAA,EAAgC,QAE1B/M,EAAU5B,EAAa4B,QAAQhF,OAAO+R,EAAc/M,SAAW,IJ+CrEtH,SAASiF,iBAAiB,aAAcF,EAAsBzE,GAC9DP,OAAOkF,iBAAiB,OAAQI,OItC1BuD,mBAAkCyL,GAAe/M,QAAAA,IAwBjDgN,EAtBW/Q,EAAmB6Q,GAsBTtL,QACzB,SAACC,EAAKzF,OACEiC,EAAWjC,GAAaiI,EAAYjI,EAAWsF,UAEjDrD,GACFwD,EAAItG,KAAK8C,GAGJwD,IAET,WAGK9F,EAAUmR,GAAWE,EAAU,GAAKA,EAG7CH,EAAMzO,aAAeA,EACrByO,EAAMI,gBHKmD,SAACrH,GAM3CxE,OAAOzG,KAAKiL,GACpB/K,SAAQ,SAACC,GACXsD,EAAqBtD,GAAO8K,EAAa9K,OGZ9C+R,EAAMvP,aAAeA,EAId,IC9DD4P,EAAsB,CAC1BC,UAAW,aACXC,QAAS,QACTC,MAAO,SCJT,IAAM9M,EAA2B,CAC/BoB,KAAM,cACNrI,cAAc,EACda,YAAG8D,uBAEIA,EAAS6D,MAAM5B,eAAfyI,EAAuB9E,eAQnB,SAGcf,EAAY7E,EAAS8E,QAArC9F,IAAAA,IAAKuB,IAAAA,QAEN+E,EAAWtF,EAAS6D,MAAMvB,YA4CpC,eACQgD,EAAW9H,WACjB8H,EAASb,2BACTjG,EAAmB,CAAC8G,GAAW,UACxBA,EA/CD+J,GACA,WAEG,CACLhO,oBACMiE,IACFtG,EAAIsQ,aAAahK,EAAUtG,EAAI+F,mBAC/B/F,EAAIN,aAAa,mBAAoB,IACrCM,EAAIV,MAAMiR,SAAW,SAErBvP,EAAS0H,SAAS,CAAC9E,OAAO,EAAOD,UAAW,iBAGhDlB,sBACM6D,EAAU,KACL/G,EAAsBS,EAAIV,MAA1BC,mBACDmC,EAAW8O,OAAOjR,EAAmB6M,QAAQ,KAAM,KAKzD7K,EAAQjC,MAAMmR,gBAAqBC,KAAKC,MAAMjP,EAAW,SAEzD4E,EAAShH,MAAMC,mBAAqBA,EACpCC,EAAmB,CAAC8G,GAAW,aAGnC5D,kBACM4D,IACFA,EAAShH,MAAMC,mBAAqB,QAGxCiD,kBACM8D,GACF9G,EAAmB,CAAC8G,GAAW,kBCxDrCsK,EAAc,CAACtD,QAAS,EAAGC,QAAS,GACpCsD,EAA8D,GAElE,SAASC,SAAkBxD,IAAAA,QAASC,IAAAA,QAClCqD,EAAc,CAACtD,QAAAA,EAASC,QAAAA,GAW1B,IAAMhK,EAA6B,CACjCmB,KAAM,eACNrI,cAAc,EACda,YAAG8D,OACKjC,EAAYiC,EAASjC,UACrBgJ,EAAMpI,EAAiBqB,EAAS6D,MAAMxB,eAAiBtE,GAEzDgS,GAAmB,EACnBC,GAAgB,EAChBC,GAAc,EACdzK,EAAYxF,EAAS6D,eAEhBqM,UAE2B,YAAhClQ,EAAS6D,MAAMtB,cAA8BvC,EAASvB,MAAMyB,mBAIvDiQ,IACPpJ,EAAIrH,iBAAiB,YAAaoH,YAG3BsJ,IACPrJ,EAAIlH,oBAAoB,YAAaiH,YAG9BuJ,IACPN,GAAmB,EACnB/P,EAAS0H,SAAS,CAAC/G,uBAAwB,OAC3CoP,GAAmB,WAGZjJ,EAAY1H,OAGbkR,GAAwBlR,EAAMkM,QAChCvN,EAAUsH,SAASjG,EAAMkM,QAEtB/I,EAAgBvC,EAAS6D,MAAzBtB,aACA+J,EAAoBlN,EAApBkN,QAASC,EAAWnN,EAAXmN,QAEVgE,EAAOxS,EAAU+P,wBACjB0C,EAAYlE,EAAUiE,EAAKnD,KAC3BqD,EAAYlE,EAAUgE,EAAKxD,KAE7BuD,GAA0BtQ,EAAS6D,MAAM/C,aAC3Cd,EAAS0H,SAAS,CAChB/G,sCACQ4P,EAAOxS,EAAU+P,wBAEnBT,EAAIf,EACJU,EAAIT,EAEa,YAAjBhK,IACF8K,EAAIkD,EAAKnD,KAAOoD,EAChBxD,EAAIuD,EAAKxD,IAAM0D,OAGX1D,EAAuB,eAAjBxK,EAAgCgO,EAAKxD,IAAMC,EACjDO,EAAyB,aAAjBhL,EAA8BgO,EAAKhD,MAAQF,EACnDH,EAA0B,eAAjB3K,EAAgCgO,EAAKrD,OAASF,EACvDI,EAAwB,aAAjB7K,EAA8BgO,EAAKnD,KAAOC,QAEhD,CACLqD,MAAOnD,EAAQH,EACfuD,OAAQzD,EAASH,EACjBA,IAAAA,EACAQ,MAAAA,EACAL,OAAAA,EACAE,KAAAA,eAODwD,IACH5Q,EAAS6D,MAAMtB,eACjBsN,EAAgB3S,KAAK,CAAC8C,SAAAA,EAAU+G,IAAAA,IAtFxC,SAAgCA,GAC9BA,EAAIrH,iBAAiB,YAAaoQ,GAsF5Be,CAAuB9J,aAIlBoD,IAK2D,KAJlE0F,EAAkBA,EAAgB5I,QAChC,SAAC6J,UAASA,EAAK9Q,WAAaA,MAGViH,QAAO,SAAC6J,UAASA,EAAK/J,MAAQA,KAAKgK,QA5F7D,SAAmChK,GACjCA,EAAIlH,oBAAoB,YAAaiQ,GA4F/BkB,CAA0BjK,SAIvB,CACL1F,SAAUuP,EACVtP,UAAW6I,EACX/I,0BACEoE,EAAYxF,EAAS6D,OAEvB1C,uBAAc8P,SAAI1O,IAAAA,aACZwN,QAKepM,IAAjBpB,GACAiD,EAAUjD,eAAiBA,IAE3B4H,IAEI5H,GACFqO,KAGE5Q,EAASvB,MAAM6I,WACd0I,GACAE,KAEDC,MAGFC,IACAC,OAIN5O,mBACMzB,EAAS6D,MAAMtB,eACb0N,IACFnJ,EAAY8I,GACZK,GAAc,GAGXD,GAAkBE,KACrBC,MAINvO,mBAAUqP,SAAIvV,IAAAA,KACZsU,EAAyB,UAATtU,GAElB6F,oBACMvB,EAAS6D,MAAMtB,eACjB8N,IACAD,IACAH,GAAc,OChJxB,IAAMzN,EAAuC,CAC3CkB,KAAM,oBACNrI,cAAc,EACda,YAAG8D,OAOG5C,EANGW,EAAaiC,EAAbjC,cAOHmT,GAAmB,EACnBnB,GAAmB,EAEjBoB,EAAmD,CACvDzN,KAAM,yBACN2K,SAAS,EACTC,MAAO,aACPpS,mBAAIuC,IAAAA,MAXKuB,EAAS6D,MAAMrB,oBAahBpF,IAAcqB,EAAMrB,WACtB4C,EAAS0H,SAAS,CAChB/G,uBAAwB,2BAUFvD,UA+C7B,SACLgU,EACAC,EACAC,EACAJ,MAGII,EAAYP,OAAS,GAA8B,OAAzBK,SACrBC,KAKgB,IAAvBC,EAAYP,QACZG,GAAmB,GACnBI,EAAY,GAAGlE,KAAOkE,EAAY,GAAG/D,aAE9B+D,EAAYJ,IAAoBG,SAGjCD,OACD,UACA,aACGG,EAAYD,EAAY,GACxBE,EAAWF,EAAYA,EAAYP,OAAS,GAC5CU,EAAiC,QAAzBL,EAERrE,EAAMwE,EAAUxE,IAChBG,EAASsE,EAAStE,OAClBE,EAAOqE,EAAQF,EAAUnE,KAAOoE,EAASpE,KACzCG,EAAQkE,EAAQF,EAAUhE,MAAQiE,EAASjE,YAI1C,CAACR,IAAAA,EAAKG,OAAAA,EAAQE,KAAAA,EAAMG,MAAAA,EAAOmD,MAHpBnD,EAAQH,EAGmBuD,OAF1BzD,EAASH,OAIrB,WACA,YACG2E,EAAUhC,KAAKiC,UAALjC,KAAY4B,EAAYjH,KAAI,SAACuH,UAAUA,EAAMxE,SACvDyE,EAAWnC,KAAKoC,UAALpC,KAAY4B,EAAYjH,KAAI,SAACuH,UAAUA,EAAMrE,UACxDwE,EAAeT,EAAYrK,QAAO,SAACsJ,SACd,SAAzBa,EACIb,EAAKnD,OAASsE,EACdnB,EAAKhD,QAAUsE,KAGf9E,EAAMgF,EAAa,GAAGhF,IACtBG,EAAS6E,EAAaA,EAAahB,OAAS,GAAG7D,aAM9C,CAACH,IAAAA,EAAKG,OAAAA,EAAQE,KALRsE,EAKcnE,MAJbsE,EAIoBnB,MAJpBmB,EADDH,EAK4Bf,OAF1BzD,EAASH,kBAKjBsE,GAtGAW,CACL7U,EAAiBC,GACjBW,EAAU+P,wBACVxQ,EAAUS,EAAUkU,kBACpBf,GAdQvQ,CAAuBlC,EAAMrB,cAInCA,EAAYqB,EAAMrB,sBAoBf8U,QANiBvK,EAOnBoI,IAPmBpI,EAvD9B,SAAkB9D,EAAcsN,eACvB,CACLnP,+BACK6B,EAAM7B,eACTkM,+BACMrK,EAAM7B,wBAAekM,YAAa,IAAIjH,QACxC,qBAAEvD,OAAmByN,EAASzN,SAEhCyN,OAuDiBgB,CAASnS,EAAS6D,MAAOsN,GAP5CpB,GAAmB,EACnB/P,EAAS0H,SAASC,GAClBoI,GAAmB,SASd,CACL1O,SAAU6Q,EACV/Q,cAAe+Q,EACftQ,mBAAUqP,EAAG7R,MACPxB,EAAawB,GAAQ,KACjBwS,EAAQtU,EAAU0C,EAASjC,UAAUkU,kBACrCG,EAAaR,EAAM1M,MACvB,SAACqL,UACCA,EAAKnD,KAAO,GAAKhO,EAAMkN,SACvBiE,EAAKhD,MAAQ,GAAKnO,EAAMkN,SACxBiE,EAAKxD,IAAM,GAAK3N,EAAMmN,SACtBgE,EAAKrD,OAAS,GAAK9N,EAAMmN,WAG7B2E,EAAkBU,EAAM9V,QAAQsW,KAGpCvQ,uBACEqP,GAAmB,MCvF3B,IAAMzO,EAAiB,CACrBiB,KAAM,SACNrI,cAAc,EACda,YAAG8D,OACMjC,EAAqBiC,EAArBjC,UAAW+G,EAAU9E,EAAV8E,gBAQTuN,EAAYlX,UACc,IAA1B6E,EAAS6D,MAAMpB,QAAmBzC,EAAS6D,MAAMpB,SAAWtH,MAGjEmX,EAAiC,KACjCC,EAAiC,cAE5BC,QACDC,EAAiBJ,EAAY,cAb5BrS,EAASmH,eACZnH,EAASmH,eAAe1I,MAAMiU,SAAS3U,UACvCA,GAYe+P,wBACf,KACE6E,EAAiBN,EAAY,UAC/BvN,EAAOgJ,wBACP,MAGD2E,GAAkBG,EAAkBN,EAAaG,IACjDE,GAAkBC,EAAkBL,EAAaI,KAE9C3S,EAASmH,gBACXnH,EAASmH,eAAe0L,SAI5BP,EAAcG,EACdF,EAAcI,EAEV3S,EAASvB,MAAM6I,WACjBe,sBAAsBmK,SAInB,CACL/Q,mBACMzB,EAAS6D,MAAMpB,QACjB+P,QASV,SAASI,EACPE,EACAC,UAEID,IAASC,IAETD,EAAM/F,MAAQgG,EAAMhG,KACpB+F,EAAMvF,QAAUwF,EAAMxF,OACtBuF,EAAM5F,SAAW6F,EAAM7F,QACvB4F,EAAM1F,OAAS2F,EAAM3F,aCvDvB7S,GCbG,SAAmByY,OAClB1U,EAAQ7D,SAASgD,cAAc,SACrCa,EAAMsG,YAAcoO,EACpB1U,EAAMI,aAAa,wBAAwC,QACrDuU,EAAOxY,SAASwY,KAChBC,EAAsBzY,SAAS0Y,cAAc,wBAE/CD,EACFD,EAAK3D,aAAahR,EAAO4U,GAEzBD,EAAKvO,YAAYpG,GDInB8U,m3CAGFxE,EAAMI,gBAAgB,CACpBjN,QAAS,CAACO,EAAaC,EAAcC,EAAmBC,GACxDR,OAAAA,IAGF2M,EAAMyE,gBEVmC,SACvCC,EACAxE,YAAAA,IAAAA,EAAgB,QAgBZvI,EAFAgN,EAAoBD,EACpBE,EAAsC,GAEtCC,EAAY3E,EAAc2E,mBAErBC,IACPF,EAAaD,EAAkBlJ,KAAI,SAACrK,UAAaA,EAASjC,sBAGnD4V,EAAgBvM,GACvBmM,EAAkB3W,SAAQ,SAACoD,GACrBoH,EACFpH,EAAS+J,SAET/J,EAASgK,aAKf2J,GAAgB,GAChBD,QAEME,EAAoB,CACxB1X,oBACS,CACLoF,qBACEqS,GAAgB,IAElB/R,mBAAU5B,EAAUZ,OACZkM,EAASlM,EAAMmH,cACfnL,EAAQoY,EAAW1X,QAAQwP,MAG7BA,IAAW/E,GAIfA,EAAgB+E,MAEVuI,GAAiBJ,GAAa,IACjC1W,OAAO,WACPwG,QAAO,SAACC,EAAKsQ,UACXtQ,EAAYsQ,GAAQP,EAAkBnY,GAAOyI,MAAMiQ,GAC7CtQ,IACN,IAELxD,EAAS0H,0BACJmM,GACHlT,uBAAwB,kBAAM2K,EAAOwC,iCAOzC9N,EAAW4O,EAAMpR,qBAClBhB,EAAiBsS,EAAe,CAAC,eACpC/M,SAAU6R,UAAe9E,EAAc/M,SAAW,IAClDM,cAAemR,KAGXO,EAAmB/T,EAAS0H,gBAElC1H,EAAS0H,SAAW,SAAC7D,GACnB4P,EAAY5P,EAAM4P,WAAaA,EAC/BM,EAAiBlQ,IAGnB7D,EAASgU,aAAe,SAACC,GACvBN,GAAgB,GAEhBJ,EAAoBU,EAEpBN,GAAgB,GAChBD,IAEA1T,EAAS0H,SAAS,CAACrF,cAAemR,KAG7BxT,GFpFT4O,EAAMsF,SLNN,SACErF,EACAhL,OAaI+C,EAA8B,GAC9BuN,EAAkC,GAE/B7I,EAAUzH,EAAVyH,OAED8I,EAAc5X,EAAiBqH,EAAO,CAAC,WACvCwQ,mBAAkBD,GAAahS,QAAS,SAAUD,OAAO,IACzDmS,mBAAiBF,GAAalS,cAAc,IAE5CqS,EAAc3F,EAAMC,EAASwF,YAG1BzS,EAAUxC,MACZA,EAAMkM,YAILkJ,EAAcpV,EAAMkM,OAAmBmJ,QAAQnJ,MAEhDkJ,OAQCpS,EACJoS,EAAWxQ,aAAa,uBACxBH,EAAMzB,SACNjC,EAAaiC,YAGXoS,EAAW1W,UAII,eAAfsB,EAAM1D,MAAqD,kBAArB4Y,EAAWnS,OAKpC,eAAf/C,EAAM1D,MACN0G,EAAQtG,QAASmT,EAA4B7P,EAAM1D,aAK/CsE,EAAW4O,EAAM4F,EAAYF,GAE/BtU,IACFmU,EAAsBA,EAAoBpX,OAAOiD,gBAI5CyL,EACPtG,EACAuG,EACAC,EACAC,YAAAA,IAAAA,GAA4B,GAE5BzG,EAAKzF,iBAAiBgM,EAAWC,EAASC,GAC1ChF,EAAU1J,KAAK,CAACiI,KAAAA,EAAMuG,UAAAA,EAAWC,QAAAA,EAASC,QAAAA,WApDd9O,EAAiByX,GAyFzB3X,kBAlBEoD,OAChB0U,EAAkB1U,EAASmK,QACjCnK,EAASmK,QAAU,SAACwK,YAAAA,IAAAA,GAA8B,GAC5CA,GACFR,EAAoBvX,SAAQ,SAACoD,GAC3BA,EAASmK,aAIbgK,EAAsB,GAfxBvN,EAAUhK,SAAQ,gBAAEuI,IAAAA,KAAMuG,IAAAA,UAAWC,IAAAA,QAASC,IAAAA,QAC5CzG,EAAKtF,oBAAoB6L,EAAWC,EAASC,MAE/ChF,EAAY,GAeV8N,cA5BuB1U,OAClBjC,EAAaiC,EAAbjC,UAEP0N,EAAG1N,EAAW,aAAc6D,GAC5B6J,EAAG1N,EAAW,YAAa6D,GAC3B6J,EAAG1N,EAAW,UAAW6D,GACzB6J,EAAG1N,EAAW,QAAS6D,GAyBvBgT,CAAkB5U,MAKbuU,GK7GT3F,EAAMiG,QN6C0B,6BAGZ,KAFTC,IAATC,QACArU,IAAAA,SAEAqF,EAAiBnJ,SAAQ,SAACoD,OACpBgV,GAAa,KAEbF,IACFE,EAAanX,EAAmBiX,GAC5B9U,EAASjC,YAAc+W,EACvB9U,EAAS8E,SAAYgQ,EAAyChQ,SAG/DkQ,EAAY,KACTC,EAAmBjV,EAAS6D,MAAMnD,SAExCV,EAAS0H,SAAS,CAAChH,SAAAA,IACnBV,EAASyJ,OAEJzJ,EAASvB,MAAM4I,aAClBrH,EAAS0H,SAAS,CAAChH,SAAUuU,SMhErCrG,EAAMsG,WbxBJ"}