{"version": 3, "file": "tippy.cjs.js", "sources": ["../src/constants.ts", "../src/utils.ts", "../src/dom-utils.ts", "../src/bindGlobalEventListeners.ts", "../src/browser.ts", "../src/validation.ts", "../src/props.ts", "../src/template.ts", "../src/createTippy.ts", "../src/index.ts", "../src/addons/createSingleton.ts", "../src/addons/delegate.ts", "../src/plugins/animateFill.ts", "../src/plugins/followCursor.ts", "../src/plugins/inlinePositioning.ts", "../src/plugins/sticky.ts", "../build/base.js"], "sourcesContent": ["export const ROUND_ARROW =\n  '<svg width=\"16\" height=\"6\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z\"></svg>';\n\nexport const BOX_CLASS = `__NAMESPACE_PREFIX__-box`;\nexport const CONTENT_CLASS = `__NAMESPACE_PREFIX__-content`;\nexport const BACKDROP_CLASS = `__NAMESPACE_PREFIX__-backdrop`;\nexport const ARROW_CLASS = `__NAMESPACE_PREFIX__-arrow`;\nexport const SVG_ARROW_CLASS = `__NAMESPACE_PREFIX__-svg-arrow`;\n\nexport const TOUCH_OPTIONS = {passive: true, capture: true};\n", "import {BasePlacement, Placement} from './types';\n\nexport function hasOwnProperty(obj: object, key: string): boolean {\n  return {}.hasOwnProperty.call(obj, key);\n}\n\nexport function getValueAtIndexOrReturn<T>(\n  value: T | [T | null, T | null],\n  index: number,\n  defaultValue: T | [T, T]\n): T {\n  if (Array.isArray(value)) {\n    const v = value[index];\n    return v == null\n      ? Array.isArray(defaultValue)\n        ? defaultValue[index]\n        : defaultValue\n      : v;\n  }\n\n  return value;\n}\n\nexport function isType(value: any, type: string): boolean {\n  const str = {}.toString.call(value);\n  return str.indexOf('[object') === 0 && str.indexOf(`${type}]`) > -1;\n}\n\nexport function invokeWithArgsOrReturn(value: any, args: any[]): any {\n  return typeof value === 'function' ? value(...args) : value;\n}\n\nexport function debounce<T>(\n  fn: (arg: T) => void,\n  ms: number\n): (arg: T) => void {\n  // Avoid wrapping in `setTimeout` if ms is 0 anyway\n  if (ms === 0) {\n    return fn;\n  }\n\n  let timeout: any;\n\n  return (arg): void => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => {\n      fn(arg);\n    }, ms);\n  };\n}\n\nexport function removeProperties<T>(obj: T, keys: string[]): Partial<T> {\n  const clone = {...obj};\n  keys.forEach((key) => {\n    delete (clone as any)[key];\n  });\n  return clone;\n}\n\nexport function splitBySpaces(value: string): string[] {\n  return value.split(/\\s+/).filter(Boolean);\n}\n\nexport function normalizeToArray<T>(value: T | T[]): T[] {\n  return ([] as T[]).concat(value);\n}\n\nexport function pushIfUnique<T>(arr: T[], value: T): void {\n  if (arr.indexOf(value) === -1) {\n    arr.push(value);\n  }\n}\n\nexport function appendPxIfNumber(value: string | number): string {\n  return typeof value === 'number' ? `${value}px` : value;\n}\n\nexport function unique<T>(arr: T[]): T[] {\n  return arr.filter((item, index) => arr.indexOf(item) === index);\n}\n\nexport function getNumber(value: string | number): number {\n  return typeof value === 'number' ? value : parseFloat(value);\n}\n\nexport function getBasePlacement(placement: Placement): BasePlacement {\n  return placement.split('-')[0] as BasePlacement;\n}\n\nexport function arrayFrom(value: ArrayLike<any>): any[] {\n  return [].slice.call(value);\n}\n\nexport function removeUndefinedProps(\n  obj: Record<string, unknown>\n): Partial<Record<string, unknown>> {\n  return Object.keys(obj).reduce((acc, key) => {\n    if (obj[key] !== undefined) {\n      (acc as any)[key] = obj[key];\n    }\n\n    return acc;\n  }, {});\n}\n", "import {ReferenceElement, Targets} from './types';\nimport {PopperTreeData} from './types-internal';\nimport {arrayFrom, isType, normalizeToArray, getBasePlacement} from './utils';\n\nexport function div(): HTMLDivElement {\n  return document.createElement('div');\n}\n\nexport function isElement(value: unknown): value is Element | DocumentFragment {\n  return ['Element', 'Fragment'].some((type) => isType(value, type));\n}\n\nexport function isNodeList(value: unknown): value is NodeList {\n  return isType(value, 'NodeList');\n}\n\nexport function isMouseEvent(value: unknown): value is MouseEvent {\n  return isType(value, 'MouseEvent');\n}\n\nexport function isReferenceElement(value: any): value is ReferenceElement {\n  return !!(value && value._tippy && value._tippy.reference === value);\n}\n\nexport function getArrayOfElements(value: Targets): Element[] {\n  if (isElement(value)) {\n    return [value];\n  }\n\n  if (isNodeList(value)) {\n    return arrayFrom(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return arrayFrom(document.querySelectorAll(value));\n}\n\nexport function setTransitionDuration(\n  els: (HTMLDivElement | null)[],\n  value: number\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.style.transitionDuration = `${value}ms`;\n    }\n  });\n}\n\nexport function setVisibilityState(\n  els: (HTMLDivElement | null)[],\n  state: 'visible' | 'hidden'\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.setAttribute('data-state', state);\n    }\n  });\n}\n\nexport function getOwnerDocument(\n  elementOrElements: Element | Element[]\n): Document {\n  const [element] = normalizeToArray(elementOrElements);\n  return element ? element.ownerDocument || document : document;\n}\n\nexport function isCursorOutsideInteractiveBorder(\n  popperTreeData: PopperTreeData[],\n  event: MouseEvent\n): boolean {\n  const {clientX, clientY} = event;\n\n  return popperTreeData.every(({popperRect, popperState, props}) => {\n    const {interactiveBorder} = props;\n    const basePlacement = getBasePlacement(popperState.placement);\n    const offsetData = popperState.modifiersData.offset;\n\n    if (!offsetData) {\n      return true;\n    }\n\n    const topDistance = basePlacement === 'bottom' ? offsetData.top!.y : 0;\n    const bottomDistance = basePlacement === 'top' ? offsetData.bottom!.y : 0;\n    const leftDistance = basePlacement === 'right' ? offsetData.left!.x : 0;\n    const rightDistance = basePlacement === 'left' ? offsetData.right!.x : 0;\n\n    const exceedsTop =\n      popperRect.top - clientY + topDistance > interactiveBorder;\n    const exceedsBottom =\n      clientY - popperRect.bottom - bottomDistance > interactiveBorder;\n    const exceedsLeft =\n      popperRect.left - clientX + leftDistance > interactiveBorder;\n    const exceedsRight =\n      clientX - popperRect.right - rightDistance > interactiveBorder;\n\n    return exceedsTop || exceedsBottom || exceedsLeft || exceedsRight;\n  });\n}\n\nexport function updateTransitionEndListener(\n  box: HTMLDivElement,\n  action: 'add' | 'remove',\n  listener: (event: TransitionEvent) => void\n): void {\n  const method = `${action}EventListener` as\n    | 'addEventListener'\n    | 'removeEventListener';\n\n  // some browsers apparently support `transition` (unprefixed) but only fire\n  // `webkitTransitionEnd`...\n  ['transitionend', 'webkitTransitionEnd'].forEach((event) => {\n    box[method](event, listener as EventListener);\n  });\n}\n", "import {TOUCH_OPTIONS} from './constants';\nimport {isReferenceElement} from './dom-utils';\n\nexport const currentInput = {isTouch: false};\nlet lastMouseMoveTime = 0;\n\n/**\n * When a `touchstart` event is fired, it's assumed the user is using touch\n * input. We'll bind a `mousemove` event listener to listen for mouse input in\n * the future. This way, the `isTouch` property is fully dynamic and will handle\n * hybrid devices that use a mix of touch + mouse input.\n */\nexport function onDocumentTouchStart(): void {\n  if (currentInput.isTouch) {\n    return;\n  }\n\n  currentInput.isTouch = true;\n\n  if (window.performance) {\n    document.addEventListener('mousemove', onDocumentMouseMove);\n  }\n}\n\n/**\n * When two `mousemove` event are fired consecutively within 20ms, it's assumed\n * the user is using mouse input again. `mousemove` can fire on touch devices as\n * well, but very rarely that quickly.\n */\nexport function onDocumentMouseMove(): void {\n  const now = performance.now();\n\n  if (now - lastMouseMoveTime < 20) {\n    currentInput.isTouch = false;\n\n    document.removeEventListener('mousemove', onDocumentMouseMove);\n  }\n\n  lastMouseMoveTime = now;\n}\n\n/**\n * When an element is in focus and has a tippy, leaving the tab/window and\n * returning causes it to show again. For mouse users this is unexpected, but\n * for keyboard use it makes sense.\n * TODO: find a better technique to solve this problem\n */\nexport function onWindowBlur(): void {\n  const activeElement = document.activeElement as HTMLElement | null;\n\n  if (isReferenceElement(activeElement)) {\n    const instance = activeElement._tippy!;\n\n    if (activeElement.blur && !instance.state.isVisible) {\n      activeElement.blur();\n    }\n  }\n}\n\nexport default function bindGlobalEventListeners(): void {\n  document.addEventListener('touchstart', onDocumentTouchStart, TOUCH_OPTIONS);\n  window.addEventListener('blur', onWindowBlur);\n}\n", "export const isBrowser =\n  typeof window !== 'undefined' && typeof document !== 'undefined';\n\nconst ua = isBrowser ? navigator.userAgent : '';\n\nexport const isIE = /MSIE |Trident\\//.test(ua);\n", "import {Targets} from './types';\n\nexport function createMemoryLeakWarning(method: string): string {\n  const txt = method === 'destroy' ? 'n already-' : ' ';\n\n  return [\n    `${method}() was called on a${txt}destroyed instance. This is a no-op but`,\n    'indicates a potential memory leak.',\n  ].join(' ');\n}\n\nexport function clean(value: string): string {\n  const spacesAndTabs = /[ \\t]{2,}/g;\n  const lineStartWithSpaces = /^[ \\t]*/gm;\n\n  return value\n    .replace(spacesAndTabs, ' ')\n    .replace(lineStartWithSpaces, '')\n    .trim();\n}\n\nfunction getDevMessage(message: string): string {\n  return clean(`\n  %ctippy.js\n\n  %c${clean(message)}\n\n  %c👷‍ This is a development-only message. It will be removed in production.\n  `);\n}\n\nexport function getFormattedMessage(message: string): string[] {\n  return [\n    getDevMessage(message),\n    // title\n    'color: #00C584; font-size: 1.3em; font-weight: bold;',\n    // message\n    'line-height: 1.5',\n    // footer\n    'color: #a6a095;',\n  ];\n}\n\n// Assume warnings and errors never have the same message\nlet visitedMessages: Set<string>;\nif (__DEV__) {\n  resetVisitedMessages();\n}\n\nexport function resetVisitedMessages(): void {\n  visitedMessages = new Set();\n}\n\nexport function warnWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.warn(...getFormattedMessage(message));\n  }\n}\n\nexport function errorWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.error(...getFormattedMessage(message));\n  }\n}\n\nexport function validateTargets(targets: Targets): void {\n  const didPassFalsyValue = !targets;\n  const didPassPlainObject =\n    Object.prototype.toString.call(targets) === '[object Object]' &&\n    !(targets as any).addEventListener;\n\n  errorWhen(\n    didPassFalsyValue,\n    [\n      'tippy() was passed',\n      '`' + String(targets) + '`',\n      'as its targets (first) argument. Valid types are: String, Element,',\n      'Element[], or NodeList.',\n    ].join(' ')\n  );\n\n  errorWhen(\n    didPassPlainObject,\n    [\n      'tippy() was passed a plain object which is not supported as an argument',\n      'for virtual positioning. Use props.getReferenceClientRect instead.',\n    ].join(' ')\n  );\n}\n", "import {DefaultProps, Plugin, Props, ReferenceElement, Tippy} from './types';\nimport {\n  hasOwnProperty,\n  removeProperties,\n  invokeWithArgsOrReturn,\n} from './utils';\nimport {warnWhen} from './validation';\n\nconst pluginProps = {\n  animateFill: false,\n  followCursor: false,\n  inlinePositioning: false,\n  sticky: false,\n};\n\nconst renderProps = {\n  allowHTML: false,\n  animation: 'fade',\n  arrow: true,\n  content: '',\n  inertia: false,\n  maxWidth: 350,\n  role: 'tooltip',\n  theme: '',\n  zIndex: 9999,\n};\n\nexport const defaultProps: DefaultProps = {\n  appendTo: () => document.body,\n  aria: {\n    content: 'auto',\n    expanded: 'auto',\n  },\n  delay: 0,\n  duration: [300, 250],\n  getReferenceClientRect: null,\n  hideOnClick: true,\n  ignoreAttributes: false,\n  interactive: false,\n  interactiveBorder: 2,\n  interactiveDebounce: 0,\n  moveTransition: '',\n  offset: [0, 10],\n  onAfterUpdate() {},\n  onBeforeUpdate() {},\n  onCreate() {},\n  onDestroy() {},\n  onHidden() {},\n  onHide() {},\n  onMount() {},\n  onShow() {},\n  onShown() {},\n  onTrigger() {},\n  onUntrigger() {},\n  onClickOutside() {},\n  placement: 'top',\n  plugins: [],\n  popperOptions: {},\n  render: null,\n  showOnCreate: false,\n  touch: true,\n  trigger: 'mouseenter focus',\n  triggerTarget: null,\n  ...pluginProps,\n  ...renderProps,\n};\n\nconst defaultKeys = Object.keys(defaultProps);\n\nexport const setDefaultProps: Tippy['setDefaultProps'] = (partialProps) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateProps(partialProps, []);\n  }\n\n  const keys = Object.keys(partialProps) as Array<keyof DefaultProps>;\n  keys.forEach((key) => {\n    (defaultProps as any)[key] = partialProps[key];\n  });\n};\n\nexport function getExtendedPassedProps(\n  passedProps: Partial<Props> & Record<string, unknown>\n): Partial<Props> {\n  const plugins = passedProps.plugins || [];\n  const pluginProps = plugins.reduce<Record<string, unknown>>((acc, plugin) => {\n    const {name, defaultValue} = plugin;\n\n    if (name) {\n      acc[name] =\n        passedProps[name] !== undefined ? passedProps[name] : defaultValue;\n    }\n\n    return acc;\n  }, {});\n\n  return {\n    ...passedProps,\n    ...pluginProps,\n  };\n}\n\nexport function getDataAttributeProps(\n  reference: ReferenceElement,\n  plugins: Plugin[]\n): Record<string, unknown> {\n  const propKeys = plugins\n    ? Object.keys(getExtendedPassedProps({...defaultProps, plugins}))\n    : defaultKeys;\n\n  const props = propKeys.reduce(\n    (acc: Partial<Props> & Record<string, unknown>, key) => {\n      const valueAsString = (\n        reference.getAttribute(`data-tippy-${key}`) || ''\n      ).trim();\n\n      if (!valueAsString) {\n        return acc;\n      }\n\n      if (key === 'content') {\n        acc[key] = valueAsString;\n      } else {\n        try {\n          acc[key] = JSON.parse(valueAsString);\n        } catch (e) {\n          acc[key] = valueAsString;\n        }\n      }\n\n      return acc;\n    },\n    {}\n  );\n\n  return props;\n}\n\nexport function evaluateProps(\n  reference: ReferenceElement,\n  props: Props\n): Props {\n  const out = {\n    ...props,\n    content: invokeWithArgsOrReturn(props.content, [reference]),\n    ...(props.ignoreAttributes\n      ? {}\n      : getDataAttributeProps(reference, props.plugins)),\n  };\n\n  out.aria = {\n    ...defaultProps.aria,\n    ...out.aria,\n  };\n\n  out.aria = {\n    expanded:\n      out.aria.expanded === 'auto' ? props.interactive : out.aria.expanded,\n    content:\n      out.aria.content === 'auto'\n        ? props.interactive\n          ? null\n          : 'describedby'\n        : out.aria.content,\n  };\n\n  return out;\n}\n\nexport function validateProps(\n  partialProps: Partial<Props> = {},\n  plugins: Plugin[] = []\n): void {\n  const keys = Object.keys(partialProps) as Array<keyof Props>;\n  keys.forEach((prop) => {\n    const nonPluginProps = removeProperties(\n      defaultProps,\n      Object.keys(pluginProps)\n    );\n\n    let didPassUnknownProp = !hasOwnProperty(nonPluginProps, prop);\n\n    // Check if the prop exists in `plugins`\n    if (didPassUnknownProp) {\n      didPassUnknownProp =\n        plugins.filter((plugin) => plugin.name === prop).length === 0;\n    }\n\n    warnWhen(\n      didPassUnknownProp,\n      [\n        `\\`${prop}\\``,\n        \"is not a valid prop. You may have spelled it incorrectly, or if it's\",\n        'a plugin, forgot to pass it in an array as props.plugins.',\n        '\\n\\n',\n        'All props: https://atomiks.github.io/tippyjs/v6/all-props/\\n',\n        'Plugins: https://atomiks.github.io/tippyjs/v6/plugins/',\n      ].join(' ')\n    );\n  });\n}\n", "import {\n  ARROW_CLASS,\n  BACKDROP_CLASS,\n  BOX_CLASS,\n  CONTENT_CLASS,\n  SVG_ARROW_CLASS,\n} from './constants';\nimport {div, isElement} from './dom-utils';\nimport {Instance, PopperElement, Props} from './types';\nimport {PopperChildren} from './types-internal';\nimport {arrayFrom} from './utils';\n\n// Firefox extensions don't allow .innerHTML = \"...\" property. This tricks it.\nconst innerHTML = (): 'innerHTML' => 'innerHTML';\n\nfunction dangerouslySetInnerHTML(element: Element, html: string): void {\n  element[innerHTML()] = html;\n}\n\nfunction createArrowElement(value: Props['arrow']): HTMLDivElement {\n  const arrow = div();\n\n  if (value === true) {\n    arrow.className = ARROW_CLASS;\n  } else {\n    arrow.className = SVG_ARROW_CLASS;\n\n    if (isElement(value)) {\n      arrow.appendChild(value);\n    } else {\n      dangerouslySetInnerHTML(arrow, value as string);\n    }\n  }\n\n  return arrow;\n}\n\nexport function setContent(content: HTMLDivElement, props: Props): void {\n  if (isElement(props.content)) {\n    dangerouslySetInnerHTML(content, '');\n    content.appendChild(props.content);\n  } else if (typeof props.content !== 'function') {\n    if (props.allowHTML) {\n      dangerouslySetInnerHTML(content, props.content);\n    } else {\n      content.textContent = props.content;\n    }\n  }\n}\n\nexport function getChildren(popper: PopperElement): PopperChildren {\n  const box = popper.firstElementChild as HTMLDivElement;\n  const boxChildren = arrayFrom(box.children);\n\n  return {\n    box,\n    content: boxChildren.find((node) => node.classList.contains(CONTENT_CLASS)),\n    arrow: boxChildren.find(\n      (node) =>\n        node.classList.contains(ARROW_CLASS) ||\n        node.classList.contains(SVG_ARROW_CLASS)\n    ),\n    backdrop: boxChildren.find((node) =>\n      node.classList.contains(BACKDROP_CLASS)\n    ),\n  };\n}\n\nexport function render(\n  instance: Instance\n): {\n  popper: PopperElement;\n  onUpdate?: (prevProps: Props, nextProps: Props) => void;\n} {\n  const popper = div();\n\n  const box = div();\n  box.className = BOX_CLASS;\n  box.setAttribute('data-state', 'hidden');\n  box.setAttribute('tabindex', '-1');\n\n  const content = div();\n  content.className = CONTENT_CLASS;\n  content.setAttribute('data-state', 'hidden');\n\n  setContent(content, instance.props);\n\n  popper.appendChild(box);\n  box.appendChild(content);\n\n  onUpdate(instance.props, instance.props);\n\n  function onUpdate(prevProps: Props, nextProps: Props): void {\n    const {box, content, arrow} = getChildren(popper);\n\n    if (nextProps.theme) {\n      box.setAttribute('data-theme', nextProps.theme);\n    } else {\n      box.removeAttribute('data-theme');\n    }\n\n    if (typeof nextProps.animation === 'string') {\n      box.setAttribute('data-animation', nextProps.animation);\n    } else {\n      box.removeAttribute('data-animation');\n    }\n\n    if (nextProps.inertia) {\n      box.setAttribute('data-inertia', '');\n    } else {\n      box.removeAttribute('data-inertia');\n    }\n\n    box.style.maxWidth =\n      typeof nextProps.maxWidth === 'number'\n        ? `${nextProps.maxWidth}px`\n        : nextProps.maxWidth;\n\n    if (nextProps.role) {\n      box.setAttribute('role', nextProps.role);\n    } else {\n      box.removeAttribute('role');\n    }\n\n    if (\n      prevProps.content !== nextProps.content ||\n      prevProps.allowHTML !== nextProps.allowHTML\n    ) {\n      setContent(content, instance.props);\n    }\n\n    if (nextProps.arrow) {\n      if (!arrow) {\n        box.appendChild(createArrowElement(nextProps.arrow));\n      } else if (prevProps.arrow !== nextProps.arrow) {\n        box.removeChild(arrow);\n        box.appendChild(createArrowElement(nextProps.arrow));\n      }\n    } else if (arrow) {\n      box.removeChild(arrow!);\n    }\n  }\n\n  return {\n    popper,\n    onUpdate,\n  };\n}\n\n// Runtime check to identify if the render function is the default one; this\n// way we can apply default CSS transitions logic and it can be tree-shaken away\nrender.$$tippy = true;\n", "import {createPopper, StrictModifiers, Modifier} from '@popperjs/core';\nimport {currentInput} from './bindGlobalEventListeners';\nimport {isIE} from './browser';\nimport {TOUCH_OPTIONS} from './constants';\nimport {\n  div,\n  getOwnerDocument,\n  isCursorOutsideInteractiveBorder,\n  isMouseEvent,\n  setTransitionDuration,\n  setVisibilityState,\n  updateTransitionEndListener,\n} from './dom-utils';\nimport {defaultProps, evaluateProps, getExtendedPassedProps} from './props';\nimport {getChildren} from './template';\nimport {\n  Content,\n  Instance,\n  LifecycleHooks,\n  PopperElement,\n  Props,\n  ReferenceElement,\n} from './types';\nimport {ListenerObject, PopperTreeData, PopperChildren} from './types-internal';\nimport {\n  arrayFrom,\n  debounce,\n  getValueAtIndexOrReturn,\n  invokeWithArgsOrReturn,\n  normalizeToArray,\n  pushIfUnique,\n  splitBySpaces,\n  unique,\n  removeUndefinedProps,\n} from './utils';\nimport {createMemoryLeakWarning, errorWhen, warnWhen} from './validation';\n\nlet idCounter = 1;\nlet mouseMoveListeners: ((event: MouseEvent) => void)[] = [];\n\n// Used by `hideAll()`\nexport let mountedInstances: Instance[] = [];\n\nexport default function createTippy(\n  reference: ReferenceElement,\n  passedProps: Partial<Props>\n): Instance {\n  const props = evaluateProps(reference, {\n    ...defaultProps,\n    ...getExtendedPassedProps(removeUndefinedProps(passedProps)),\n  });\n\n  // ===========================================================================\n  // 🔒 Private members\n  // ===========================================================================\n  let showTimeout: any;\n  let hideTimeout: any;\n  let scheduleHideAnimationFrame: number;\n  let isVisibleFromClick = false;\n  let didHideDueToDocumentMouseDown = false;\n  let didTouchMove = false;\n  let ignoreOnFirstUpdate = false;\n  let lastTriggerEvent: Event | undefined;\n  let currentTransitionEndListener: (event: TransitionEvent) => void;\n  let onFirstUpdate: () => void;\n  let listeners: ListenerObject[] = [];\n  let debouncedOnMouseMove = debounce(onMouseMove, props.interactiveDebounce);\n  let currentTarget: Element;\n  const doc = getOwnerDocument(props.triggerTarget || reference);\n\n  // ===========================================================================\n  // 🔑 Public members\n  // ===========================================================================\n  const id = idCounter++;\n  const popperInstance = null;\n  const plugins = unique(props.plugins);\n\n  const state = {\n    // Is the instance currently enabled?\n    isEnabled: true,\n    // Is the tippy currently showing and not transitioning out?\n    isVisible: false,\n    // Has the instance been destroyed?\n    isDestroyed: false,\n    // Is the tippy currently mounted to the DOM?\n    isMounted: false,\n    // Has the tippy finished transitioning in?\n    isShown: false,\n  };\n\n  const instance: Instance = {\n    // properties\n    id,\n    reference,\n    popper: div(),\n    popperInstance,\n    props,\n    state,\n    plugins,\n    // methods\n    clearDelayTimeouts,\n    setProps,\n    setContent,\n    show,\n    hide,\n    hideWithInteractivity,\n    enable,\n    disable,\n    unmount,\n    destroy,\n  };\n\n  // TODO: Investigate why this early return causes a TDZ error in the tests —\n  // it doesn't seem to happen in the browser\n  /* istanbul ignore if */\n  if (!props.render) {\n    if (__DEV__) {\n      errorWhen(true, 'render() function has not been supplied.');\n    }\n\n    return instance;\n  }\n\n  // ===========================================================================\n  // Initial mutations\n  // ===========================================================================\n  const {popper, onUpdate} = props.render(instance);\n\n  popper.setAttribute('data-__NAMESPACE_PREFIX__-root', '');\n  popper.id = `__NAMESPACE_PREFIX__-${instance.id}`;\n\n  instance.popper = popper;\n  reference._tippy = instance;\n  popper._tippy = instance;\n\n  const pluginsHooks = plugins.map((plugin) => plugin.fn(instance));\n  const hasAriaExpanded = reference.hasAttribute('aria-expanded');\n\n  addListeners();\n  handleAriaExpandedAttribute();\n  handleStyles();\n\n  invokeHook('onCreate', [instance]);\n\n  if (props.showOnCreate) {\n    scheduleShow();\n  }\n\n  // Prevent a tippy with a delay from hiding if the cursor left then returned\n  // before it started hiding\n  popper.addEventListener('mouseenter', () => {\n    if (instance.props.interactive && instance.state.isVisible) {\n      instance.clearDelayTimeouts();\n    }\n  });\n\n  popper.addEventListener('mouseleave', (event) => {\n    if (\n      instance.props.interactive &&\n      instance.props.trigger.indexOf('mouseenter') >= 0\n    ) {\n      doc.addEventListener('mousemove', debouncedOnMouseMove);\n      debouncedOnMouseMove(event);\n    }\n  });\n\n  return instance;\n\n  // ===========================================================================\n  // 🔒 Private methods\n  // ===========================================================================\n  function getNormalizedTouchSettings(): [string | boolean, number] {\n    const {touch} = instance.props;\n    return Array.isArray(touch) ? touch : [touch, 0];\n  }\n\n  function getIsCustomTouchBehavior(): boolean {\n    return getNormalizedTouchSettings()[0] === 'hold';\n  }\n\n  function getIsDefaultRenderFn(): boolean {\n    // @ts-ignore\n    return !!instance.props.render?.$$tippy;\n  }\n\n  function getCurrentTarget(): Element {\n    return currentTarget || reference;\n  }\n\n  function getDefaultTemplateChildren(): PopperChildren {\n    return getChildren(popper);\n  }\n\n  function getDelay(isShow: boolean): number {\n    // For touch or keyboard input, force `0` delay for UX reasons\n    // Also if the instance is mounted but not visible (transitioning out),\n    // ignore delay\n    if (\n      (instance.state.isMounted && !instance.state.isVisible) ||\n      currentInput.isTouch ||\n      (lastTriggerEvent && lastTriggerEvent.type === 'focus')\n    ) {\n      return 0;\n    }\n\n    return getValueAtIndexOrReturn(\n      instance.props.delay,\n      isShow ? 0 : 1,\n      defaultProps.delay\n    );\n  }\n\n  function handleStyles(): void {\n    popper.style.pointerEvents =\n      instance.props.interactive && instance.state.isVisible ? '' : 'none';\n    popper.style.zIndex = `${instance.props.zIndex}`;\n  }\n\n  function invokeHook(\n    hook: keyof LifecycleHooks,\n    args: [Instance, any?],\n    shouldInvokePropsHook = true\n  ): void {\n    pluginsHooks.forEach((pluginHooks) => {\n      if (pluginHooks[hook]) {\n        pluginHooks[hook]!(...args);\n      }\n    });\n\n    if (shouldInvokePropsHook) {\n      instance.props[hook](...args);\n    }\n  }\n\n  function handleAriaContentAttribute(): void {\n    const {aria} = instance.props;\n\n    if (!aria.content) {\n      return;\n    }\n\n    const attr = `aria-${aria.content}`;\n    const id = popper.id;\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      const currentValue = node.getAttribute(attr);\n\n      if (instance.state.isVisible) {\n        node.setAttribute(attr, currentValue ? `${currentValue} ${id}` : id);\n      } else {\n        const nextValue = currentValue && currentValue.replace(id, '').trim();\n\n        if (nextValue) {\n          node.setAttribute(attr, nextValue);\n        } else {\n          node.removeAttribute(attr);\n        }\n      }\n    });\n  }\n\n  function handleAriaExpandedAttribute(): void {\n    if (hasAriaExpanded || !instance.props.aria.expanded) {\n      return;\n    }\n\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      if (instance.props.interactive) {\n        node.setAttribute(\n          'aria-expanded',\n          instance.state.isVisible && node === getCurrentTarget()\n            ? 'true'\n            : 'false'\n        );\n      } else {\n        node.removeAttribute('aria-expanded');\n      }\n    });\n  }\n\n  function cleanupInteractiveMouseListeners(): void {\n    doc.removeEventListener('mousemove', debouncedOnMouseMove);\n    mouseMoveListeners = mouseMoveListeners.filter(\n      (listener) => listener !== debouncedOnMouseMove\n    );\n  }\n\n  function onDocumentPress(event: MouseEvent | TouchEvent): void {\n    // Moved finger to scroll instead of an intentional tap outside\n    if (currentInput.isTouch) {\n      if (didTouchMove || event.type === 'mousedown') {\n        return;\n      }\n    }\n\n    // Clicked on interactive popper\n    if (\n      instance.props.interactive &&\n      popper.contains(event.target as Element)\n    ) {\n      return;\n    }\n\n    // Clicked on the event listeners target\n    if (getCurrentTarget().contains(event.target as Element)) {\n      if (currentInput.isTouch) {\n        return;\n      }\n\n      if (\n        instance.state.isVisible &&\n        instance.props.trigger.indexOf('click') >= 0\n      ) {\n        return;\n      }\n    } else {\n      invokeHook('onClickOutside', [instance, event]);\n    }\n\n    if (instance.props.hideOnClick === true) {\n      isVisibleFromClick = false;\n      instance.clearDelayTimeouts();\n      instance.hide();\n\n      // `mousedown` event is fired right before `focus` if pressing the\n      // currentTarget. This lets a tippy with `focus` trigger know that it\n      // should not show\n      didHideDueToDocumentMouseDown = true;\n      setTimeout(() => {\n        didHideDueToDocumentMouseDown = false;\n      });\n\n      // The listener gets added in `scheduleShow()`, but this may be hiding it\n      // before it shows, and hide()'s early bail-out behavior can prevent it\n      // from being cleaned up\n      if (!instance.state.isMounted) {\n        removeDocumentPress();\n      }\n    }\n  }\n\n  function onTouchMove(): void {\n    didTouchMove = true;\n  }\n\n  function onTouchStart(): void {\n    didTouchMove = false;\n  }\n\n  function addDocumentPress(): void {\n    doc.addEventListener('mousedown', onDocumentPress, true);\n    doc.addEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.addEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.addEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function removeDocumentPress(): void {\n    doc.removeEventListener('mousedown', onDocumentPress, true);\n    doc.removeEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.removeEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.removeEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function onTransitionedOut(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, () => {\n      if (\n        !instance.state.isVisible &&\n        popper.parentNode &&\n        popper.parentNode.contains(popper)\n      ) {\n        callback();\n      }\n    });\n  }\n\n  function onTransitionedIn(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, callback);\n  }\n\n  function onTransitionEnd(duration: number, callback: () => void): void {\n    const box = getDefaultTemplateChildren().box;\n\n    function listener(event: TransitionEvent): void {\n      if (event.target === box) {\n        updateTransitionEndListener(box, 'remove', listener);\n        callback();\n      }\n    }\n\n    // Make callback synchronous if duration is 0\n    // `transitionend` won't fire otherwise\n    if (duration === 0) {\n      return callback();\n    }\n\n    updateTransitionEndListener(box, 'remove', currentTransitionEndListener);\n    updateTransitionEndListener(box, 'add', listener);\n\n    currentTransitionEndListener = listener;\n  }\n\n  function on(\n    eventType: string,\n    handler: EventListener,\n    options: boolean | object = false\n  ): void {\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach((node) => {\n      node.addEventListener(eventType, handler, options);\n      listeners.push({node, eventType, handler, options});\n    });\n  }\n\n  function addListeners(): void {\n    if (getIsCustomTouchBehavior()) {\n      on('touchstart', onTrigger, {passive: true});\n      on('touchend', onMouseLeave as EventListener, {passive: true});\n    }\n\n    splitBySpaces(instance.props.trigger).forEach((eventType) => {\n      if (eventType === 'manual') {\n        return;\n      }\n\n      on(eventType, onTrigger);\n\n      switch (eventType) {\n        case 'mouseenter':\n          on('mouseleave', onMouseLeave as EventListener);\n          break;\n        case 'focus':\n          on(isIE ? 'focusout' : 'blur', onBlurOrFocusOut as EventListener);\n          break;\n        case 'focusin':\n          on('focusout', onBlurOrFocusOut as EventListener);\n          break;\n      }\n    });\n  }\n\n  function removeListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function onTrigger(event: Event): void {\n    let shouldScheduleClickHide = false;\n\n    if (\n      !instance.state.isEnabled ||\n      isEventListenerStopped(event) ||\n      didHideDueToDocumentMouseDown\n    ) {\n      return;\n    }\n\n    const wasFocused = lastTriggerEvent?.type === 'focus';\n\n    lastTriggerEvent = event;\n    currentTarget = event.currentTarget as Element;\n\n    handleAriaExpandedAttribute();\n\n    if (!instance.state.isVisible && isMouseEvent(event)) {\n      // If scrolling, `mouseenter` events can be fired if the cursor lands\n      // over a new target, but `mousemove` events don't get fired. This\n      // causes interactive tooltips to get stuck open until the cursor is\n      // moved\n      mouseMoveListeners.forEach((listener) => listener(event));\n    }\n\n    // Toggle show/hide when clicking click-triggered tooltips\n    if (\n      event.type === 'click' &&\n      (instance.props.trigger.indexOf('mouseenter') < 0 ||\n        isVisibleFromClick) &&\n      instance.props.hideOnClick !== false &&\n      instance.state.isVisible\n    ) {\n      shouldScheduleClickHide = true;\n    } else {\n      scheduleShow(event);\n    }\n\n    if (event.type === 'click') {\n      isVisibleFromClick = !shouldScheduleClickHide;\n    }\n\n    if (shouldScheduleClickHide && !wasFocused) {\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseMove(event: MouseEvent): void {\n    const target = event.target as Node;\n    const isCursorOverReferenceOrPopper =\n      reference.contains(target) || popper.contains(target);\n\n    if (event.type === 'mousemove' && isCursorOverReferenceOrPopper) {\n      return;\n    }\n\n    const popperTreeData = getNestedPopperTree()\n      .concat(popper)\n      .map((popper) => {\n        const instance = popper._tippy!;\n        const state = instance.popperInstance?.state;\n\n        if (state) {\n          return {\n            popperRect: popper.getBoundingClientRect(),\n            popperState: state,\n            props,\n          };\n        }\n\n        return null;\n      })\n      .filter(Boolean) as PopperTreeData[];\n\n    if (isCursorOutsideInteractiveBorder(popperTreeData, event)) {\n      cleanupInteractiveMouseListeners();\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseLeave(event: MouseEvent): void {\n    const shouldBail =\n      isEventListenerStopped(event) ||\n      (instance.props.trigger.indexOf('click') >= 0 && isVisibleFromClick);\n\n    if (shouldBail) {\n      return;\n    }\n\n    if (instance.props.interactive) {\n      instance.hideWithInteractivity(event);\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function onBlurOrFocusOut(event: FocusEvent): void {\n    if (\n      instance.props.trigger.indexOf('focusin') < 0 &&\n      event.target !== getCurrentTarget()\n    ) {\n      return;\n    }\n\n    // If focus was moved to within the popper\n    if (\n      instance.props.interactive &&\n      event.relatedTarget &&\n      popper.contains(event.relatedTarget as Element)\n    ) {\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function isEventListenerStopped(event: Event): boolean {\n    return currentInput.isTouch\n      ? getIsCustomTouchBehavior() !== event.type.indexOf('touch') >= 0\n      : false;\n  }\n\n  function createPopperInstance(): void {\n    destroyPopperInstance();\n\n    const {\n      popperOptions,\n      placement,\n      offset,\n      getReferenceClientRect,\n      moveTransition,\n    } = instance.props;\n\n    const arrow = getIsDefaultRenderFn() ? getChildren(popper).arrow : null;\n\n    const computedReference = getReferenceClientRect\n      ? {\n          getBoundingClientRect: getReferenceClientRect,\n          contextElement:\n            getReferenceClientRect.contextElement || getCurrentTarget(),\n        }\n      : reference;\n\n    const tippyModifier: Modifier<'$$tippy', {}> = {\n      name: '$$tippy',\n      enabled: true,\n      phase: 'beforeWrite',\n      requires: ['computeStyles'],\n      fn({state}) {\n        if (getIsDefaultRenderFn()) {\n          const {box} = getDefaultTemplateChildren();\n\n          ['placement', 'reference-hidden', 'escaped'].forEach((attr) => {\n            if (attr === 'placement') {\n              box.setAttribute('data-placement', state.placement);\n            } else {\n              if (state.attributes.popper[`data-popper-${attr}`]) {\n                box.setAttribute(`data-${attr}`, '');\n              } else {\n                box.removeAttribute(`data-${attr}`);\n              }\n            }\n          });\n\n          state.attributes.popper = {};\n        }\n      },\n    };\n\n    type TippyModifier = Modifier<'$$tippy', {}>;\n    type ExtendedModifiers = StrictModifiers | Partial<TippyModifier>;\n\n    const modifiers: Array<ExtendedModifiers> = [\n      {\n        name: 'offset',\n        options: {\n          offset,\n        },\n      },\n      {\n        name: 'preventOverflow',\n        options: {\n          padding: {\n            top: 2,\n            bottom: 2,\n            left: 5,\n            right: 5,\n          },\n        },\n      },\n      {\n        name: 'flip',\n        options: {\n          padding: 5,\n        },\n      },\n      {\n        name: 'computeStyles',\n        options: {\n          adaptive: !moveTransition,\n        },\n      },\n      tippyModifier,\n    ];\n\n    if (getIsDefaultRenderFn() && arrow) {\n      modifiers.push({\n        name: 'arrow',\n        options: {\n          element: arrow,\n          padding: 3,\n        },\n      });\n    }\n\n    modifiers.push(...(popperOptions?.modifiers || []));\n\n    instance.popperInstance = createPopper<ExtendedModifiers>(\n      computedReference,\n      popper,\n      {\n        ...popperOptions,\n        placement,\n        onFirstUpdate,\n        modifiers,\n      }\n    );\n  }\n\n  function destroyPopperInstance(): void {\n    if (instance.popperInstance) {\n      instance.popperInstance.destroy();\n      instance.popperInstance = null;\n    }\n  }\n\n  function mount(): void {\n    const {appendTo} = instance.props;\n\n    let parentNode: any;\n\n    // By default, we'll append the popper to the triggerTargets's parentNode so\n    // it's directly after the reference element so the elements inside the\n    // tippy can be tabbed to\n    // If there are clipping issues, the user can specify a different appendTo\n    // and ensure focus management is handled correctly manually\n    const node = getCurrentTarget();\n\n    if (\n      (instance.props.interactive && appendTo === defaultProps.appendTo) ||\n      appendTo === 'parent'\n    ) {\n      parentNode = node.parentNode;\n    } else {\n      parentNode = invokeWithArgsOrReturn(appendTo, [node]);\n    }\n\n    // The popper element needs to exist on the DOM before its position can be\n    // updated as Popper needs to read its dimensions\n    if (!parentNode.contains(popper)) {\n      parentNode.appendChild(popper);\n    }\n\n    createPopperInstance();\n\n    /* istanbul ignore else */\n    if (__DEV__) {\n      // Accessibility check\n      warnWhen(\n        instance.props.interactive &&\n          appendTo === defaultProps.appendTo &&\n          node.nextElementSibling !== popper,\n        [\n          'Interactive tippy element may not be accessible via keyboard',\n          'navigation because it is not directly after the reference element',\n          'in the DOM source order.',\n          '\\n\\n',\n          'Using a wrapper <div> or <span> tag around the reference element',\n          'solves this by creating a new parentNode context.',\n          '\\n\\n',\n          'Specifying `appendTo: document.body` silences this warning, but it',\n          'assumes you are using a focus management solution to handle',\n          'keyboard navigation.',\n          '\\n\\n',\n          'See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity',\n        ].join(' ')\n      );\n    }\n  }\n\n  function getNestedPopperTree(): PopperElement[] {\n    return arrayFrom(\n      popper.querySelectorAll('[data-__NAMESPACE_PREFIX__-root]')\n    );\n  }\n\n  function scheduleShow(event?: Event): void {\n    instance.clearDelayTimeouts();\n\n    if (event) {\n      invokeHook('onTrigger', [instance, event]);\n    }\n\n    addDocumentPress();\n\n    let delay = getDelay(true);\n    const [touchValue, touchDelay] = getNormalizedTouchSettings();\n\n    if (currentInput.isTouch && touchValue === 'hold' && touchDelay) {\n      delay = touchDelay;\n    }\n\n    if (delay) {\n      showTimeout = setTimeout(() => {\n        instance.show();\n      }, delay);\n    } else {\n      instance.show();\n    }\n  }\n\n  function scheduleHide(event: Event): void {\n    instance.clearDelayTimeouts();\n\n    invokeHook('onUntrigger', [instance, event]);\n\n    if (!instance.state.isVisible) {\n      removeDocumentPress();\n\n      return;\n    }\n\n    // For interactive tippies, scheduleHide is added to a document.body handler\n    // from onMouseLeave so must intercept scheduled hides from mousemove/leave\n    // events when trigger contains mouseenter and click, and the tip is\n    // currently shown as a result of a click.\n    if (\n      instance.props.trigger.indexOf('mouseenter') >= 0 &&\n      instance.props.trigger.indexOf('click') >= 0 &&\n      ['mouseleave', 'mousemove'].indexOf(event.type) >= 0 &&\n      isVisibleFromClick\n    ) {\n      return;\n    }\n\n    const delay = getDelay(false);\n\n    if (delay) {\n      hideTimeout = setTimeout(() => {\n        if (instance.state.isVisible) {\n          instance.hide();\n        }\n      }, delay);\n    } else {\n      // Fixes a `transitionend` problem when it fires 1 frame too\n      // late sometimes, we don't want hide() to be called.\n      scheduleHideAnimationFrame = requestAnimationFrame(() => {\n        instance.hide();\n      });\n    }\n  }\n\n  // ===========================================================================\n  // 🔑 Public methods\n  // ===========================================================================\n  function enable(): void {\n    instance.state.isEnabled = true;\n  }\n\n  function disable(): void {\n    // Disabling the instance should also hide it\n    // https://github.com/atomiks/tippy.js-react/issues/106\n    instance.hide();\n    instance.state.isEnabled = false;\n  }\n\n  function clearDelayTimeouts(): void {\n    clearTimeout(showTimeout);\n    clearTimeout(hideTimeout);\n    cancelAnimationFrame(scheduleHideAnimationFrame);\n  }\n\n  function setProps(partialProps: Partial<Props>): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('setProps'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    invokeHook('onBeforeUpdate', [instance, partialProps]);\n\n    removeListeners();\n\n    const prevProps = instance.props;\n    const nextProps = evaluateProps(reference, {\n      ...instance.props,\n      ...partialProps,\n      ignoreAttributes: true,\n    });\n\n    instance.props = nextProps;\n\n    addListeners();\n\n    if (prevProps.interactiveDebounce !== nextProps.interactiveDebounce) {\n      cleanupInteractiveMouseListeners();\n      debouncedOnMouseMove = debounce(\n        onMouseMove,\n        nextProps.interactiveDebounce\n      );\n    }\n\n    // Ensure stale aria-expanded attributes are removed\n    if (prevProps.triggerTarget && !nextProps.triggerTarget) {\n      normalizeToArray(prevProps.triggerTarget).forEach((node) => {\n        node.removeAttribute('aria-expanded');\n      });\n    } else if (nextProps.triggerTarget) {\n      reference.removeAttribute('aria-expanded');\n    }\n\n    handleAriaExpandedAttribute();\n    handleStyles();\n\n    if (onUpdate) {\n      onUpdate(prevProps, nextProps);\n    }\n\n    if (instance.popperInstance) {\n      createPopperInstance();\n\n      // Fixes an issue with nested tippies if they are all getting re-rendered,\n      // and the nested ones get re-rendered first.\n      // https://github.com/atomiks/tippyjs-react/issues/177\n      // TODO: find a cleaner / more efficient solution(!)\n      getNestedPopperTree().forEach((nestedPopper) => {\n        // React (and other UI libs likely) requires a rAF wrapper as it flushes\n        // its work in one\n        requestAnimationFrame(nestedPopper._tippy!.popperInstance!.forceUpdate);\n      });\n    }\n\n    invokeHook('onAfterUpdate', [instance, partialProps]);\n  }\n\n  function setContent(content: Content): void {\n    instance.setProps({content});\n  }\n\n  function show(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('show'));\n    }\n\n    // Early bail-out\n    const isAlreadyVisible = instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const isTouchAndTouchDisabled =\n      currentInput.isTouch && !instance.props.touch;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      0,\n      defaultProps.duration\n    );\n\n    if (\n      isAlreadyVisible ||\n      isDestroyed ||\n      isDisabled ||\n      isTouchAndTouchDisabled\n    ) {\n      return;\n    }\n\n    // Normalize `disabled` behavior across browsers.\n    // Firefox allows events on disabled elements, but Chrome doesn't.\n    // Using a wrapper element (i.e. <span>) is recommended.\n    if (getCurrentTarget().hasAttribute('disabled')) {\n      return;\n    }\n\n    invokeHook('onShow', [instance], false);\n    if (instance.props.onShow(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = true;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'visible';\n    }\n\n    handleStyles();\n    addDocumentPress();\n\n    if (!instance.state.isMounted) {\n      popper.style.transition = 'none';\n    }\n\n    // If flipping to the opposite side after hiding at least once, the\n    // animation will use the wrong placement without resetting the duration\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n      setTransitionDuration([box, content], 0);\n    }\n\n    onFirstUpdate = (): void => {\n      if (!instance.state.isVisible || ignoreOnFirstUpdate) {\n        return;\n      }\n\n      ignoreOnFirstUpdate = true;\n\n      // reflow\n      void popper.offsetHeight;\n\n      popper.style.transition = instance.props.moveTransition;\n\n      if (getIsDefaultRenderFn() && instance.props.animation) {\n        const {box, content} = getDefaultTemplateChildren();\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'visible');\n      }\n\n      handleAriaContentAttribute();\n      handleAriaExpandedAttribute();\n\n      pushIfUnique(mountedInstances, instance);\n\n      instance.state.isMounted = true;\n      invokeHook('onMount', [instance]);\n\n      if (instance.props.animation && getIsDefaultRenderFn()) {\n        onTransitionedIn(duration, () => {\n          instance.state.isShown = true;\n          invokeHook('onShown', [instance]);\n        });\n      }\n    };\n\n    mount();\n  }\n\n  function hide(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hide'));\n    }\n\n    // Early bail-out\n    const isAlreadyHidden = !instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      1,\n      defaultProps.duration\n    );\n\n    if (isAlreadyHidden || isDestroyed || isDisabled) {\n      return;\n    }\n\n    invokeHook('onHide', [instance], false);\n    if (instance.props.onHide(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = false;\n    instance.state.isShown = false;\n    ignoreOnFirstUpdate = false;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'hidden';\n    }\n\n    cleanupInteractiveMouseListeners();\n    removeDocumentPress();\n    handleStyles();\n\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n\n      if (instance.props.animation) {\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'hidden');\n      }\n    }\n\n    handleAriaContentAttribute();\n    handleAriaExpandedAttribute();\n\n    if (instance.props.animation) {\n      if (getIsDefaultRenderFn()) {\n        onTransitionedOut(duration, instance.unmount);\n      }\n    } else {\n      instance.unmount();\n    }\n  }\n\n  function hideWithInteractivity(event: MouseEvent): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(\n        instance.state.isDestroyed,\n        createMemoryLeakWarning('hideWithInteractivity')\n      );\n    }\n\n    doc.addEventListener('mousemove', debouncedOnMouseMove);\n    pushIfUnique(mouseMoveListeners, debouncedOnMouseMove);\n    debouncedOnMouseMove(event);\n  }\n\n  function unmount(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('unmount'));\n    }\n\n    if (instance.state.isVisible) {\n      instance.hide();\n    }\n\n    if (!instance.state.isMounted) {\n      return;\n    }\n\n    destroyPopperInstance();\n\n    // If a popper is not interactive, it will be appended outside the popper\n    // tree by default. This seems mainly for interactive tippies, but we should\n    // find a workaround if possible\n    getNestedPopperTree().forEach((nestedPopper) => {\n      nestedPopper._tippy!.unmount();\n    });\n\n    if (popper.parentNode) {\n      popper.parentNode.removeChild(popper);\n    }\n\n    mountedInstances = mountedInstances.filter((i) => i !== instance);\n\n    instance.state.isMounted = false;\n    invokeHook('onHidden', [instance]);\n  }\n\n  function destroy(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('destroy'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    instance.clearDelayTimeouts();\n    instance.unmount();\n\n    removeListeners();\n\n    delete reference._tippy;\n\n    instance.state.isDestroyed = true;\n\n    invokeHook('onDestroy', [instance]);\n  }\n}\n", "import bindGlobalEventListeners, {\n  currentInput,\n} from './bindGlobalEventListeners';\nimport createTippy, {mountedInstances} from './createTippy';\nimport {getArrayOfElements, isElement, isReferenceElement} from './dom-utils';\nimport {defaultProps, setDefaultProps, validateProps} from './props';\nimport {HideAll, HideAllOptions, Instance, Props, Targets} from './types';\nimport {validateTargets, warnWhen} from './validation';\n\nfunction tippy(\n  targets: Targets,\n  optionalProps: Partial<Props> = {}\n): Instance | Instance[] {\n  const plugins = defaultProps.plugins.concat(optionalProps.plugins || []);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateTargets(targets);\n    validateProps(optionalProps, plugins);\n  }\n\n  bindGlobalEventListeners();\n\n  const passedProps: Partial<Props> = {...optionalProps, plugins};\n\n  const elements = getArrayOfElements(targets);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    const isSingleContentElement = isElement(passedProps.content);\n    const isMoreThanOneReferenceElement = elements.length > 1;\n    warnWhen(\n      isSingleContentElement && isMoreThanOneReferenceElement,\n      [\n        'tippy() was passed an Element as the `content` prop, but more than',\n        'one tippy instance was created by this invocation. This means the',\n        'content element will only be appended to the last tippy instance.',\n        '\\n\\n',\n        'Instead, pass the .innerHTML of the element, or use a function that',\n        'returns a cloned version of the element instead.',\n        '\\n\\n',\n        '1) content: element.innerHTML\\n',\n        '2) content: () => element.cloneNode(true)',\n      ].join(' ')\n    );\n  }\n\n  const instances = elements.reduce<Instance[]>(\n    (acc, reference): Instance[] => {\n      const instance = reference && createTippy(reference, passedProps);\n\n      if (instance) {\n        acc.push(instance);\n      }\n\n      return acc;\n    },\n    []\n  );\n\n  return isElement(targets) ? instances[0] : instances;\n}\n\ntippy.defaultProps = defaultProps;\ntippy.setDefaultProps = setDefaultProps;\ntippy.currentInput = currentInput;\n\nexport default tippy;\n\nexport const hideAll: HideAll = ({\n  exclude: excludedReferenceOrInstance,\n  duration,\n}: HideAllOptions = {}) => {\n  mountedInstances.forEach((instance) => {\n    let isExcluded = false;\n\n    if (excludedReferenceOrInstance) {\n      isExcluded = isReferenceElement(excludedReferenceOrInstance)\n        ? instance.reference === excludedReferenceOrInstance\n        : instance.popper === (excludedReferenceOrInstance as Instance).popper;\n    }\n\n    if (!isExcluded) {\n      const originalDuration = instance.props.duration;\n\n      instance.setProps({duration});\n      instance.hide();\n\n      if (!instance.state.isDestroyed) {\n        instance.setProps({duration: originalDuration});\n      }\n    }\n  });\n};\n", "import tippy from '..';\nimport {div} from '../dom-utils';\nimport {\n  C<PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  CreateSingletonProps,\n  ReferenceElement,\n  CreateSingletonInstance,\n} from '../types';\nimport {removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\n\nconst createSingleton: CreateSingleton = (\n  tippyInstances,\n  optionalProps = {}\n) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !Array.isArray(tippyInstances),\n      [\n        'The first argument passed to createSingleton() must be an array of',\n        'tippy instances. The passed value was',\n        String(tippyInstances),\n      ].join(' ')\n    );\n  }\n\n  let mutTippyInstances = tippyInstances;\n  let references: Array<ReferenceElement> = [];\n  let currentTarget: Element;\n  let overrides = optionalProps.overrides;\n\n  function setReferences(): void {\n    references = mutTippyInstances.map((instance) => instance.reference);\n  }\n\n  function enableInstances(isEnabled: boolean): void {\n    mutTippyInstances.forEach((instance) => {\n      if (isEnabled) {\n        instance.enable();\n      } else {\n        instance.disable();\n      }\n    });\n  }\n\n  enableInstances(false);\n  setReferences();\n\n  const singleton: Plugin = {\n    fn() {\n      return {\n        onDestroy(): void {\n          enableInstances(true);\n        },\n        onTrigger(instance, event): void {\n          const target = event.currentTarget as Element;\n          const index = references.indexOf(target);\n\n          // bail-out\n          if (target === currentTarget) {\n            return;\n          }\n\n          currentTarget = target;\n\n          const overrideProps = (overrides || [])\n            .concat('content')\n            .reduce((acc, prop) => {\n              (acc as any)[prop] = mutTippyInstances[index].props[prop];\n              return acc;\n            }, {});\n\n          instance.setProps({\n            ...overrideProps,\n            getReferenceClientRect: () => target.getBoundingClientRect(),\n          });\n        },\n      };\n    },\n  };\n\n  const instance = tippy(div(), {\n    ...removeProperties(optionalProps, ['overrides']),\n    plugins: [singleton, ...(optionalProps.plugins || [])],\n    triggerTarget: references,\n  }) as CreateSingletonInstance<CreateSingletonProps>;\n\n  const originalSetProps = instance.setProps;\n\n  instance.setProps = (props): void => {\n    overrides = props.overrides || overrides;\n    originalSetProps(props);\n  };\n\n  instance.setInstances = (nextInstances): void => {\n    enableInstances(true);\n\n    mutTippyInstances = nextInstances;\n\n    enableInstances(false);\n    setReferences();\n\n    instance.setProps({triggerTarget: references});\n  };\n\n  return instance;\n};\n\nexport default createSingleton;\n", "import tippy from '..';\nimport {defaultProps} from '../props';\nimport {Instance, Props, Targets} from '../types';\nimport {ListenerObject} from '../types-internal';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\n\nconst BUBBLING_EVENTS_MAP = {\n  mouseover: 'mouseenter',\n  focusin: 'focus',\n  click: 'click',\n};\n\n/**\n * Creates a delegate instance that controls the creation of tippy instances\n * for child elements (`target` CSS selector).\n */\nfunction delegate(\n  targets: Targets,\n  props: Partial<Props> & {target: string}\n): Instance | Instance[] {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !(props && props.target),\n      [\n        'You must specity a `target` prop indicating a CSS selector string matching',\n        'the target elements that should receive a tippy.',\n      ].join(' ')\n    );\n  }\n\n  let listeners: ListenerObject[] = [];\n  let childTippyInstances: Instance[] = [];\n\n  const {target} = props;\n\n  const nativeProps = removeProperties(props, ['target']);\n  const parentProps = {...nativeProps, trigger: 'manual', touch: false};\n  const childProps = {...nativeProps, showOnCreate: true};\n\n  const returnValue = tippy(targets, parentProps);\n  const normalizedReturnValue = normalizeToArray(returnValue);\n\n  function onTrigger(event: Event): void {\n    if (!event.target) {\n      return;\n    }\n\n    const targetNode = (event.target as Element).closest(target);\n\n    if (!targetNode) {\n      return;\n    }\n\n    // Get relevant trigger with fallbacks:\n    // 1. Check `data-tippy-trigger` attribute on target node\n    // 2. Fallback to `trigger` passed to `delegate()`\n    // 3. Fallback to `defaultProps.trigger`\n    const trigger =\n      targetNode.getAttribute('data-tippy-trigger') ||\n      props.trigger ||\n      defaultProps.trigger;\n\n    // @ts-ignore\n    if (targetNode._tippy) {\n      return;\n    }\n\n    if (event.type === 'touchstart' && typeof childProps.touch === 'boolean') {\n      return;\n    }\n\n    if (\n      event.type !== 'touchstart' &&\n      trigger.indexOf((BUBBLING_EVENTS_MAP as any)[event.type])\n    ) {\n      return;\n    }\n\n    const instance = tippy(targetNode, childProps);\n\n    if (instance) {\n      childTippyInstances = childTippyInstances.concat(instance);\n    }\n  }\n\n  function on(\n    node: Element,\n    eventType: string,\n    handler: EventListener,\n    options: object | boolean = false\n  ): void {\n    node.addEventListener(eventType, handler, options);\n    listeners.push({node, eventType, handler, options});\n  }\n\n  function addEventListeners(instance: Instance): void {\n    const {reference} = instance;\n\n    on(reference, 'touchstart', onTrigger);\n    on(reference, 'mouseover', onTrigger);\n    on(reference, 'focusin', onTrigger);\n    on(reference, 'click', onTrigger);\n  }\n\n  function removeEventListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function applyMutations(instance: Instance): void {\n    const originalDestroy = instance.destroy;\n    instance.destroy = (shouldDestroyChildInstances = true): void => {\n      if (shouldDestroyChildInstances) {\n        childTippyInstances.forEach((instance) => {\n          instance.destroy();\n        });\n      }\n\n      childTippyInstances = [];\n\n      removeEventListeners();\n      originalDestroy();\n    };\n\n    addEventListeners(instance);\n  }\n\n  normalizedReturnValue.forEach(applyMutations);\n\n  return returnValue;\n}\n\nexport default delegate;\n", "import {BACKDROP_CLASS} from '../constants';\nimport {div, setVisibilityState} from '../dom-utils';\nimport {getChildren} from '../template';\nimport {AnimateFill} from '../types';\nimport {errorWhen} from '../validation';\n\nconst animateFill: AnimateFill = {\n  name: 'animateFill',\n  defaultValue: false,\n  fn(instance) {\n    // @ts-ignore\n    if (!instance.props.render?.$$tippy) {\n      if (__DEV__) {\n        errorWhen(\n          instance.props.animateFill,\n          'The `animateFill` plugin requires the default render function.'\n        );\n      }\n\n      return {};\n    }\n\n    const {box, content} = getChildren(instance.popper);\n\n    const backdrop = instance.props.animateFill\n      ? createBackdropElement()\n      : null;\n\n    return {\n      onCreate(): void {\n        if (backdrop) {\n          box.insertBefore(backdrop, box.firstElementChild!);\n          box.setAttribute('data-animatefill', '');\n          box.style.overflow = 'hidden';\n\n          instance.setProps({arrow: false, animation: 'shift-away'});\n        }\n      },\n      onMount(): void {\n        if (backdrop) {\n          const {transitionDuration} = box.style;\n          const duration = Number(transitionDuration.replace('ms', ''));\n\n          // The content should fade in after the backdrop has mostly filled the\n          // tooltip element. `clip-path` is the other alternative but is not\n          // well-supported and is buggy on some devices.\n          content.style.transitionDelay = `${Math.round(duration / 10)}ms`;\n\n          backdrop.style.transitionDuration = transitionDuration;\n          setVisibilityState([backdrop], 'visible');\n        }\n      },\n      onShow(): void {\n        if (backdrop) {\n          backdrop.style.transitionDuration = '0ms';\n        }\n      },\n      onHide(): void {\n        if (backdrop) {\n          setVisibilityState([backdrop], 'hidden');\n        }\n      },\n    };\n  },\n};\n\nexport default animateFill;\n\nfunction createBackdropElement(): HTMLDivElement {\n  const backdrop = div();\n  backdrop.className = BACKDROP_CLASS;\n  setVisibilityState([backdrop], 'hidden');\n  return backdrop;\n}\n", "import {getOwnerDocument} from '../dom-utils';\nimport {FollowCursor, Instance} from '../types';\n\nlet mouseCoords = {clientX: 0, clientY: 0};\nlet activeInstances: Array<{instance: Instance; doc: Document}> = [];\n\nfunction storeMouseCoords({clientX, clientY}: MouseEvent): void {\n  mouseCoords = {clientX, clientY};\n}\n\nfunction addMouseCoordsListener(doc: Document): void {\n  doc.addEventListener('mousemove', storeMouseCoords);\n}\n\nfunction removeMouseCoordsListener(doc: Document): void {\n  doc.removeEventListener('mousemove', storeMouseCoords);\n}\n\nconst followCursor: FollowCursor = {\n  name: 'followCursor',\n  defaultValue: false,\n  fn(instance) {\n    const reference = instance.reference;\n    const doc = getOwnerDocument(instance.props.triggerTarget || reference);\n\n    let isInternalUpdate = false;\n    let wasFocusEvent = false;\n    let isUnmounted = true;\n    let prevProps = instance.props;\n\n    function getIsInitialBehavior(): boolean {\n      return (\n        instance.props.followCursor === 'initial' && instance.state.isVisible\n      );\n    }\n\n    function addListener(): void {\n      doc.addEventListener('mousemove', onMouseMove);\n    }\n\n    function removeListener(): void {\n      doc.removeEventListener('mousemove', onMouseMove);\n    }\n\n    function unsetGetReferenceClientRect(): void {\n      isInternalUpdate = true;\n      instance.setProps({getReferenceClientRect: null});\n      isInternalUpdate = false;\n    }\n\n    function onMouseMove(event: MouseEvent): void {\n      // If the instance is interactive, avoid updating the position unless it's\n      // over the reference element\n      const isCursorOverReference = event.target\n        ? reference.contains(event.target as Node)\n        : true;\n      const {followCursor} = instance.props;\n      const {clientX, clientY} = event;\n\n      const rect = reference.getBoundingClientRect();\n      const relativeX = clientX - rect.left;\n      const relativeY = clientY - rect.top;\n\n      if (isCursorOverReference || !instance.props.interactive) {\n        instance.setProps({\n          getReferenceClientRect() {\n            const rect = reference.getBoundingClientRect();\n\n            let x = clientX;\n            let y = clientY;\n\n            if (followCursor === 'initial') {\n              x = rect.left + relativeX;\n              y = rect.top + relativeY;\n            }\n\n            const top = followCursor === 'horizontal' ? rect.top : y;\n            const right = followCursor === 'vertical' ? rect.right : x;\n            const bottom = followCursor === 'horizontal' ? rect.bottom : y;\n            const left = followCursor === 'vertical' ? rect.left : x;\n\n            return {\n              width: right - left,\n              height: bottom - top,\n              top,\n              right,\n              bottom,\n              left,\n            };\n          },\n        });\n      }\n    }\n\n    function create(): void {\n      if (instance.props.followCursor) {\n        activeInstances.push({instance, doc});\n        addMouseCoordsListener(doc);\n      }\n    }\n\n    function destroy(): void {\n      activeInstances = activeInstances.filter(\n        (data) => data.instance !== instance\n      );\n\n      if (activeInstances.filter((data) => data.doc === doc).length === 0) {\n        removeMouseCoordsListener(doc);\n      }\n    }\n\n    return {\n      onCreate: create,\n      onDestroy: destroy,\n      onBeforeUpdate(): void {\n        prevProps = instance.props;\n      },\n      onAfterUpdate(_, {followCursor}): void {\n        if (isInternalUpdate) {\n          return;\n        }\n\n        if (\n          followCursor !== undefined &&\n          prevProps.followCursor !== followCursor\n        ) {\n          destroy();\n\n          if (followCursor) {\n            create();\n\n            if (\n              instance.state.isMounted &&\n              !wasFocusEvent &&\n              !getIsInitialBehavior()\n            ) {\n              addListener();\n            }\n          } else {\n            removeListener();\n            unsetGetReferenceClientRect();\n          }\n        }\n      },\n      onMount(): void {\n        if (instance.props.followCursor) {\n          if (isUnmounted) {\n            onMouseMove(mouseCoords as MouseEvent);\n            isUnmounted = false;\n          }\n\n          if (!wasFocusEvent && !getIsInitialBehavior()) {\n            addListener();\n          }\n        }\n      },\n      onTrigger(_, {type}): void {\n        wasFocusEvent = type === 'focus';\n      },\n      onHidden(): void {\n        if (instance.props.followCursor) {\n          unsetGetReferenceClientRect();\n          removeListener();\n          isUnmounted = true;\n        }\n      },\n    };\n  },\n};\n\nexport default followCursor;\n", "import {Modifier, Placement} from '@popperjs/core';\nimport {isMouseEvent} from '../dom-utils';\nimport {BasePlacement, InlinePositioning, Props} from '../types';\nimport {arrayFrom, getBasePlacement} from '../utils';\n\nfunction getProps(props: Props, modifier: Modifier<any, any>): Partial<Props> {\n  return {\n    popperOptions: {\n      ...props.popperOptions,\n      modifiers: [\n        ...(props.popperOptions?.modifiers || []).filter(\n          ({name}) => name !== modifier.name\n        ),\n        modifier,\n      ],\n    },\n  };\n}\n\nconst inlinePositioning: InlinePositioning = {\n  name: 'inlinePositioning',\n  defaultValue: false,\n  fn(instance) {\n    const {reference} = instance;\n\n    function isEnabled(): boolean {\n      return !!instance.props.inlinePositioning;\n    }\n\n    let placement: Placement;\n    let cursorRectIndex = -1;\n    let isInternalUpdate = false;\n\n    const modifier: Modifier<'tippyInlinePositioning', {}> = {\n      name: 'tippyInlinePositioning',\n      enabled: true,\n      phase: 'afterWrite',\n      fn({state}) {\n        if (isEnabled()) {\n          if (placement !== state.placement) {\n            instance.setProps({\n              getReferenceClientRect: () =>\n                getReferenceClientRect(state.placement),\n            });\n          }\n\n          placement = state.placement;\n        }\n      },\n    };\n\n    function getReferenceClientRect(placement: Placement): ClientRect {\n      return getInlineBoundingClientRect(\n        getBasePlacement(placement),\n        reference.getBoundingClientRect(),\n        arrayFrom(reference.getClientRects()),\n        cursorRectIndex\n      );\n    }\n\n    function setInternalProps(partialProps: Partial<Props>): void {\n      isInternalUpdate = true;\n      instance.setProps(partialProps);\n      isInternalUpdate = false;\n    }\n\n    function addModifier(): void {\n      if (!isInternalUpdate) {\n        setInternalProps(getProps(instance.props, modifier));\n      }\n    }\n\n    return {\n      onCreate: addModifier,\n      onAfterUpdate: addModifier,\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          const rects = arrayFrom(instance.reference.getClientRects());\n          const cursorRect = rects.find(\n            (rect) =>\n              rect.left - 2 <= event.clientX &&\n              rect.right + 2 >= event.clientX &&\n              rect.top - 2 <= event.clientY &&\n              rect.bottom + 2 >= event.clientY\n          );\n\n          cursorRectIndex = rects.indexOf(cursorRect);\n        }\n      },\n      onUntrigger(): void {\n        cursorRectIndex = -1;\n      },\n    };\n  },\n};\n\nexport default inlinePositioning;\n\nexport function getInlineBoundingClientRect(\n  currentBasePlacement: BasePlacement | null,\n  boundingRect: ClientRect,\n  clientRects: ClientRect[],\n  cursorRectIndex: number\n): ClientRect {\n  // Not an inline element, or placement is not yet known\n  if (clientRects.length < 2 || currentBasePlacement === null) {\n    return boundingRect;\n  }\n\n  // There are two rects and they are disjoined\n  if (\n    clientRects.length === 2 &&\n    cursorRectIndex >= 0 &&\n    clientRects[0].left > clientRects[1].right\n  ) {\n    return clientRects[cursorRectIndex] || boundingRect;\n  }\n\n  switch (currentBasePlacement) {\n    case 'top':\n    case 'bottom': {\n      const firstRect = clientRects[0];\n      const lastRect = clientRects[clientRects.length - 1];\n      const isTop = currentBasePlacement === 'top';\n\n      const top = firstRect.top;\n      const bottom = lastRect.bottom;\n      const left = isTop ? firstRect.left : lastRect.left;\n      const right = isTop ? firstRect.right : lastRect.right;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    case 'left':\n    case 'right': {\n      const minLeft = Math.min(...clientRects.map((rects) => rects.left));\n      const maxRight = Math.max(...clientRects.map((rects) => rects.right));\n      const measureRects = clientRects.filter((rect) =>\n        currentBasePlacement === 'left'\n          ? rect.left === minLeft\n          : rect.right === maxRight\n      );\n\n      const top = measureRects[0].top;\n      const bottom = measureRects[measureRects.length - 1].bottom;\n      const left = minLeft;\n      const right = maxRight;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    default: {\n      return boundingRect;\n    }\n  }\n}\n", "import {VirtualElement} from '@popperjs/core';\nimport {ReferenceElement, Sticky} from '../types';\n\nconst sticky: Sticky = {\n  name: 'sticky',\n  defaultValue: false,\n  fn(instance) {\n    const {reference, popper} = instance;\n\n    function getReference(): ReferenceElement | VirtualElement {\n      return instance.popperInstance\n        ? instance.popperInstance.state.elements.reference\n        : reference;\n    }\n\n    function shouldCheck(value: 'reference' | 'popper'): boolean {\n      return instance.props.sticky === true || instance.props.sticky === value;\n    }\n\n    let prevRefRect: ClientRect | null = null;\n    let prevPopRect: ClientRect | null = null;\n\n    function updatePosition(): void {\n      const currentRefRect = shouldCheck('reference')\n        ? getReference().getBoundingClientRect()\n        : null;\n      const currentPopRect = shouldCheck('popper')\n        ? popper.getBoundingClientRect()\n        : null;\n\n      if (\n        (currentRefRect && areRectsDifferent(prevRefRect, currentRefRect)) ||\n        (currentPopRect && areRectsDifferent(prevPopRect, currentPopRect))\n      ) {\n        if (instance.popperInstance) {\n          instance.popperInstance.update();\n        }\n      }\n\n      prevRefRect = currentRefRect;\n      prevPopRect = currentPopRect;\n\n      if (instance.state.isMounted) {\n        requestAnimationFrame(updatePosition);\n      }\n    }\n\n    return {\n      onMount(): void {\n        if (instance.props.sticky) {\n          updatePosition();\n        }\n      },\n    };\n  },\n};\n\nexport default sticky;\n\nfunction areRectsDifferent(\n  rectA: ClientRect | null,\n  rectB: ClientRect | null\n): boolean {\n  if (rectA && rectB) {\n    return (\n      rectA.top !== rectB.top ||\n      rectA.right !== rectB.right ||\n      rectA.bottom !== rectB.bottom ||\n      rectA.left !== rectB.left\n    );\n  }\n\n  return true;\n}\n", "import tippy from '../src';\nimport {render} from '../src/template';\n\ntippy.setDefaultProps({render});\n\nexport {default, hideAll} from '../src';\nexport {default as createSingleton} from '../src/addons/createSingleton';\nexport {default as delegate} from '../src/addons/delegate';\nexport {default as animateFill} from '../src/plugins/animateFill';\nexport {default as followCursor} from '../src/plugins/followCursor';\nexport {default as inlinePositioning} from '../src/plugins/inlinePositioning';\nexport {default as sticky} from '../src/plugins/sticky';\nexport {ROUND_ARROW as roundArrow} from '../src/constants';\n"], "names": ["ROUND_ARROW", "BOX_CLASS", "CONTENT_CLASS", "BACKDROP_CLASS", "ARROW_CLASS", "SVG_ARROW_CLASS", "TOUCH_OPTIONS", "passive", "capture", "hasOwnProperty", "obj", "key", "call", "getValueAtIndexOrReturn", "value", "index", "defaultValue", "Array", "isArray", "v", "isType", "type", "str", "toString", "indexOf", "invokeWithArgsOrReturn", "args", "debounce", "fn", "ms", "timeout", "arg", "clearTimeout", "setTimeout", "removeProperties", "keys", "clone", "for<PERSON>ach", "splitBySpaces", "split", "filter", "Boolean", "normalizeToArray", "concat", "pushIfUnique", "arr", "push", "unique", "item", "getBasePlacement", "placement", "arrayFrom", "slice", "removeUndefinedProps", "Object", "reduce", "acc", "undefined", "div", "document", "createElement", "isElement", "some", "isNodeList", "isMouseEvent", "isReferenceElement", "_tippy", "reference", "getArrayOfElements", "querySelectorAll", "setTransitionDuration", "els", "el", "style", "transitionDuration", "setVisibilityState", "state", "setAttribute", "getOwnerDocument", "elementOrElements", "element", "ownerDocument", "isCursorOutsideInteractiveBorder", "popperTreeData", "event", "clientX", "clientY", "every", "popperRect", "popperState", "props", "interactiveBorder", "basePlacement", "offsetData", "modifiersData", "offset", "topDistance", "top", "y", "bottomDistance", "bottom", "leftDistance", "left", "x", "rightDistance", "right", "exceedsTop", "exceedsBottom", "exceedsLeft", "exceedsRight", "updateTransitionEndListener", "box", "action", "listener", "method", "currentInput", "is<PERSON><PERSON>ch", "lastMouseMoveTime", "onDocumentTouchStart", "window", "performance", "addEventListener", "onDocumentMouseMove", "now", "removeEventListener", "onWindowBlur", "activeElement", "instance", "blur", "isVisible", "bindGlobalEventListeners", "<PERSON><PERSON><PERSON><PERSON>", "ua", "navigator", "userAgent", "isIE", "test", "createMemoryLeakWarning", "txt", "join", "clean", "spacesAndTabs", "lineStartWithSpaces", "replace", "trim", "getDevMessage", "message", "getFormattedMessage", "visitedMessages", "resetVisitedMessages", "Set", "warn<PERSON><PERSON>", "condition", "has", "add", "console", "warn", "<PERSON><PERSON><PERSON>", "error", "validateTargets", "targets", "didPassFalsyValue", "didPassPlainObject", "prototype", "String", "pluginProps", "animateFill", "followCursor", "inlinePositioning", "sticky", "renderProps", "allowHTML", "animation", "arrow", "content", "inertia", "max<PERSON><PERSON><PERSON>", "role", "theme", "zIndex", "defaultProps", "appendTo", "body", "aria", "expanded", "delay", "duration", "getReferenceClientRect", "hideOnClick", "ignoreAttributes", "interactive", "interactiveDebounce", "moveTransition", "onAfterUpdate", "onBeforeUpdate", "onCreate", "onDestroy", "onHidden", "onHide", "onMount", "onShow", "onShown", "onTrigger", "onUntrigger", "onClickOutside", "plugins", "popperOptions", "render", "showOnCreate", "touch", "trigger", "triggerTarget", "defaultKeys", "setDefaultProps", "partialProps", "validateProps", "getExtendedPassedProps", "passedProps", "plugin", "name", "getDataAttributeProps", "propKeys", "valueAsString", "getAttribute", "JSON", "parse", "e", "evaluateProps", "out", "prop", "nonPluginProps", "didPassUnknownProp", "length", "innerHTML", "dangerouslySetInnerHTML", "html", "createArrowElement", "className", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "popper", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxChildren", "children", "find", "node", "classList", "contains", "backdrop", "onUpdate", "prevProps", "nextProps", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "$$tippy", "idCounter", "mouseMoveListeners", "mountedInstances", "createTippy", "showTimeout", "hideTimeout", "scheduleHideAnimationFrame", "isVisibleFromClick", "didHideDueToDocumentMouseDown", "didTouchMove", "ignoreOnFirstUpdate", "lastTriggerEvent", "currentTransitionEndListener", "onFirstUpdate", "listeners", "debouncedOnMouseMove", "onMouseMove", "currentTarget", "doc", "id", "popperInstance", "isEnabled", "isDestroyed", "isMounted", "isShown", "clearDelayTimeouts", "setProps", "show", "hide", "hideWithInteractivity", "enable", "disable", "unmount", "destroy", "pluginsHooks", "map", "hasAriaExpanded", "hasAttribute", "addListeners", "handleAriaExpandedAttribute", "handleStyles", "invokeHook", "scheduleShow", "getNormalizedTouchSettings", "getIsCustomTouchBehavior", "getIsDefaultRenderFn", "getC<PERSON>rentTarget", "getDefaultTemplateChildren", "get<PERSON>elay", "isShow", "pointerEvents", "hook", "shouldInvokePropsHook", "pluginHooks", "handleAriaContentAttribute", "attr", "nodes", "currentValue", "nextValue", "cleanupInteractiveMouseListeners", "onDocumentPress", "target", "removeDocumentPress", "onTouchMove", "onTouchStart", "addDocumentPress", "onTransitionedOut", "callback", "onTransitionEnd", "parentNode", "onTransitionedIn", "on", "eventType", "handler", "options", "onMouseLeave", "onBlurOrFocusOut", "removeListeners", "shouldScheduleClickHide", "isEventListenerStopped", "wasFocused", "scheduleHide", "isCursorOverReferenceOrPopper", "getNestedPopperTree", "getBoundingClientRect", "shouldBail", "relatedTarget", "createPopperInstance", "destroyPopperInstance", "computedReference", "contextElement", "tippyModifier", "enabled", "phase", "requires", "attributes", "modifiers", "padding", "adaptive", "createPopper", "mount", "nextElement<PERSON><PERSON>ling", "touchValue", "touchDelay", "requestAnimationFrame", "cancelAnimationFrame", "nestedPopper", "forceUpdate", "isAlreadyVisible", "isDisabled", "isTouchAndTouchDisabled", "visibility", "transition", "offsetHeight", "isAlreadyHidden", "i", "tippy", "optionalProps", "elements", "isSingleContentElement", "isMoreThanOneReferenceElement", "instances", "hide<PERSON>ll", "excludedReferenceOrInstance", "exclude", "isExcluded", "originalDuration", "createSingleton", "tippyInstances", "mutTippyInstances", "references", "overrides", "setReferences", "enableInstances", "singleton", "overrideProps", "originalSetProps", "setInstances", "nextInstances", "BUBBLING_EVENTS_MAP", "mouseover", "focusin", "click", "delegate", "childTippyInstances", "nativeProps", "parentProps", "childProps", "returnValue", "normalizedReturnValue", "targetNode", "closest", "addEventListeners", "removeEventListeners", "applyMutations", "original<PERSON><PERSON>roy", "shouldDestroyChildInstances", "createBackdropElement", "insertBefore", "overflow", "Number", "transitionDelay", "Math", "round", "mouseCoords", "activeInstances", "storeMouseCoords", "addMouseCoordsListener", "removeMouseCoordsListener", "isInternalUpdate", "wasFocusEvent", "isUnmounted", "getIsInitialBehavior", "addListener", "removeListener", "unsetGetReferenceClientRect", "isCursorOverReference", "rect", "relativeX", "relativeY", "width", "height", "create", "data", "_", "getProps", "modifier", "cursorRectIndex", "getInlineBoundingClientRect", "getClientRects", "setInternalProps", "addModifier", "rects", "cursorRect", "currentBasePlacement", "boundingRect", "clientRects", "firstRect", "lastRect", "isTop", "minLeft", "min", "maxRight", "max", "measureRects", "getReference", "<PERSON><PERSON><PERSON><PERSON>", "prevRefRect", "prevPopRect", "updatePosition", "currentRefRect", "currentPopRect", "areRectsDifferent", "update", "rectA", "rectB"], "mappings": ";;;;;;;;;;;IAAaA,WAAW,GACtB;AAEK,IAAMC,SAAS,cAAf;AACA,IAAMC,aAAa,kBAAnB;AACA,IAAMC,cAAc,mBAApB;AACA,IAAMC,WAAW,gBAAjB;AACA,IAAMC,eAAe,oBAArB;AAEA,IAAMC,aAAa,GAAG;AAACC,EAAAA,OAAO,EAAE,IAAV;AAAgBC,EAAAA,OAAO,EAAE;AAAzB,CAAtB;;ACPA,SAASC,cAAT,CAAwBC,GAAxB,EAAqCC,GAArC,EAA2D;AAChE,SAAO,GAAGF,cAAH,CAAkBG,IAAlB,CAAuBF,GAAvB,EAA4BC,GAA5B,CAAP;AACD;AAED,AAAO,SAASE,uBAAT,CACLC,KADK,EAELC,KAFK,EAGLC,YAHK,EAIF;AACH,MAAIC,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;AACxB,QAAMK,CAAC,GAAGL,KAAK,CAACC,KAAD,CAAf;AACA,WAAOI,CAAC,IAAI,IAAL,GACHF,KAAK,CAACC,OAAN,CAAcF,YAAd,IACEA,YAAY,CAACD,KAAD,CADd,GAEEC,YAHC,GAIHG,CAJJ;AAKD;;AAED,SAAOL,KAAP;AACD;AAED,AAAO,SAASM,MAAT,CAAgBN,KAAhB,EAA4BO,IAA5B,EAAmD;AACxD,MAAMC,GAAG,GAAG,GAAGC,QAAH,CAAYX,IAAZ,CAAiBE,KAAjB,CAAZ;AACA,SAAOQ,GAAG,CAACE,OAAJ,CAAY,SAAZ,MAA2B,CAA3B,IAAgCF,GAAG,CAACE,OAAJ,CAAeH,IAAf,UAA0B,CAAC,CAAlE;AACD;AAED,AAAO,SAASI,sBAAT,CAAgCX,KAAhC,EAA4CY,IAA5C,EAA8D;AACnE,SAAO,OAAOZ,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,MAAL,SAASY,IAAT,CAA9B,GAA+CZ,KAAtD;AACD;AAED,AAAO,SAASa,QAAT,CACLC,EADK,EAELC,EAFK,EAGa;AAClB;AACA,MAAIA,EAAE,KAAK,CAAX,EAAc;AACZ,WAAOD,EAAP;AACD;;AAED,MAAIE,OAAJ;AAEA,SAAO,UAACC,GAAD,EAAe;AACpBC,IAAAA,YAAY,CAACF,OAAD,CAAZ;AACAA,IAAAA,OAAO,GAAGG,UAAU,CAAC,YAAM;AACzBL,MAAAA,EAAE,CAACG,GAAD,CAAF;AACD,KAFmB,EAEjBF,EAFiB,CAApB;AAGD,GALD;AAMD;AAED,AAAO,SAASK,gBAAT,CAA6BxB,GAA7B,EAAqCyB,IAArC,EAAiE;AACtE,MAAMC,KAAK,qBAAO1B,GAAP,CAAX;AACAyB,EAAAA,IAAI,CAACE,OAAL,CAAa,UAAC1B,GAAD,EAAS;AACpB,WAAQyB,KAAD,CAAezB,GAAf,CAAP;AACD,GAFD;AAGA,SAAOyB,KAAP;AACD;AAED,AAAO,SAASE,aAAT,CAAuBxB,KAAvB,EAAgD;AACrD,SAAOA,KAAK,CAACyB,KAAN,CAAY,KAAZ,EAAmBC,MAAnB,CAA0BC,OAA1B,CAAP;AACD;AAED,AAAO,SAASC,gBAAT,CAA6B5B,KAA7B,EAAkD;AACvD,SAAQ,EAAD,CAAY6B,MAAZ,CAAmB7B,KAAnB,CAAP;AACD;AAED,AAAO,SAAS8B,YAAT,CAAyBC,GAAzB,EAAmC/B,KAAnC,EAAmD;AACxD,MAAI+B,GAAG,CAACrB,OAAJ,CAAYV,KAAZ,MAAuB,CAAC,CAA5B,EAA+B;AAC7B+B,IAAAA,GAAG,CAACC,IAAJ,CAAShC,KAAT;AACD;AACF;AAED,AAIO,SAASiC,MAAT,CAAmBF,GAAnB,EAAkC;AACvC,SAAOA,GAAG,CAACL,MAAJ,CAAW,UAACQ,IAAD,EAAOjC,KAAP;AAAA,WAAiB8B,GAAG,CAACrB,OAAJ,CAAYwB,IAAZ,MAAsBjC,KAAvC;AAAA,GAAX,CAAP;AACD;AAED,AAIO,SAASkC,gBAAT,CAA0BC,SAA1B,EAA+D;AACpE,SAAOA,SAAS,CAACX,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAP;AACD;AAED,AAAO,SAASY,SAAT,CAAmBrC,KAAnB,EAAiD;AACtD,SAAO,GAAGsC,KAAH,CAASxC,IAAT,CAAcE,KAAd,CAAP;AACD;AAED,AAAO,SAASuC,oBAAT,CACL3C,GADK,EAE6B;AAClC,SAAO4C,MAAM,CAACnB,IAAP,CAAYzB,GAAZ,EAAiB6C,MAAjB,CAAwB,UAACC,GAAD,EAAM7C,GAAN,EAAc;AAC3C,QAAID,GAAG,CAACC,GAAD,CAAH,KAAa8C,SAAjB,EAA4B;AACzBD,MAAAA,GAAD,CAAa7C,GAAb,IAAoBD,GAAG,CAACC,GAAD,CAAvB;AACD;;AAED,WAAO6C,GAAP;AACD,GANM,EAMJ,EANI,CAAP;AAOD;;ACnGM,SAASE,GAAT,GAA+B;AACpC,SAAOC,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAP;AACD;AAED,AAAO,SAASC,SAAT,CAAmB/C,KAAnB,EAAwE;AAC7E,SAAO,CAAC,SAAD,EAAY,UAAZ,EAAwBgD,IAAxB,CAA6B,UAACzC,IAAD;AAAA,WAAUD,MAAM,CAACN,KAAD,EAAQO,IAAR,CAAhB;AAAA,GAA7B,CAAP;AACD;AAED,AAAO,SAAS0C,UAAT,CAAoBjD,KAApB,EAAuD;AAC5D,SAAOM,MAAM,CAACN,KAAD,EAAQ,UAAR,CAAb;AACD;AAED,AAAO,SAASkD,YAAT,CAAsBlD,KAAtB,EAA2D;AAChE,SAAOM,MAAM,CAACN,KAAD,EAAQ,YAAR,CAAb;AACD;AAED,AAAO,SAASmD,kBAAT,CAA4BnD,KAA5B,EAAmE;AACxE,SAAO,CAAC,EAAEA,KAAK,IAAIA,KAAK,CAACoD,MAAf,IAAyBpD,KAAK,CAACoD,MAAN,CAAaC,SAAb,KAA2BrD,KAAtD,CAAR;AACD;AAED,AAAO,SAASsD,kBAAT,CAA4BtD,KAA5B,EAAuD;AAC5D,MAAI+C,SAAS,CAAC/C,KAAD,CAAb,EAAsB;AACpB,WAAO,CAACA,KAAD,CAAP;AACD;;AAED,MAAIiD,UAAU,CAACjD,KAAD,CAAd,EAAuB;AACrB,WAAOqC,SAAS,CAACrC,KAAD,CAAhB;AACD;;AAED,MAAIG,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;AACxB,WAAOA,KAAP;AACD;;AAED,SAAOqC,SAAS,CAACQ,QAAQ,CAACU,gBAAT,CAA0BvD,KAA1B,CAAD,CAAhB;AACD;AAED,AAAO,SAASwD,qBAAT,CACLC,GADK,EAELzD,KAFK,EAGC;AACNyD,EAAAA,GAAG,CAAClC,OAAJ,CAAY,UAACmC,EAAD,EAAQ;AAClB,QAAIA,EAAJ,EAAQ;AACNA,MAAAA,EAAE,CAACC,KAAH,CAASC,kBAAT,GAAiC5D,KAAjC;AACD;AACF,GAJD;AAKD;AAED,AAAO,SAAS6D,kBAAT,CACLJ,GADK,EAELK,KAFK,EAGC;AACNL,EAAAA,GAAG,CAAClC,OAAJ,CAAY,UAACmC,EAAD,EAAQ;AAClB,QAAIA,EAAJ,EAAQ;AACNA,MAAAA,EAAE,CAACK,YAAH,CAAgB,YAAhB,EAA8BD,KAA9B;AACD;AACF,GAJD;AAKD;AAED,AAAO,SAASE,gBAAT,CACLC,iBADK,EAEK;AAAA,0BACQrC,gBAAgB,CAACqC,iBAAD,CADxB;AAAA,MACHC,OADG;;AAEV,SAAOA,OAAO,GAAGA,OAAO,CAACC,aAAR,IAAyBtB,QAA5B,GAAuCA,QAArD;AACD;AAED,AAAO,SAASuB,gCAAT,CACLC,cADK,EAELC,KAFK,EAGI;AAAA,MACFC,OADE,GACkBD,KADlB,CACFC,OADE;AAAA,MACOC,OADP,GACkBF,KADlB,CACOE,OADP;AAGT,SAAOH,cAAc,CAACI,KAAf,CAAqB,gBAAsC;AAAA,QAApCC,UAAoC,QAApCA,UAAoC;AAAA,QAAxBC,WAAwB,QAAxBA,WAAwB;AAAA,QAAXC,KAAW,QAAXA,KAAW;AAAA,QACzDC,iBADyD,GACpCD,KADoC,CACzDC,iBADyD;AAEhE,QAAMC,aAAa,GAAG3C,gBAAgB,CAACwC,WAAW,CAACvC,SAAb,CAAtC;AACA,QAAM2C,UAAU,GAAGJ,WAAW,CAACK,aAAZ,CAA0BC,MAA7C;;AAEA,QAAI,CAACF,UAAL,EAAiB;AACf,aAAO,IAAP;AACD;;AAED,QAAMG,WAAW,GAAGJ,aAAa,KAAK,QAAlB,GAA6BC,UAAU,CAACI,GAAX,CAAgBC,CAA7C,GAAiD,CAArE;AACA,QAAMC,cAAc,GAAGP,aAAa,KAAK,KAAlB,GAA0BC,UAAU,CAACO,MAAX,CAAmBF,CAA7C,GAAiD,CAAxE;AACA,QAAMG,YAAY,GAAGT,aAAa,KAAK,OAAlB,GAA4BC,UAAU,CAACS,IAAX,CAAiBC,CAA7C,GAAiD,CAAtE;AACA,QAAMC,aAAa,GAAGZ,aAAa,KAAK,MAAlB,GAA2BC,UAAU,CAACY,KAAX,CAAkBF,CAA7C,GAAiD,CAAvE;AAEA,QAAMG,UAAU,GACdlB,UAAU,CAACS,GAAX,GAAiBX,OAAjB,GAA2BU,WAA3B,GAAyCL,iBAD3C;AAEA,QAAMgB,aAAa,GACjBrB,OAAO,GAAGE,UAAU,CAACY,MAArB,GAA8BD,cAA9B,GAA+CR,iBADjD;AAEA,QAAMiB,WAAW,GACfpB,UAAU,CAACc,IAAX,GAAkBjB,OAAlB,GAA4BgB,YAA5B,GAA2CV,iBAD7C;AAEA,QAAMkB,YAAY,GAChBxB,OAAO,GAAGG,UAAU,CAACiB,KAArB,GAA6BD,aAA7B,GAA6Cb,iBAD/C;AAGA,WAAOe,UAAU,IAAIC,aAAd,IAA+BC,WAA/B,IAA8CC,YAArD;AACD,GAxBM,CAAP;AAyBD;AAED,AAAO,SAASC,2BAAT,CACLC,GADK,EAELC,MAFK,EAGLC,QAHK,EAIC;AACN,MAAMC,MAAM,GAAMF,MAAN,kBAAZ,CADM;AAMN;;AACA,GAAC,eAAD,EAAkB,qBAAlB,EAAyC3E,OAAzC,CAAiD,UAAC+C,KAAD,EAAW;AAC1D2B,IAAAA,GAAG,CAACG,MAAD,CAAH,CAAY9B,KAAZ,EAAmB6B,QAAnB;AACD,GAFD;AAGD;;ACjHM,IAAME,YAAY,GAAG;AAACC,EAAAA,OAAO,EAAE;AAAV,CAArB;AACP,IAAIC,iBAAiB,GAAG,CAAxB;AAEA;;;;;;;AAMA,AAAO,SAASC,oBAAT,GAAsC;AAC3C,MAAIH,YAAY,CAACC,OAAjB,EAA0B;AACxB;AACD;;AAEDD,EAAAA,YAAY,CAACC,OAAb,GAAuB,IAAvB;;AAEA,MAAIG,MAAM,CAACC,WAAX,EAAwB;AACtB7D,IAAAA,QAAQ,CAAC8D,gBAAT,CAA0B,WAA1B,EAAuCC,mBAAvC;AACD;AACF;AAED;;;;;;AAKA,AAAO,SAASA,mBAAT,GAAqC;AAC1C,MAAMC,GAAG,GAAGH,WAAW,CAACG,GAAZ,EAAZ;;AAEA,MAAIA,GAAG,GAAGN,iBAAN,GAA0B,EAA9B,EAAkC;AAChCF,IAAAA,YAAY,CAACC,OAAb,GAAuB,KAAvB;AAEAzD,IAAAA,QAAQ,CAACiE,mBAAT,CAA6B,WAA7B,EAA0CF,mBAA1C;AACD;;AAEDL,EAAAA,iBAAiB,GAAGM,GAApB;AACD;AAED;;;;;;;AAMA,AAAO,SAASE,YAAT,GAA8B;AACnC,MAAMC,aAAa,GAAGnE,QAAQ,CAACmE,aAA/B;;AAEA,MAAI7D,kBAAkB,CAAC6D,aAAD,CAAtB,EAAuC;AACrC,QAAMC,QAAQ,GAAGD,aAAa,CAAC5D,MAA/B;;AAEA,QAAI4D,aAAa,CAACE,IAAd,IAAsB,CAACD,QAAQ,CAACnD,KAAT,CAAeqD,SAA1C,EAAqD;AACnDH,MAAAA,aAAa,CAACE,IAAd;AACD;AACF;AACF;AAED,AAAe,SAASE,wBAAT,GAA0C;AACvDvE,EAAAA,QAAQ,CAAC8D,gBAAT,CAA0B,YAA1B,EAAwCH,oBAAxC,EAA8DhH,aAA9D;AACAiH,EAAAA,MAAM,CAACE,gBAAP,CAAwB,MAAxB,EAAgCI,YAAhC;AACD;;AC9DM,IAAMM,SAAS,GACpB,OAAOZ,MAAP,KAAkB,WAAlB,IAAiC,OAAO5D,QAAP,KAAoB,WADhD;AAGP,IAAMyE,EAAE,GAAGD,SAAS,GAAGE,SAAS,CAACC,SAAb,GAAyB,EAA7C;AAEA,AAAO,IAAMC,IAAI,GAAG,kBAAkBC,IAAlB,CAAuBJ,EAAvB,CAAb;;ACHA,SAASK,uBAAT,CAAiCvB,MAAjC,EAAyD;AAC9D,MAAMwB,GAAG,GAAGxB,MAAM,KAAK,SAAX,GAAuB,YAAvB,GAAsC,GAAlD;AAEA,SAAO,CACFA,MADE,0BACyBwB,GADzB,8CAEL,oCAFK,EAGLC,IAHK,CAGA,GAHA,CAAP;AAID;AAED,AAAO,SAASC,KAAT,CAAe9H,KAAf,EAAsC;AAC3C,MAAM+H,aAAa,GAAG,YAAtB;AACA,MAAMC,mBAAmB,GAAG,WAA5B;AAEA,SAAOhI,KAAK,CACTiI,OADI,CACIF,aADJ,EACmB,GADnB,EAEJE,OAFI,CAEID,mBAFJ,EAEyB,EAFzB,EAGJE,IAHI,EAAP;AAID;;AAED,SAASC,aAAT,CAAuBC,OAAvB,EAAgD;AAC9C,SAAON,KAAK,4BAGRA,KAAK,CAACM,OAAD,CAHG,0GAAZ;AAOD;;AAED,AAAO,SAASC,mBAAT,CAA6BD,OAA7B,EAAwD;AAC7D,SAAO,CACLD,aAAa,CAACC,OAAD,CADR;AAGL,wDAHK;AAKL,oBALK;AAOL,mBAPK,CAAP;AASD;;AAGD,IAAIE,eAAJ;;AACA,2CAAa;AACXC,EAAAA,oBAAoB;AACrB;;AAED,AAAO,SAASA,oBAAT,GAAsC;AAC3CD,EAAAA,eAAe,GAAG,IAAIE,GAAJ,EAAlB;AACD;AAED,AAAO,SAASC,QAAT,CAAkBC,SAAlB,EAAsCN,OAAtC,EAA6D;AAClE,MAAIM,SAAS,IAAI,CAACJ,eAAe,CAACK,GAAhB,CAAoBP,OAApB,CAAlB,EAAgD;AAAA;;AAC9CE,IAAAA,eAAe,CAACM,GAAhB,CAAoBR,OAApB;;AACA,gBAAAS,OAAO,EAACC,IAAR,iBAAgBT,mBAAmB,CAACD,OAAD,CAAnC;AACD;AACF;AAED,AAAO,SAASW,SAAT,CAAmBL,SAAnB,EAAuCN,OAAvC,EAA8D;AACnE,MAAIM,SAAS,IAAI,CAACJ,eAAe,CAACK,GAAhB,CAAoBP,OAApB,CAAlB,EAAgD;AAAA;;AAC9CE,IAAAA,eAAe,CAACM,GAAhB,CAAoBR,OAApB;;AACA,iBAAAS,OAAO,EAACG,KAAR,kBAAiBX,mBAAmB,CAACD,OAAD,CAApC;AACD;AACF;AAED,AAAO,SAASa,eAAT,CAAyBC,OAAzB,EAAiD;AACtD,MAAMC,iBAAiB,GAAG,CAACD,OAA3B;AACA,MAAME,kBAAkB,GACtB5G,MAAM,CAAC6G,SAAP,CAAiB5I,QAAjB,CAA0BX,IAA1B,CAA+BoJ,OAA/B,MAA4C,iBAA5C,IACA,CAAEA,OAAD,CAAiBvC,gBAFpB;AAIAoC,EAAAA,SAAS,CACPI,iBADO,EAEP,CACE,oBADF,EAEE,MAAMG,MAAM,CAACJ,OAAD,CAAZ,GAAwB,GAF1B,EAGE,oEAHF,EAIE,yBAJF,EAKErB,IALF,CAKO,GALP,CAFO,CAAT;AAUAkB,EAAAA,SAAS,CACPK,kBADO,EAEP,CACE,yEADF,EAEE,oEAFF,EAGEvB,IAHF,CAGO,GAHP,CAFO,CAAT;AAOD;;AClFD,IAAM0B,WAAW,GAAG;AAClBC,EAAAA,WAAW,EAAE,KADK;AAElBC,EAAAA,YAAY,EAAE,KAFI;AAGlBC,EAAAA,iBAAiB,EAAE,KAHD;AAIlBC,EAAAA,MAAM,EAAE;AAJU,CAApB;AAOA,IAAMC,WAAW,GAAG;AAClBC,EAAAA,SAAS,EAAE,KADO;AAElBC,EAAAA,SAAS,EAAE,MAFO;AAGlBC,EAAAA,KAAK,EAAE,IAHW;AAIlBC,EAAAA,OAAO,EAAE,EAJS;AAKlBC,EAAAA,OAAO,EAAE,KALS;AAMlBC,EAAAA,QAAQ,EAAE,GANQ;AAOlBC,EAAAA,IAAI,EAAE,SAPY;AAQlBC,EAAAA,KAAK,EAAE,EARW;AASlBC,EAAAA,MAAM,EAAE;AATU,CAApB;AAYA,AAAO,IAAMC,YAA0B;AACrCC,EAAAA,QAAQ,EAAE;AAAA,WAAM1H,QAAQ,CAAC2H,IAAf;AAAA,GAD2B;AAErCC,EAAAA,IAAI,EAAE;AACJT,IAAAA,OAAO,EAAE,MADL;AAEJU,IAAAA,QAAQ,EAAE;AAFN,GAF+B;AAMrCC,EAAAA,KAAK,EAAE,CAN8B;AAOrCC,EAAAA,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,CAP2B;AAQrCC,EAAAA,sBAAsB,EAAE,IARa;AASrCC,EAAAA,WAAW,EAAE,IATwB;AAUrCC,EAAAA,gBAAgB,EAAE,KAVmB;AAWrCC,EAAAA,WAAW,EAAE,KAXwB;AAYrCnG,EAAAA,iBAAiB,EAAE,CAZkB;AAarCoG,EAAAA,mBAAmB,EAAE,CAbgB;AAcrCC,EAAAA,cAAc,EAAE,EAdqB;AAerCjG,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,EAAJ,CAf6B;AAgBrCkG,EAAAA,aAhBqC,2BAgBrB,EAhBqB;AAiBrCC,EAAAA,cAjBqC,4BAiBpB,EAjBoB;AAkBrCC,EAAAA,QAlBqC,sBAkB1B,EAlB0B;AAmBrCC,EAAAA,SAnBqC,uBAmBzB,EAnByB;AAoBrCC,EAAAA,QApBqC,sBAoB1B,EApB0B;AAqBrCC,EAAAA,MArBqC,oBAqB5B,EArB4B;AAsBrCC,EAAAA,OAtBqC,qBAsB3B,EAtB2B;AAuBrCC,EAAAA,MAvBqC,oBAuB5B,EAvB4B;AAwBrCC,EAAAA,OAxBqC,qBAwB3B,EAxB2B;AAyBrCC,EAAAA,SAzBqC,uBAyBzB,EAzByB;AA0BrCC,EAAAA,WA1BqC,yBA0BvB,EA1BuB;AA2BrCC,EAAAA,cA3BqC,4BA2BpB,EA3BoB;AA4BrC1J,EAAAA,SAAS,EAAE,KA5B0B;AA6BrC2J,EAAAA,OAAO,EAAE,EA7B4B;AA8BrCC,EAAAA,aAAa,EAAE,EA9BsB;AA+BrCC,EAAAA,MAAM,EAAE,IA/B6B;AAgCrCC,EAAAA,YAAY,EAAE,KAhCuB;AAiCrCC,EAAAA,KAAK,EAAE,IAjC8B;AAkCrCC,EAAAA,OAAO,EAAE,kBAlC4B;AAmCrCC,EAAAA,aAAa,EAAE;AAnCsB,GAoClC9C,WApCkC,MAqClCK,WArCkC,CAAhC;AAwCP,IAAM0C,WAAW,GAAG9J,MAAM,CAACnB,IAAP,CAAYiJ,YAAZ,CAApB;AAEA,AAAO,IAAMiC,eAAyC,GAAG,SAA5CA,eAA4C,CAACC,YAAD,EAAkB;AACzE;AACA,6CAAa;AACXC,IAAAA,aAAa,CAACD,YAAD,EAAe,EAAf,CAAb;AACD;;AAED,MAAMnL,IAAI,GAAGmB,MAAM,CAACnB,IAAP,CAAYmL,YAAZ,CAAb;AACAnL,EAAAA,IAAI,CAACE,OAAL,CAAa,UAAC1B,GAAD,EAAS;AACnByK,IAAAA,YAAD,CAAsBzK,GAAtB,IAA6B2M,YAAY,CAAC3M,GAAD,CAAzC;AACD,GAFD;AAGD,CAVM;AAYP,AAAO,SAAS6M,sBAAT,CACLC,WADK,EAEW;AAChB,MAAMZ,OAAO,GAAGY,WAAW,CAACZ,OAAZ,IAAuB,EAAvC;AACA,MAAMxC,WAAW,GAAGwC,OAAO,CAACtJ,MAAR,CAAwC,UAACC,GAAD,EAAMkK,MAAN,EAAiB;AAAA,QACpEC,IADoE,GAC9CD,MAD8C,CACpEC,IADoE;AAAA,QAC9D3M,YAD8D,GAC9C0M,MAD8C,CAC9D1M,YAD8D;;AAG3E,QAAI2M,IAAJ,EAAU;AACRnK,MAAAA,GAAG,CAACmK,IAAD,CAAH,GACEF,WAAW,CAACE,IAAD,CAAX,KAAsBlK,SAAtB,GAAkCgK,WAAW,CAACE,IAAD,CAA7C,GAAsD3M,YADxD;AAED;;AAED,WAAOwC,GAAP;AACD,GATmB,EASjB,EATiB,CAApB;AAWA,2BACKiK,WADL,MAEKpD,WAFL;AAID;AAED,AAAO,SAASuD,qBAAT,CACLzJ,SADK,EAEL0I,OAFK,EAGoB;AACzB,MAAMgB,QAAQ,GAAGhB,OAAO,GACpBvJ,MAAM,CAACnB,IAAP,CAAYqL,sBAAsB,mBAAKpC,YAAL;AAAmByB,IAAAA,OAAO,EAAPA;AAAnB,KAAlC,CADoB,GAEpBO,WAFJ;AAIA,MAAM1H,KAAK,GAAGmI,QAAQ,CAACtK,MAAT,CACZ,UAACC,GAAD,EAAgD7C,GAAhD,EAAwD;AACtD,QAAMmN,aAAa,GAAG,CACpB3J,SAAS,CAAC4J,YAAV,iBAAqCpN,GAArC,KAA+C,EAD3B,EAEpBqI,IAFoB,EAAtB;;AAIA,QAAI,CAAC8E,aAAL,EAAoB;AAClB,aAAOtK,GAAP;AACD;;AAED,QAAI7C,GAAG,KAAK,SAAZ,EAAuB;AACrB6C,MAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWmN,aAAX;AACD,KAFD,MAEO;AACL,UAAI;AACFtK,QAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWqN,IAAI,CAACC,KAAL,CAAWH,aAAX,CAAX;AACD,OAFD,CAEE,OAAOI,CAAP,EAAU;AACV1K,QAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWmN,aAAX;AACD;AACF;;AAED,WAAOtK,GAAP;AACD,GArBW,EAsBZ,EAtBY,CAAd;AAyBA,SAAOkC,KAAP;AACD;AAED,AAAO,SAASyI,aAAT,CACLhK,SADK,EAELuB,KAFK,EAGE;AACP,MAAM0I,GAAG,qBACJ1I,KADI;AAEPoF,IAAAA,OAAO,EAAErJ,sBAAsB,CAACiE,KAAK,CAACoF,OAAP,EAAgB,CAAC3G,SAAD,CAAhB;AAFxB,KAGHuB,KAAK,CAACmG,gBAAN,GACA,EADA,GAEA+B,qBAAqB,CAACzJ,SAAD,EAAYuB,KAAK,CAACmH,OAAlB,CALlB,CAAT;AAQAuB,EAAAA,GAAG,CAAC7C,IAAJ,qBACKH,YAAY,CAACG,IADlB,MAEK6C,GAAG,CAAC7C,IAFT;AAKA6C,EAAAA,GAAG,CAAC7C,IAAJ,GAAW;AACTC,IAAAA,QAAQ,EACN4C,GAAG,CAAC7C,IAAJ,CAASC,QAAT,KAAsB,MAAtB,GAA+B9F,KAAK,CAACoG,WAArC,GAAmDsC,GAAG,CAAC7C,IAAJ,CAASC,QAFrD;AAGTV,IAAAA,OAAO,EACLsD,GAAG,CAAC7C,IAAJ,CAAST,OAAT,KAAqB,MAArB,GACIpF,KAAK,CAACoG,WAAN,GACE,IADF,GAEE,aAHN,GAIIsC,GAAG,CAAC7C,IAAJ,CAAST;AARN,GAAX;AAWA,SAAOsD,GAAP;AACD;AAED,AAAO,SAASb,aAAT,CACLD,YADK,EAELT,OAFK,EAGC;AAAA,MAFNS,YAEM;AAFNA,IAAAA,YAEM,GAFyB,EAEzB;AAAA;;AAAA,MADNT,OACM;AADNA,IAAAA,OACM,GADc,EACd;AAAA;;AACN,MAAM1K,IAAI,GAAGmB,MAAM,CAACnB,IAAP,CAAYmL,YAAZ,CAAb;AACAnL,EAAAA,IAAI,CAACE,OAAL,CAAa,UAACgM,IAAD,EAAU;AACrB,QAAMC,cAAc,GAAGpM,gBAAgB,CACrCkJ,YADqC,EAErC9H,MAAM,CAACnB,IAAP,CAAYkI,WAAZ,CAFqC,CAAvC;AAKA,QAAIkE,kBAAkB,GAAG,CAAC9N,cAAc,CAAC6N,cAAD,EAAiBD,IAAjB,CAAxC,CANqB;;AASrB,QAAIE,kBAAJ,EAAwB;AACtBA,MAAAA,kBAAkB,GAChB1B,OAAO,CAACrK,MAAR,CAAe,UAACkL,MAAD;AAAA,eAAYA,MAAM,CAACC,IAAP,KAAgBU,IAA5B;AAAA,OAAf,EAAiDG,MAAjD,KAA4D,CAD9D;AAED;;AAEDjF,IAAAA,QAAQ,CACNgF,kBADM,EAEN,OACOF,IADP,QAEE,sEAFF,EAGE,2DAHF,EAIE,MAJF,EAKE,8DALF,EAME,wDANF,EAOE1F,IAPF,CAOO,GAPP,CAFM,CAAR;AAWD,GAzBD;AA0BD;;AC3LD,IAAM8F,SAAS,GAAG,SAAZA,SAAY;AAAA,SAAmB,WAAnB;AAAA,CAAlB;;AAEA,SAASC,uBAAT,CAAiC1J,OAAjC,EAAmD2J,IAAnD,EAAuE;AACrE3J,EAAAA,OAAO,CAACyJ,SAAS,EAAV,CAAP,GAAuBE,IAAvB;AACD;;AAED,SAASC,kBAAT,CAA4B9N,KAA5B,EAAmE;AACjE,MAAM+J,KAAK,GAAGnH,GAAG,EAAjB;;AAEA,MAAI5C,KAAK,KAAK,IAAd,EAAoB;AAClB+J,IAAAA,KAAK,CAACgE,SAAN,GAAkBzO,WAAlB;AACD,GAFD,MAEO;AACLyK,IAAAA,KAAK,CAACgE,SAAN,GAAkBxO,eAAlB;;AAEA,QAAIwD,SAAS,CAAC/C,KAAD,CAAb,EAAsB;AACpB+J,MAAAA,KAAK,CAACiE,WAAN,CAAkBhO,KAAlB;AACD,KAFD,MAEO;AACL4N,MAAAA,uBAAuB,CAAC7D,KAAD,EAAQ/J,KAAR,CAAvB;AACD;AACF;;AAED,SAAO+J,KAAP;AACD;;AAED,AAAO,SAASkE,UAAT,CAAoBjE,OAApB,EAA6CpF,KAA7C,EAAiE;AACtE,MAAI7B,SAAS,CAAC6B,KAAK,CAACoF,OAAP,CAAb,EAA8B;AAC5B4D,IAAAA,uBAAuB,CAAC5D,OAAD,EAAU,EAAV,CAAvB;AACAA,IAAAA,OAAO,CAACgE,WAAR,CAAoBpJ,KAAK,CAACoF,OAA1B;AACD,GAHD,MAGO,IAAI,OAAOpF,KAAK,CAACoF,OAAb,KAAyB,UAA7B,EAAyC;AAC9C,QAAIpF,KAAK,CAACiF,SAAV,EAAqB;AACnB+D,MAAAA,uBAAuB,CAAC5D,OAAD,EAAUpF,KAAK,CAACoF,OAAhB,CAAvB;AACD,KAFD,MAEO;AACLA,MAAAA,OAAO,CAACkE,WAAR,GAAsBtJ,KAAK,CAACoF,OAA5B;AACD;AACF;AACF;AAED,AAAO,SAASmE,WAAT,CAAqBC,MAArB,EAA4D;AACjE,MAAMnI,GAAG,GAAGmI,MAAM,CAACC,iBAAnB;AACA,MAAMC,WAAW,GAAGjM,SAAS,CAAC4D,GAAG,CAACsI,QAAL,CAA7B;AAEA,SAAO;AACLtI,IAAAA,GAAG,EAAHA,GADK;AAEL+D,IAAAA,OAAO,EAAEsE,WAAW,CAACE,IAAZ,CAAiB,UAACC,IAAD;AAAA,aAAUA,IAAI,CAACC,SAAL,CAAeC,QAAf,CAAwBvP,aAAxB,CAAV;AAAA,KAAjB,CAFJ;AAGL2K,IAAAA,KAAK,EAAEuE,WAAW,CAACE,IAAZ,CACL,UAACC,IAAD;AAAA,aACEA,IAAI,CAACC,SAAL,CAAeC,QAAf,CAAwBrP,WAAxB,KACAmP,IAAI,CAACC,SAAL,CAAeC,QAAf,CAAwBpP,eAAxB,CAFF;AAAA,KADK,CAHF;AAQLqP,IAAAA,QAAQ,EAAEN,WAAW,CAACE,IAAZ,CAAiB,UAACC,IAAD;AAAA,aACzBA,IAAI,CAACC,SAAL,CAAeC,QAAf,CAAwBtP,cAAxB,CADyB;AAAA,KAAjB;AARL,GAAP;AAYD;AAED,AAAO,SAAS4M,MAAT,CACLhF,QADK,EAKL;AACA,MAAMmH,MAAM,GAAGxL,GAAG,EAAlB;AAEA,MAAMqD,GAAG,GAAGrD,GAAG,EAAf;AACAqD,EAAAA,GAAG,CAAC8H,SAAJ,GAAgB5O,SAAhB;AACA8G,EAAAA,GAAG,CAAClC,YAAJ,CAAiB,YAAjB,EAA+B,QAA/B;AACAkC,EAAAA,GAAG,CAAClC,YAAJ,CAAiB,UAAjB,EAA6B,IAA7B;AAEA,MAAMiG,OAAO,GAAGpH,GAAG,EAAnB;AACAoH,EAAAA,OAAO,CAAC+D,SAAR,GAAoB3O,aAApB;AACA4K,EAAAA,OAAO,CAACjG,YAAR,CAAqB,YAArB,EAAmC,QAAnC;AAEAkK,EAAAA,UAAU,CAACjE,OAAD,EAAU/C,QAAQ,CAACrC,KAAnB,CAAV;AAEAwJ,EAAAA,MAAM,CAACJ,WAAP,CAAmB/H,GAAnB;AACAA,EAAAA,GAAG,CAAC+H,WAAJ,CAAgBhE,OAAhB;AAEA6E,EAAAA,QAAQ,CAAC5H,QAAQ,CAACrC,KAAV,EAAiBqC,QAAQ,CAACrC,KAA1B,CAAR;;AAEA,WAASiK,QAAT,CAAkBC,SAAlB,EAAoCC,SAApC,EAA4D;AAAA,uBAC5BZ,WAAW,CAACC,MAAD,CADiB;AAAA,QACnDnI,GADmD,gBACnDA,GADmD;AAAA,QAC9C+D,OAD8C,gBAC9CA,OAD8C;AAAA,QACrCD,KADqC,gBACrCA,KADqC;;AAG1D,QAAIgF,SAAS,CAAC3E,KAAd,EAAqB;AACnBnE,MAAAA,GAAG,CAAClC,YAAJ,CAAiB,YAAjB,EAA+BgL,SAAS,CAAC3E,KAAzC;AACD,KAFD,MAEO;AACLnE,MAAAA,GAAG,CAAC+I,eAAJ,CAAoB,YAApB;AACD;;AAED,QAAI,OAAOD,SAAS,CAACjF,SAAjB,KAA+B,QAAnC,EAA6C;AAC3C7D,MAAAA,GAAG,CAAClC,YAAJ,CAAiB,gBAAjB,EAAmCgL,SAAS,CAACjF,SAA7C;AACD,KAFD,MAEO;AACL7D,MAAAA,GAAG,CAAC+I,eAAJ,CAAoB,gBAApB;AACD;;AAED,QAAID,SAAS,CAAC9E,OAAd,EAAuB;AACrBhE,MAAAA,GAAG,CAAClC,YAAJ,CAAiB,cAAjB,EAAiC,EAAjC;AACD,KAFD,MAEO;AACLkC,MAAAA,GAAG,CAAC+I,eAAJ,CAAoB,cAApB;AACD;;AAED/I,IAAAA,GAAG,CAACtC,KAAJ,CAAUuG,QAAV,GACE,OAAO6E,SAAS,CAAC7E,QAAjB,KAA8B,QAA9B,GACO6E,SAAS,CAAC7E,QADjB,UAEI6E,SAAS,CAAC7E,QAHhB;;AAKA,QAAI6E,SAAS,CAAC5E,IAAd,EAAoB;AAClBlE,MAAAA,GAAG,CAAClC,YAAJ,CAAiB,MAAjB,EAAyBgL,SAAS,CAAC5E,IAAnC;AACD,KAFD,MAEO;AACLlE,MAAAA,GAAG,CAAC+I,eAAJ,CAAoB,MAApB;AACD;;AAED,QACEF,SAAS,CAAC9E,OAAV,KAAsB+E,SAAS,CAAC/E,OAAhC,IACA8E,SAAS,CAACjF,SAAV,KAAwBkF,SAAS,CAAClF,SAFpC,EAGE;AACAoE,MAAAA,UAAU,CAACjE,OAAD,EAAU/C,QAAQ,CAACrC,KAAnB,CAAV;AACD;;AAED,QAAImK,SAAS,CAAChF,KAAd,EAAqB;AACnB,UAAI,CAACA,KAAL,EAAY;AACV9D,QAAAA,GAAG,CAAC+H,WAAJ,CAAgBF,kBAAkB,CAACiB,SAAS,CAAChF,KAAX,CAAlC;AACD,OAFD,MAEO,IAAI+E,SAAS,CAAC/E,KAAV,KAAoBgF,SAAS,CAAChF,KAAlC,EAAyC;AAC9C9D,QAAAA,GAAG,CAACgJ,WAAJ,CAAgBlF,KAAhB;AACA9D,QAAAA,GAAG,CAAC+H,WAAJ,CAAgBF,kBAAkB,CAACiB,SAAS,CAAChF,KAAX,CAAlC;AACD;AACF,KAPD,MAOO,IAAIA,KAAJ,EAAW;AAChB9D,MAAAA,GAAG,CAACgJ,WAAJ,CAAgBlF,KAAhB;AACD;AACF;;AAED,SAAO;AACLqE,IAAAA,MAAM,EAANA,MADK;AAELS,IAAAA,QAAQ,EAARA;AAFK,GAAP;AAID;AAGD;;AACA5C,MAAM,CAACiD,OAAP,GAAiB,IAAjB;;AClHA,IAAIC,SAAS,GAAG,CAAhB;AACA,IAAIC,kBAAmD,GAAG,EAA1D;;AAGA,AAAO,IAAIC,gBAA4B,GAAG,EAAnC;AAEP,AAAe,SAASC,WAAT,CACbjM,SADa,EAEbsJ,WAFa,EAGH;AACV,MAAM/H,KAAK,GAAGyI,aAAa,CAAChK,SAAD,oBACtBiH,YADsB,MAEtBoC,sBAAsB,CAACnK,oBAAoB,CAACoK,WAAD,CAArB,CAFA,EAA3B,CADU;AAOV;AACA;;AACA,MAAI4C,WAAJ;AACA,MAAIC,WAAJ;AACA,MAAIC,0BAAJ;AACA,MAAIC,kBAAkB,GAAG,KAAzB;AACA,MAAIC,6BAA6B,GAAG,KAApC;AACA,MAAIC,YAAY,GAAG,KAAnB;AACA,MAAIC,mBAAmB,GAAG,KAA1B;AACA,MAAIC,gBAAJ;AACA,MAAIC,4BAAJ;AACA,MAAIC,aAAJ;AACA,MAAIC,SAA2B,GAAG,EAAlC;AACA,MAAIC,oBAAoB,GAAGrP,QAAQ,CAACsP,WAAD,EAAcvL,KAAK,CAACqG,mBAApB,CAAnC;AACA,MAAImF,aAAJ;AACA,MAAMC,GAAG,GAAGrM,gBAAgB,CAACY,KAAK,CAACyH,aAAN,IAAuBhJ,SAAxB,CAA5B,CAtBU;AAyBV;AACA;;AACA,MAAMiN,EAAE,GAAGnB,SAAS,EAApB;AACA,MAAMoB,cAAc,GAAG,IAAvB;AACA,MAAMxE,OAAO,GAAG9J,MAAM,CAAC2C,KAAK,CAACmH,OAAP,CAAtB;AAEA,MAAMjI,KAAK,GAAG;AACZ;AACA0M,IAAAA,SAAS,EAAE,IAFC;AAGZ;AACArJ,IAAAA,SAAS,EAAE,KAJC;AAKZ;AACAsJ,IAAAA,WAAW,EAAE,KAND;AAOZ;AACAC,IAAAA,SAAS,EAAE,KARC;AASZ;AACAC,IAAAA,OAAO,EAAE;AAVG,GAAd;AAaA,MAAM1J,QAAkB,GAAG;AACzB;AACAqJ,IAAAA,EAAE,EAAFA,EAFyB;AAGzBjN,IAAAA,SAAS,EAATA,SAHyB;AAIzB+K,IAAAA,MAAM,EAAExL,GAAG,EAJc;AAKzB2N,IAAAA,cAAc,EAAdA,cALyB;AAMzB3L,IAAAA,KAAK,EAALA,KANyB;AAOzBd,IAAAA,KAAK,EAALA,KAPyB;AAQzBiI,IAAAA,OAAO,EAAPA,OARyB;AASzB;AACA6E,IAAAA,kBAAkB,EAAlBA,kBAVyB;AAWzBC,IAAAA,QAAQ,EAARA,QAXyB;AAYzB5C,IAAAA,UAAU,EAAVA,UAZyB;AAazB6C,IAAAA,IAAI,EAAJA,IAbyB;AAczBC,IAAAA,IAAI,EAAJA,IAdyB;AAezBC,IAAAA,qBAAqB,EAArBA,qBAfyB;AAgBzBC,IAAAA,MAAM,EAANA,MAhByB;AAiBzBC,IAAAA,OAAO,EAAPA,OAjByB;AAkBzBC,IAAAA,OAAO,EAAPA,OAlByB;AAmBzBC,IAAAA,OAAO,EAAPA;AAnByB,GAA3B,CA5CU;AAmEV;;AACA;;AACA,MAAI,CAACxM,KAAK,CAACqH,MAAX,EAAmB;AACjB,+CAAa;AACXlD,MAAAA,SAAS,CAAC,IAAD,EAAO,0CAAP,CAAT;AACD;;AAED,WAAO9B,QAAP;AACD,GA3ES;AA8EV;AACA;;;AA/EU,sBAgFiBrC,KAAK,CAACqH,MAAN,CAAahF,QAAb,CAhFjB;AAAA,MAgFHmH,MAhFG,iBAgFHA,MAhFG;AAAA,MAgFKS,QAhFL,iBAgFKA,QAhFL;;AAkFVT,EAAAA,MAAM,CAACrK,YAAP,CAAoB,iBAApB,EAAsD,EAAtD;AACAqK,EAAAA,MAAM,CAACkC,EAAP,cAAoCrJ,QAAQ,CAACqJ,EAA7C;AAEArJ,EAAAA,QAAQ,CAACmH,MAAT,GAAkBA,MAAlB;AACA/K,EAAAA,SAAS,CAACD,MAAV,GAAmB6D,QAAnB;AACAmH,EAAAA,MAAM,CAAChL,MAAP,GAAgB6D,QAAhB;AAEA,MAAMoK,YAAY,GAAGtF,OAAO,CAACuF,GAAR,CAAY,UAAC1E,MAAD;AAAA,WAAYA,MAAM,CAAC9L,EAAP,CAAUmG,QAAV,CAAZ;AAAA,GAAZ,CAArB;AACA,MAAMsK,eAAe,GAAGlO,SAAS,CAACmO,YAAV,CAAuB,eAAvB,CAAxB;AAEAC,EAAAA,YAAY;AACZC,EAAAA,2BAA2B;AAC3BC,EAAAA,YAAY;AAEZC,EAAAA,UAAU,CAAC,UAAD,EAAa,CAAC3K,QAAD,CAAb,CAAV;;AAEA,MAAIrC,KAAK,CAACsH,YAAV,EAAwB;AACtB2F,IAAAA,YAAY;AACb,GApGS;AAuGV;;;AACAzD,EAAAA,MAAM,CAACzH,gBAAP,CAAwB,YAAxB,EAAsC,YAAM;AAC1C,QAAIM,QAAQ,CAACrC,KAAT,CAAeoG,WAAf,IAA8B/D,QAAQ,CAACnD,KAAT,CAAeqD,SAAjD,EAA4D;AAC1DF,MAAAA,QAAQ,CAAC2J,kBAAT;AACD;AACF,GAJD;AAMAxC,EAAAA,MAAM,CAACzH,gBAAP,CAAwB,YAAxB,EAAsC,UAACrC,KAAD,EAAW;AAC/C,QACE2C,QAAQ,CAACrC,KAAT,CAAeoG,WAAf,IACA/D,QAAQ,CAACrC,KAAT,CAAewH,OAAf,CAAuB1L,OAAvB,CAA+B,YAA/B,KAAgD,CAFlD,EAGE;AACA2P,MAAAA,GAAG,CAAC1J,gBAAJ,CAAqB,WAArB,EAAkCuJ,oBAAlC;AACAA,MAAAA,oBAAoB,CAAC5L,KAAD,CAApB;AACD;AACF,GARD;AAUA,SAAO2C,QAAP,CAxHU;AA2HV;AACA;;AACA,WAAS6K,0BAAT,GAAkE;AAAA,QACzD3F,KADyD,GAChDlF,QAAQ,CAACrC,KADuC,CACzDuH,KADyD;AAEhE,WAAOhM,KAAK,CAACC,OAAN,CAAc+L,KAAd,IAAuBA,KAAvB,GAA+B,CAACA,KAAD,EAAQ,CAAR,CAAtC;AACD;;AAED,WAAS4F,wBAAT,GAA6C;AAC3C,WAAOD,0BAA0B,GAAG,CAAH,CAA1B,KAAoC,MAA3C;AACD;;AAED,WAASE,oBAAT,GAAyC;AAAA;;AACvC;AACA,WAAO,CAAC,2BAAC/K,QAAQ,CAACrC,KAAT,CAAeqH,MAAhB,qBAAC,sBAAuBiD,OAAxB,CAAR;AACD;;AAED,WAAS+C,gBAAT,GAAqC;AACnC,WAAO7B,aAAa,IAAI/M,SAAxB;AACD;;AAED,WAAS6O,0BAAT,GAAsD;AACpD,WAAO/D,WAAW,CAACC,MAAD,CAAlB;AACD;;AAED,WAAS+D,QAAT,CAAkBC,MAAlB,EAA2C;AACzC;AACA;AACA;AACA,QACGnL,QAAQ,CAACnD,KAAT,CAAe4M,SAAf,IAA4B,CAACzJ,QAAQ,CAACnD,KAAT,CAAeqD,SAA7C,IACAd,YAAY,CAACC,OADb,IAECwJ,gBAAgB,IAAIA,gBAAgB,CAACvP,IAAjB,KAA0B,OAHjD,EAIE;AACA,aAAO,CAAP;AACD;;AAED,WAAOR,uBAAuB,CAC5BkH,QAAQ,CAACrC,KAAT,CAAe+F,KADa,EAE5ByH,MAAM,GAAG,CAAH,GAAO,CAFe,EAG5B9H,YAAY,CAACK,KAHe,CAA9B;AAKD;;AAED,WAASgH,YAAT,GAA8B;AAC5BvD,IAAAA,MAAM,CAACzK,KAAP,CAAa0O,aAAb,GACEpL,QAAQ,CAACrC,KAAT,CAAeoG,WAAf,IAA8B/D,QAAQ,CAACnD,KAAT,CAAeqD,SAA7C,GAAyD,EAAzD,GAA8D,MADhE;AAEAiH,IAAAA,MAAM,CAACzK,KAAP,CAAa0G,MAAb,QAAyBpD,QAAQ,CAACrC,KAAT,CAAeyF,MAAxC;AACD;;AAED,WAASuH,UAAT,CACEU,IADF,EAEE1R,IAFF,EAGE2R,qBAHF,EAIQ;AAAA,QADNA,qBACM;AADNA,MAAAA,qBACM,GADkB,IAClB;AAAA;;AACNlB,IAAAA,YAAY,CAAC9P,OAAb,CAAqB,UAACiR,WAAD,EAAiB;AACpC,UAAIA,WAAW,CAACF,IAAD,CAAf,EAAuB;AACrBE,QAAAA,WAAW,CAACF,IAAD,CAAX,eAAsB1R,IAAtB;AACD;AACF,KAJD;;AAMA,QAAI2R,qBAAJ,EAA2B;AAAA;;AACzB,yBAAAtL,QAAQ,CAACrC,KAAT,EAAe0N,IAAf,yBAAwB1R,IAAxB;AACD;AACF;;AAED,WAAS6R,0BAAT,GAA4C;AAAA,QACnChI,IADmC,GAC3BxD,QAAQ,CAACrC,KADkB,CACnC6F,IADmC;;AAG1C,QAAI,CAACA,IAAI,CAACT,OAAV,EAAmB;AACjB;AACD;;AAED,QAAM0I,IAAI,aAAWjI,IAAI,CAACT,OAA1B;AACA,QAAMsG,EAAE,GAAGlC,MAAM,CAACkC,EAAlB;AACA,QAAMqC,KAAK,GAAG/Q,gBAAgB,CAACqF,QAAQ,CAACrC,KAAT,CAAeyH,aAAf,IAAgChJ,SAAjC,CAA9B;AAEAsP,IAAAA,KAAK,CAACpR,OAAN,CAAc,UAACkN,IAAD,EAAU;AACtB,UAAMmE,YAAY,GAAGnE,IAAI,CAACxB,YAAL,CAAkByF,IAAlB,CAArB;;AAEA,UAAIzL,QAAQ,CAACnD,KAAT,CAAeqD,SAAnB,EAA8B;AAC5BsH,QAAAA,IAAI,CAAC1K,YAAL,CAAkB2O,IAAlB,EAAwBE,YAAY,GAAMA,YAAN,SAAsBtC,EAAtB,GAA6BA,EAAjE;AACD,OAFD,MAEO;AACL,YAAMuC,SAAS,GAAGD,YAAY,IAAIA,YAAY,CAAC3K,OAAb,CAAqBqI,EAArB,EAAyB,EAAzB,EAA6BpI,IAA7B,EAAlC;;AAEA,YAAI2K,SAAJ,EAAe;AACbpE,UAAAA,IAAI,CAAC1K,YAAL,CAAkB2O,IAAlB,EAAwBG,SAAxB;AACD,SAFD,MAEO;AACLpE,UAAAA,IAAI,CAACO,eAAL,CAAqB0D,IAArB;AACD;AACF;AACF,KAdD;AAeD;;AAED,WAAShB,2BAAT,GAA6C;AAC3C,QAAIH,eAAe,IAAI,CAACtK,QAAQ,CAACrC,KAAT,CAAe6F,IAAf,CAAoBC,QAA5C,EAAsD;AACpD;AACD;;AAED,QAAMiI,KAAK,GAAG/Q,gBAAgB,CAACqF,QAAQ,CAACrC,KAAT,CAAeyH,aAAf,IAAgChJ,SAAjC,CAA9B;AAEAsP,IAAAA,KAAK,CAACpR,OAAN,CAAc,UAACkN,IAAD,EAAU;AACtB,UAAIxH,QAAQ,CAACrC,KAAT,CAAeoG,WAAnB,EAAgC;AAC9ByD,QAAAA,IAAI,CAAC1K,YAAL,CACE,eADF,EAEEkD,QAAQ,CAACnD,KAAT,CAAeqD,SAAf,IAA4BsH,IAAI,KAAKwD,gBAAgB,EAArD,GACI,MADJ,GAEI,OAJN;AAMD,OAPD,MAOO;AACLxD,QAAAA,IAAI,CAACO,eAAL,CAAqB,eAArB;AACD;AACF,KAXD;AAYD;;AAED,WAAS8D,gCAAT,GAAkD;AAChDzC,IAAAA,GAAG,CAACvJ,mBAAJ,CAAwB,WAAxB,EAAqCoJ,oBAArC;AACAd,IAAAA,kBAAkB,GAAGA,kBAAkB,CAAC1N,MAAnB,CACnB,UAACyE,QAAD;AAAA,aAAcA,QAAQ,KAAK+J,oBAA3B;AAAA,KADmB,CAArB;AAGD;;AAED,WAAS6C,eAAT,CAAyBzO,KAAzB,EAA+D;AAC7D;AACA,QAAI+B,YAAY,CAACC,OAAjB,EAA0B;AACxB,UAAIsJ,YAAY,IAAItL,KAAK,CAAC/D,IAAN,KAAe,WAAnC,EAAgD;AAC9C;AACD;AACF,KAN4D;;;AAS7D,QACE0G,QAAQ,CAACrC,KAAT,CAAeoG,WAAf,IACAoD,MAAM,CAACO,QAAP,CAAgBrK,KAAK,CAAC0O,MAAtB,CAFF,EAGE;AACA;AACD,KAd4D;;;AAiB7D,QAAIf,gBAAgB,GAAGtD,QAAnB,CAA4BrK,KAAK,CAAC0O,MAAlC,CAAJ,EAA0D;AACxD,UAAI3M,YAAY,CAACC,OAAjB,EAA0B;AACxB;AACD;;AAED,UACEW,QAAQ,CAACnD,KAAT,CAAeqD,SAAf,IACAF,QAAQ,CAACrC,KAAT,CAAewH,OAAf,CAAuB1L,OAAvB,CAA+B,OAA/B,KAA2C,CAF7C,EAGE;AACA;AACD;AACF,KAXD,MAWO;AACLkR,MAAAA,UAAU,CAAC,gBAAD,EAAmB,CAAC3K,QAAD,EAAW3C,KAAX,CAAnB,CAAV;AACD;;AAED,QAAI2C,QAAQ,CAACrC,KAAT,CAAekG,WAAf,KAA+B,IAAnC,EAAyC;AACvC4E,MAAAA,kBAAkB,GAAG,KAArB;AACAzI,MAAAA,QAAQ,CAAC2J,kBAAT;AACA3J,MAAAA,QAAQ,CAAC8J,IAAT,GAHuC;AAMvC;AACA;;AACApB,MAAAA,6BAA6B,GAAG,IAAhC;AACAxO,MAAAA,UAAU,CAAC,YAAM;AACfwO,QAAAA,6BAA6B,GAAG,KAAhC;AACD,OAFS,CAAV,CATuC;AAcvC;AACA;;AACA,UAAI,CAAC1I,QAAQ,CAACnD,KAAT,CAAe4M,SAApB,EAA+B;AAC7BuC,QAAAA,mBAAmB;AACpB;AACF;AACF;;AAED,WAASC,WAAT,GAA6B;AAC3BtD,IAAAA,YAAY,GAAG,IAAf;AACD;;AAED,WAASuD,YAAT,GAA8B;AAC5BvD,IAAAA,YAAY,GAAG,KAAf;AACD;;AAED,WAASwD,gBAAT,GAAkC;AAChC/C,IAAAA,GAAG,CAAC1J,gBAAJ,CAAqB,WAArB,EAAkCoM,eAAlC,EAAmD,IAAnD;AACA1C,IAAAA,GAAG,CAAC1J,gBAAJ,CAAqB,UAArB,EAAiCoM,eAAjC,EAAkDvT,aAAlD;AACA6Q,IAAAA,GAAG,CAAC1J,gBAAJ,CAAqB,YAArB,EAAmCwM,YAAnC,EAAiD3T,aAAjD;AACA6Q,IAAAA,GAAG,CAAC1J,gBAAJ,CAAqB,WAArB,EAAkCuM,WAAlC,EAA+C1T,aAA/C;AACD;;AAED,WAASyT,mBAAT,GAAqC;AACnC5C,IAAAA,GAAG,CAACvJ,mBAAJ,CAAwB,WAAxB,EAAqCiM,eAArC,EAAsD,IAAtD;AACA1C,IAAAA,GAAG,CAACvJ,mBAAJ,CAAwB,UAAxB,EAAoCiM,eAApC,EAAqDvT,aAArD;AACA6Q,IAAAA,GAAG,CAACvJ,mBAAJ,CAAwB,YAAxB,EAAsCqM,YAAtC,EAAoD3T,aAApD;AACA6Q,IAAAA,GAAG,CAACvJ,mBAAJ,CAAwB,WAAxB,EAAqCoM,WAArC,EAAkD1T,aAAlD;AACD;;AAED,WAAS6T,iBAAT,CAA2BzI,QAA3B,EAA6C0I,QAA7C,EAAyE;AACvEC,IAAAA,eAAe,CAAC3I,QAAD,EAAW,YAAM;AAC9B,UACE,CAAC3D,QAAQ,CAACnD,KAAT,CAAeqD,SAAhB,IACAiH,MAAM,CAACoF,UADP,IAEApF,MAAM,CAACoF,UAAP,CAAkB7E,QAAlB,CAA2BP,MAA3B,CAHF,EAIE;AACAkF,QAAAA,QAAQ;AACT;AACF,KARc,CAAf;AASD;;AAED,WAASG,gBAAT,CAA0B7I,QAA1B,EAA4C0I,QAA5C,EAAwE;AACtEC,IAAAA,eAAe,CAAC3I,QAAD,EAAW0I,QAAX,CAAf;AACD;;AAED,WAASC,eAAT,CAAyB3I,QAAzB,EAA2C0I,QAA3C,EAAuE;AACrE,QAAMrN,GAAG,GAAGiM,0BAA0B,GAAGjM,GAAzC;;AAEA,aAASE,QAAT,CAAkB7B,KAAlB,EAAgD;AAC9C,UAAIA,KAAK,CAAC0O,MAAN,KAAiB/M,GAArB,EAA0B;AACxBD,QAAAA,2BAA2B,CAACC,GAAD,EAAM,QAAN,EAAgBE,QAAhB,CAA3B;AACAmN,QAAAA,QAAQ;AACT;AACF,KARoE;AAWrE;;;AACA,QAAI1I,QAAQ,KAAK,CAAjB,EAAoB;AAClB,aAAO0I,QAAQ,EAAf;AACD;;AAEDtN,IAAAA,2BAA2B,CAACC,GAAD,EAAM,QAAN,EAAgB8J,4BAAhB,CAA3B;AACA/J,IAAAA,2BAA2B,CAACC,GAAD,EAAM,KAAN,EAAaE,QAAb,CAA3B;AAEA4J,IAAAA,4BAA4B,GAAG5J,QAA/B;AACD;;AAED,WAASuN,EAAT,CACEC,SADF,EAEEC,OAFF,EAGEC,OAHF,EAIQ;AAAA,QADNA,OACM;AADNA,MAAAA,OACM,GADsB,KACtB;AAAA;;AACN,QAAMlB,KAAK,GAAG/Q,gBAAgB,CAACqF,QAAQ,CAACrC,KAAT,CAAeyH,aAAf,IAAgChJ,SAAjC,CAA9B;AACAsP,IAAAA,KAAK,CAACpR,OAAN,CAAc,UAACkN,IAAD,EAAU;AACtBA,MAAAA,IAAI,CAAC9H,gBAAL,CAAsBgN,SAAtB,EAAiCC,OAAjC,EAA0CC,OAA1C;AACA5D,MAAAA,SAAS,CAACjO,IAAV,CAAe;AAACyM,QAAAA,IAAI,EAAJA,IAAD;AAAOkF,QAAAA,SAAS,EAATA,SAAP;AAAkBC,QAAAA,OAAO,EAAPA,OAAlB;AAA2BC,QAAAA,OAAO,EAAPA;AAA3B,OAAf;AACD,KAHD;AAID;;AAED,WAASpC,YAAT,GAA8B;AAC5B,QAAIM,wBAAwB,EAA5B,EAAgC;AAC9B2B,MAAAA,EAAE,CAAC,YAAD,EAAe9H,SAAf,EAA0B;AAACnM,QAAAA,OAAO,EAAE;AAAV,OAA1B,CAAF;AACAiU,MAAAA,EAAE,CAAC,UAAD,EAAaI,YAAb,EAA4C;AAACrU,QAAAA,OAAO,EAAE;AAAV,OAA5C,CAAF;AACD;;AAED+B,IAAAA,aAAa,CAACyF,QAAQ,CAACrC,KAAT,CAAewH,OAAhB,CAAb,CAAsC7K,OAAtC,CAA8C,UAACoS,SAAD,EAAe;AAC3D,UAAIA,SAAS,KAAK,QAAlB,EAA4B;AAC1B;AACD;;AAEDD,MAAAA,EAAE,CAACC,SAAD,EAAY/H,SAAZ,CAAF;;AAEA,cAAQ+H,SAAR;AACE,aAAK,YAAL;AACED,UAAAA,EAAE,CAAC,YAAD,EAAeI,YAAf,CAAF;AACA;;AACF,aAAK,OAAL;AACEJ,UAAAA,EAAE,CAACjM,IAAI,GAAG,UAAH,GAAgB,MAArB,EAA6BsM,gBAA7B,CAAF;AACA;;AACF,aAAK,SAAL;AACEL,UAAAA,EAAE,CAAC,UAAD,EAAaK,gBAAb,CAAF;AACA;AATJ;AAWD,KAlBD;AAmBD;;AAED,WAASC,eAAT,GAAiC;AAC/B/D,IAAAA,SAAS,CAAC1O,OAAV,CAAkB,gBAAyD;AAAA,UAAvDkN,IAAuD,QAAvDA,IAAuD;AAAA,UAAjDkF,SAAiD,QAAjDA,SAAiD;AAAA,UAAtCC,OAAsC,QAAtCA,OAAsC;AAAA,UAA7BC,OAA6B,QAA7BA,OAA6B;AACzEpF,MAAAA,IAAI,CAAC3H,mBAAL,CAAyB6M,SAAzB,EAAoCC,OAApC,EAA6CC,OAA7C;AACD,KAFD;AAGA5D,IAAAA,SAAS,GAAG,EAAZ;AACD;;AAED,WAASrE,SAAT,CAAmBtH,KAAnB,EAAuC;AAAA;;AACrC,QAAI2P,uBAAuB,GAAG,KAA9B;;AAEA,QACE,CAAChN,QAAQ,CAACnD,KAAT,CAAe0M,SAAhB,IACA0D,sBAAsB,CAAC5P,KAAD,CADtB,IAEAqL,6BAHF,EAIE;AACA;AACD;;AAED,QAAMwE,UAAU,GAAG,sBAAArE,gBAAgB,SAAhB,8BAAkBvP,IAAlB,MAA2B,OAA9C;AAEAuP,IAAAA,gBAAgB,GAAGxL,KAAnB;AACA8L,IAAAA,aAAa,GAAG9L,KAAK,CAAC8L,aAAtB;AAEAsB,IAAAA,2BAA2B;;AAE3B,QAAI,CAACzK,QAAQ,CAACnD,KAAT,CAAeqD,SAAhB,IAA6BjE,YAAY,CAACoB,KAAD,CAA7C,EAAsD;AACpD;AACA;AACA;AACA;AACA8K,MAAAA,kBAAkB,CAAC7N,OAAnB,CAA2B,UAAC4E,QAAD;AAAA,eAAcA,QAAQ,CAAC7B,KAAD,CAAtB;AAAA,OAA3B;AACD,KAxBoC;;;AA2BrC,QACEA,KAAK,CAAC/D,IAAN,KAAe,OAAf,KACC0G,QAAQ,CAACrC,KAAT,CAAewH,OAAf,CAAuB1L,OAAvB,CAA+B,YAA/B,IAA+C,CAA/C,IACCgP,kBAFF,KAGAzI,QAAQ,CAACrC,KAAT,CAAekG,WAAf,KAA+B,KAH/B,IAIA7D,QAAQ,CAACnD,KAAT,CAAeqD,SALjB,EAME;AACA8M,MAAAA,uBAAuB,GAAG,IAA1B;AACD,KARD,MAQO;AACLpC,MAAAA,YAAY,CAACvN,KAAD,CAAZ;AACD;;AAED,QAAIA,KAAK,CAAC/D,IAAN,KAAe,OAAnB,EAA4B;AAC1BmP,MAAAA,kBAAkB,GAAG,CAACuE,uBAAtB;AACD;;AAED,QAAIA,uBAAuB,IAAI,CAACE,UAAhC,EAA4C;AAC1CC,MAAAA,YAAY,CAAC9P,KAAD,CAAZ;AACD;AACF;;AAED,WAAS6L,WAAT,CAAqB7L,KAArB,EAA8C;AAC5C,QAAM0O,MAAM,GAAG1O,KAAK,CAAC0O,MAArB;AACA,QAAMqB,6BAA6B,GACjChR,SAAS,CAACsL,QAAV,CAAmBqE,MAAnB,KAA8B5E,MAAM,CAACO,QAAP,CAAgBqE,MAAhB,CADhC;;AAGA,QAAI1O,KAAK,CAAC/D,IAAN,KAAe,WAAf,IAA8B8T,6BAAlC,EAAiE;AAC/D;AACD;;AAED,QAAMhQ,cAAc,GAAGiQ,mBAAmB,GACvCzS,MADoB,CACbuM,MADa,EAEpBkD,GAFoB,CAEhB,UAAClD,MAAD,EAAY;AAAA;;AACf,UAAMnH,QAAQ,GAAGmH,MAAM,CAAChL,MAAxB;AACA,UAAMU,KAAK,4BAAGmD,QAAQ,CAACsJ,cAAZ,qBAAG,sBAAyBzM,KAAvC;;AAEA,UAAIA,KAAJ,EAAW;AACT,eAAO;AACLY,UAAAA,UAAU,EAAE0J,MAAM,CAACmG,qBAAP,EADP;AAEL5P,UAAAA,WAAW,EAAEb,KAFR;AAGLc,UAAAA,KAAK,EAALA;AAHK,SAAP;AAKD;;AAED,aAAO,IAAP;AACD,KAfoB,EAgBpBlD,MAhBoB,CAgBbC,OAhBa,CAAvB;;AAkBA,QAAIyC,gCAAgC,CAACC,cAAD,EAAiBC,KAAjB,CAApC,EAA6D;AAC3DwO,MAAAA,gCAAgC;AAChCsB,MAAAA,YAAY,CAAC9P,KAAD,CAAZ;AACD;AACF;;AAED,WAASwP,YAAT,CAAsBxP,KAAtB,EAA+C;AAC7C,QAAMkQ,UAAU,GACdN,sBAAsB,CAAC5P,KAAD,CAAtB,IACC2C,QAAQ,CAACrC,KAAT,CAAewH,OAAf,CAAuB1L,OAAvB,CAA+B,OAA/B,KAA2C,CAA3C,IAAgDgP,kBAFnD;;AAIA,QAAI8E,UAAJ,EAAgB;AACd;AACD;;AAED,QAAIvN,QAAQ,CAACrC,KAAT,CAAeoG,WAAnB,EAAgC;AAC9B/D,MAAAA,QAAQ,CAAC+J,qBAAT,CAA+B1M,KAA/B;AACA;AACD;;AAED8P,IAAAA,YAAY,CAAC9P,KAAD,CAAZ;AACD;;AAED,WAASyP,gBAAT,CAA0BzP,KAA1B,EAAmD;AACjD,QACE2C,QAAQ,CAACrC,KAAT,CAAewH,OAAf,CAAuB1L,OAAvB,CAA+B,SAA/B,IAA4C,CAA5C,IACA4D,KAAK,CAAC0O,MAAN,KAAiBf,gBAAgB,EAFnC,EAGE;AACA;AACD,KANgD;;;AASjD,QACEhL,QAAQ,CAACrC,KAAT,CAAeoG,WAAf,IACA1G,KAAK,CAACmQ,aADN,IAEArG,MAAM,CAACO,QAAP,CAAgBrK,KAAK,CAACmQ,aAAtB,CAHF,EAIE;AACA;AACD;;AAEDL,IAAAA,YAAY,CAAC9P,KAAD,CAAZ;AACD;;AAED,WAAS4P,sBAAT,CAAgC5P,KAAhC,EAAuD;AACrD,WAAO+B,YAAY,CAACC,OAAb,GACHyL,wBAAwB,OAAOzN,KAAK,CAAC/D,IAAN,CAAWG,OAAX,CAAmB,OAAnB,KAA+B,CAD3D,GAEH,KAFJ;AAGD;;AAED,WAASgU,oBAAT,GAAsC;AACpCC,IAAAA,qBAAqB;AADe,2BAShC1N,QAAQ,CAACrC,KATuB;AAAA,QAIlCoH,aAJkC,oBAIlCA,aAJkC;AAAA,QAKlC5J,SALkC,oBAKlCA,SALkC;AAAA,QAMlC6C,MANkC,oBAMlCA,MANkC;AAAA,QAOlC4F,sBAPkC,oBAOlCA,sBAPkC;AAAA,QAQlCK,cARkC,oBAQlCA,cARkC;AAWpC,QAAMnB,KAAK,GAAGiI,oBAAoB,KAAK7D,WAAW,CAACC,MAAD,CAAX,CAAoBrE,KAAzB,GAAiC,IAAnE;AAEA,QAAM6K,iBAAiB,GAAG/J,sBAAsB,GAC5C;AACE0J,MAAAA,qBAAqB,EAAE1J,sBADzB;AAEEgK,MAAAA,cAAc,EACZhK,sBAAsB,CAACgK,cAAvB,IAAyC5C,gBAAgB;AAH7D,KAD4C,GAM5C5O,SANJ;AAQA,QAAMyR,aAAsC,GAAG;AAC7CjI,MAAAA,IAAI,EAAE,SADuC;AAE7CkI,MAAAA,OAAO,EAAE,IAFoC;AAG7CC,MAAAA,KAAK,EAAE,aAHsC;AAI7CC,MAAAA,QAAQ,EAAE,CAAC,eAAD,CAJmC;AAK7CnU,MAAAA,EAL6C,qBAKjC;AAAA,YAARgD,KAAQ,SAARA,KAAQ;;AACV,YAAIkO,oBAAoB,EAAxB,EAA4B;AAAA,sCACZE,0BAA0B,EADd;AAAA,cACnBjM,GADmB,yBACnBA,GADmB;;AAG1B,WAAC,WAAD,EAAc,kBAAd,EAAkC,SAAlC,EAA6C1E,OAA7C,CAAqD,UAACmR,IAAD,EAAU;AAC7D,gBAAIA,IAAI,KAAK,WAAb,EAA0B;AACxBzM,cAAAA,GAAG,CAAClC,YAAJ,CAAiB,gBAAjB,EAAmCD,KAAK,CAAC1B,SAAzC;AACD,aAFD,MAEO;AACL,kBAAI0B,KAAK,CAACoR,UAAN,CAAiB9G,MAAjB,kBAAuCsE,IAAvC,CAAJ,EAAoD;AAClDzM,gBAAAA,GAAG,CAAClC,YAAJ,WAAyB2O,IAAzB,EAAiC,EAAjC;AACD,eAFD,MAEO;AACLzM,gBAAAA,GAAG,CAAC+I,eAAJ,WAA4B0D,IAA5B;AACD;AACF;AACF,WAVD;AAYA5O,UAAAA,KAAK,CAACoR,UAAN,CAAiB9G,MAAjB,GAA0B,EAA1B;AACD;AACF;AAvB4C,KAA/C;AA6BA,QAAM+G,SAAmC,GAAG,CAC1C;AACEtI,MAAAA,IAAI,EAAE,QADR;AAEEgH,MAAAA,OAAO,EAAE;AACP5O,QAAAA,MAAM,EAANA;AADO;AAFX,KAD0C,EAO1C;AACE4H,MAAAA,IAAI,EAAE,iBADR;AAEEgH,MAAAA,OAAO,EAAE;AACPuB,QAAAA,OAAO,EAAE;AACPjQ,UAAAA,GAAG,EAAE,CADE;AAEPG,UAAAA,MAAM,EAAE,CAFD;AAGPE,UAAAA,IAAI,EAAE,CAHC;AAIPG,UAAAA,KAAK,EAAE;AAJA;AADF;AAFX,KAP0C,EAkB1C;AACEkH,MAAAA,IAAI,EAAE,MADR;AAEEgH,MAAAA,OAAO,EAAE;AACPuB,QAAAA,OAAO,EAAE;AADF;AAFX,KAlB0C,EAwB1C;AACEvI,MAAAA,IAAI,EAAE,eADR;AAEEgH,MAAAA,OAAO,EAAE;AACPwB,QAAAA,QAAQ,EAAE,CAACnK;AADJ;AAFX,KAxB0C,EA8B1C4J,aA9B0C,CAA5C;;AAiCA,QAAI9C,oBAAoB,MAAMjI,KAA9B,EAAqC;AACnCoL,MAAAA,SAAS,CAACnT,IAAV,CAAe;AACb6K,QAAAA,IAAI,EAAE,OADO;AAEbgH,QAAAA,OAAO,EAAE;AACP3P,UAAAA,OAAO,EAAE6F,KADF;AAEPqL,UAAAA,OAAO,EAAE;AAFF;AAFI,OAAf;AAOD;;AAEDD,IAAAA,SAAS,CAACnT,IAAV,OAAAmT,SAAS,EAAU,CAAAnJ,aAAa,QAAb,YAAAA,aAAa,CAAEmJ,SAAf,KAA4B,EAAtC,CAAT;AAEAlO,IAAAA,QAAQ,CAACsJ,cAAT,GAA0B+E,iBAAY,CACpCV,iBADoC,EAEpCxG,MAFoC,oBAI/BpC,aAJ+B;AAKlC5J,MAAAA,SAAS,EAATA,SALkC;AAMlC4N,MAAAA,aAAa,EAAbA,aANkC;AAOlCmF,MAAAA,SAAS,EAATA;AAPkC,OAAtC;AAUD;;AAED,WAASR,qBAAT,GAAuC;AACrC,QAAI1N,QAAQ,CAACsJ,cAAb,EAA6B;AAC3BtJ,MAAAA,QAAQ,CAACsJ,cAAT,CAAwBa,OAAxB;AACAnK,MAAAA,QAAQ,CAACsJ,cAAT,GAA0B,IAA1B;AACD;AACF;;AAED,WAASgF,KAAT,GAAuB;AAAA,QACdhL,QADc,GACFtD,QAAQ,CAACrC,KADP,CACd2F,QADc;AAGrB,QAAIiJ,UAAJ,CAHqB;AAMrB;AACA;AACA;AACA;;AACA,QAAM/E,IAAI,GAAGwD,gBAAgB,EAA7B;;AAEA,QACGhL,QAAQ,CAACrC,KAAT,CAAeoG,WAAf,IAA8BT,QAAQ,KAAKD,YAAY,CAACC,QAAzD,IACAA,QAAQ,KAAK,QAFf,EAGE;AACAiJ,MAAAA,UAAU,GAAG/E,IAAI,CAAC+E,UAAlB;AACD,KALD,MAKO;AACLA,MAAAA,UAAU,GAAG7S,sBAAsB,CAAC4J,QAAD,EAAW,CAACkE,IAAD,CAAX,CAAnC;AACD,KAnBoB;AAsBrB;;;AACA,QAAI,CAAC+E,UAAU,CAAC7E,QAAX,CAAoBP,MAApB,CAAL,EAAkC;AAChCoF,MAAAA,UAAU,CAACxF,WAAX,CAAuBI,MAAvB;AACD;;AAEDsG,IAAAA,oBAAoB;AAEpB;;AACA,+CAAa;AACX;AACAjM,MAAAA,QAAQ,CACNxB,QAAQ,CAACrC,KAAT,CAAeoG,WAAf,IACET,QAAQ,KAAKD,YAAY,CAACC,QAD5B,IAEEkE,IAAI,CAAC+G,kBAAL,KAA4BpH,MAHxB,EAIN,CACE,8DADF,EAEE,mEAFF,EAGE,0BAHF,EAIE,MAJF,EAKE,kEALF,EAME,mDANF,EAOE,MAPF,EAQE,oEARF,EASE,6DATF,EAUE,sBAVF,EAWE,MAXF,EAYE,wEAZF,EAaEvG,IAbF,CAaO,GAbP,CAJM,CAAR;AAmBD;AACF;;AAED,WAASyM,mBAAT,GAAgD;AAC9C,WAAOjS,SAAS,CACd+L,MAAM,CAAC7K,gBAAP,CAAwB,mBAAxB,CADc,CAAhB;AAGD;;AAED,WAASsO,YAAT,CAAsBvN,KAAtB,EAA2C;AACzC2C,IAAAA,QAAQ,CAAC2J,kBAAT;;AAEA,QAAItM,KAAJ,EAAW;AACTsN,MAAAA,UAAU,CAAC,WAAD,EAAc,CAAC3K,QAAD,EAAW3C,KAAX,CAAd,CAAV;AACD;;AAED8O,IAAAA,gBAAgB;AAEhB,QAAIzI,KAAK,GAAGwH,QAAQ,CAAC,IAAD,CAApB;;AATyC,gCAURL,0BAA0B,EAVlB;AAAA,QAUlC2D,UAVkC;AAAA,QAUtBC,UAVsB;;AAYzC,QAAIrP,YAAY,CAACC,OAAb,IAAwBmP,UAAU,KAAK,MAAvC,IAAiDC,UAArD,EAAiE;AAC/D/K,MAAAA,KAAK,GAAG+K,UAAR;AACD;;AAED,QAAI/K,KAAJ,EAAW;AACT4E,MAAAA,WAAW,GAAGpO,UAAU,CAAC,YAAM;AAC7B8F,QAAAA,QAAQ,CAAC6J,IAAT;AACD,OAFuB,EAErBnG,KAFqB,CAAxB;AAGD,KAJD,MAIO;AACL1D,MAAAA,QAAQ,CAAC6J,IAAT;AACD;AACF;;AAED,WAASsD,YAAT,CAAsB9P,KAAtB,EAA0C;AACxC2C,IAAAA,QAAQ,CAAC2J,kBAAT;AAEAgB,IAAAA,UAAU,CAAC,aAAD,EAAgB,CAAC3K,QAAD,EAAW3C,KAAX,CAAhB,CAAV;;AAEA,QAAI,CAAC2C,QAAQ,CAACnD,KAAT,CAAeqD,SAApB,EAA+B;AAC7B8L,MAAAA,mBAAmB;AAEnB;AACD,KATuC;AAYxC;AACA;AACA;;;AACA,QACEhM,QAAQ,CAACrC,KAAT,CAAewH,OAAf,CAAuB1L,OAAvB,CAA+B,YAA/B,KAAgD,CAAhD,IACAuG,QAAQ,CAACrC,KAAT,CAAewH,OAAf,CAAuB1L,OAAvB,CAA+B,OAA/B,KAA2C,CAD3C,IAEA,CAAC,YAAD,EAAe,WAAf,EAA4BA,OAA5B,CAAoC4D,KAAK,CAAC/D,IAA1C,KAAmD,CAFnD,IAGAmP,kBAJF,EAKE;AACA;AACD;;AAED,QAAM/E,KAAK,GAAGwH,QAAQ,CAAC,KAAD,CAAtB;;AAEA,QAAIxH,KAAJ,EAAW;AACT6E,MAAAA,WAAW,GAAGrO,UAAU,CAAC,YAAM;AAC7B,YAAI8F,QAAQ,CAACnD,KAAT,CAAeqD,SAAnB,EAA8B;AAC5BF,UAAAA,QAAQ,CAAC8J,IAAT;AACD;AACF,OAJuB,EAIrBpG,KAJqB,CAAxB;AAKD,KAND,MAMO;AACL;AACA;AACA8E,MAAAA,0BAA0B,GAAGkG,qBAAqB,CAAC,YAAM;AACvD1O,QAAAA,QAAQ,CAAC8J,IAAT;AACD,OAFiD,CAAlD;AAGD;AACF,GA9vBS;AAiwBV;AACA;;;AACA,WAASE,MAAT,GAAwB;AACtBhK,IAAAA,QAAQ,CAACnD,KAAT,CAAe0M,SAAf,GAA2B,IAA3B;AACD;;AAED,WAASU,OAAT,GAAyB;AACvB;AACA;AACAjK,IAAAA,QAAQ,CAAC8J,IAAT;AACA9J,IAAAA,QAAQ,CAACnD,KAAT,CAAe0M,SAAf,GAA2B,KAA3B;AACD;;AAED,WAASI,kBAAT,GAAoC;AAClC1P,IAAAA,YAAY,CAACqO,WAAD,CAAZ;AACArO,IAAAA,YAAY,CAACsO,WAAD,CAAZ;AACAoG,IAAAA,oBAAoB,CAACnG,0BAAD,CAApB;AACD;;AAED,WAASoB,QAAT,CAAkBrE,YAAlB,EAAsD;AACpD;AACA,+CAAa;AACX/D,MAAAA,QAAQ,CAACxB,QAAQ,CAACnD,KAAT,CAAe2M,WAAhB,EAA6B9I,uBAAuB,CAAC,UAAD,CAApD,CAAR;AACD;;AAED,QAAIV,QAAQ,CAACnD,KAAT,CAAe2M,WAAnB,EAAgC;AAC9B;AACD;;AAEDmB,IAAAA,UAAU,CAAC,gBAAD,EAAmB,CAAC3K,QAAD,EAAWuF,YAAX,CAAnB,CAAV;AAEAwH,IAAAA,eAAe;AAEf,QAAMlF,SAAS,GAAG7H,QAAQ,CAACrC,KAA3B;AACA,QAAMmK,SAAS,GAAG1B,aAAa,CAAChK,SAAD,oBAC1B4D,QAAQ,CAACrC,KADiB,MAE1B4H,YAF0B;AAG7BzB,MAAAA,gBAAgB,EAAE;AAHW,OAA/B;AAMA9D,IAAAA,QAAQ,CAACrC,KAAT,GAAiBmK,SAAjB;AAEA0C,IAAAA,YAAY;;AAEZ,QAAI3C,SAAS,CAAC7D,mBAAV,KAAkC8D,SAAS,CAAC9D,mBAAhD,EAAqE;AACnE6H,MAAAA,gCAAgC;AAChC5C,MAAAA,oBAAoB,GAAGrP,QAAQ,CAC7BsP,WAD6B,EAE7BpB,SAAS,CAAC9D,mBAFmB,CAA/B;AAID,KA/BmD;;;AAkCpD,QAAI6D,SAAS,CAACzC,aAAV,IAA2B,CAAC0C,SAAS,CAAC1C,aAA1C,EAAyD;AACvDzK,MAAAA,gBAAgB,CAACkN,SAAS,CAACzC,aAAX,CAAhB,CAA0C9K,OAA1C,CAAkD,UAACkN,IAAD,EAAU;AAC1DA,QAAAA,IAAI,CAACO,eAAL,CAAqB,eAArB;AACD,OAFD;AAGD,KAJD,MAIO,IAAID,SAAS,CAAC1C,aAAd,EAA6B;AAClChJ,MAAAA,SAAS,CAAC2L,eAAV,CAA0B,eAA1B;AACD;;AAED0C,IAAAA,2BAA2B;AAC3BC,IAAAA,YAAY;;AAEZ,QAAI9C,QAAJ,EAAc;AACZA,MAAAA,QAAQ,CAACC,SAAD,EAAYC,SAAZ,CAAR;AACD;;AAED,QAAI9H,QAAQ,CAACsJ,cAAb,EAA6B;AAC3BmE,MAAAA,oBAAoB,GADO;AAI3B;AACA;AACA;;AACAJ,MAAAA,mBAAmB,GAAG/S,OAAtB,CAA8B,UAACsU,YAAD,EAAkB;AAC9C;AACA;AACAF,QAAAA,qBAAqB,CAACE,YAAY,CAACzS,MAAb,CAAqBmN,cAArB,CAAqCuF,WAAtC,CAArB;AACD,OAJD;AAKD;;AAEDlE,IAAAA,UAAU,CAAC,eAAD,EAAkB,CAAC3K,QAAD,EAAWuF,YAAX,CAAlB,CAAV;AACD;;AAED,WAASyB,UAAT,CAAoBjE,OAApB,EAA4C;AAC1C/C,IAAAA,QAAQ,CAAC4J,QAAT,CAAkB;AAAC7G,MAAAA,OAAO,EAAPA;AAAD,KAAlB;AACD;;AAED,WAAS8G,IAAT,GAAsB;AACpB;AACA,+CAAa;AACXrI,MAAAA,QAAQ,CAACxB,QAAQ,CAACnD,KAAT,CAAe2M,WAAhB,EAA6B9I,uBAAuB,CAAC,MAAD,CAApD,CAAR;AACD,KAJmB;;;AAOpB,QAAMoO,gBAAgB,GAAG9O,QAAQ,CAACnD,KAAT,CAAeqD,SAAxC;AACA,QAAMsJ,WAAW,GAAGxJ,QAAQ,CAACnD,KAAT,CAAe2M,WAAnC;AACA,QAAMuF,UAAU,GAAG,CAAC/O,QAAQ,CAACnD,KAAT,CAAe0M,SAAnC;AACA,QAAMyF,uBAAuB,GAC3B5P,YAAY,CAACC,OAAb,IAAwB,CAACW,QAAQ,CAACrC,KAAT,CAAeuH,KAD1C;AAEA,QAAMvB,QAAQ,GAAG7K,uBAAuB,CACtCkH,QAAQ,CAACrC,KAAT,CAAegG,QADuB,EAEtC,CAFsC,EAGtCN,YAAY,CAACM,QAHyB,CAAxC;;AAMA,QACEmL,gBAAgB,IAChBtF,WADA,IAEAuF,UAFA,IAGAC,uBAJF,EAKE;AACA;AACD,KAzBmB;AA4BpB;AACA;;;AACA,QAAIhE,gBAAgB,GAAGT,YAAnB,CAAgC,UAAhC,CAAJ,EAAiD;AAC/C;AACD;;AAEDI,IAAAA,UAAU,CAAC,QAAD,EAAW,CAAC3K,QAAD,CAAX,EAAuB,KAAvB,CAAV;;AACA,QAAIA,QAAQ,CAACrC,KAAT,CAAe8G,MAAf,CAAsBzE,QAAtB,MAAoC,KAAxC,EAA+C;AAC7C;AACD;;AAEDA,IAAAA,QAAQ,CAACnD,KAAT,CAAeqD,SAAf,GAA2B,IAA3B;;AAEA,QAAI6K,oBAAoB,EAAxB,EAA4B;AAC1B5D,MAAAA,MAAM,CAACzK,KAAP,CAAauS,UAAb,GAA0B,SAA1B;AACD;;AAEDvE,IAAAA,YAAY;AACZyB,IAAAA,gBAAgB;;AAEhB,QAAI,CAACnM,QAAQ,CAACnD,KAAT,CAAe4M,SAApB,EAA+B;AAC7BtC,MAAAA,MAAM,CAACzK,KAAP,CAAawS,UAAb,GAA0B,MAA1B;AACD,KAlDmB;AAqDpB;;;AACA,QAAInE,oBAAoB,EAAxB,EAA4B;AAAA,mCACHE,0BAA0B,EADvB;AAAA,UACnBjM,GADmB,0BACnBA,GADmB;AAAA,UACd+D,OADc,0BACdA,OADc;;AAE1BxG,MAAAA,qBAAqB,CAAC,CAACyC,GAAD,EAAM+D,OAAN,CAAD,EAAiB,CAAjB,CAArB;AACD;;AAEDgG,IAAAA,aAAa,GAAG,yBAAY;AAC1B,UAAI,CAAC/I,QAAQ,CAACnD,KAAT,CAAeqD,SAAhB,IAA6B0I,mBAAjC,EAAsD;AACpD;AACD;;AAEDA,MAAAA,mBAAmB,GAAG,IAAtB,CAL0B;;AAQ1B,WAAKzB,MAAM,CAACgI,YAAZ;AAEAhI,MAAAA,MAAM,CAACzK,KAAP,CAAawS,UAAb,GAA0BlP,QAAQ,CAACrC,KAAT,CAAesG,cAAzC;;AAEA,UAAI8G,oBAAoB,MAAM/K,QAAQ,CAACrC,KAAT,CAAekF,SAA7C,EAAwD;AAAA,qCAC/BoI,0BAA0B,EADK;AAAA,YAC/CjM,IAD+C,0BAC/CA,GAD+C;AAAA,YAC1C+D,QAD0C,0BAC1CA,OAD0C;;AAEtDxG,QAAAA,qBAAqB,CAAC,CAACyC,IAAD,EAAM+D,QAAN,CAAD,EAAiBY,QAAjB,CAArB;AACA/G,QAAAA,kBAAkB,CAAC,CAACoC,IAAD,EAAM+D,QAAN,CAAD,EAAiB,SAAjB,CAAlB;AACD;;AAEDyI,MAAAA,0BAA0B;AAC1Bf,MAAAA,2BAA2B;AAE3B5P,MAAAA,YAAY,CAACuN,gBAAD,EAAmBpI,QAAnB,CAAZ;AAEAA,MAAAA,QAAQ,CAACnD,KAAT,CAAe4M,SAAf,GAA2B,IAA3B;AACAkB,MAAAA,UAAU,CAAC,SAAD,EAAY,CAAC3K,QAAD,CAAZ,CAAV;;AAEA,UAAIA,QAAQ,CAACrC,KAAT,CAAekF,SAAf,IAA4BkI,oBAAoB,EAApD,EAAwD;AACtDyB,QAAAA,gBAAgB,CAAC7I,QAAD,EAAW,YAAM;AAC/B3D,UAAAA,QAAQ,CAACnD,KAAT,CAAe6M,OAAf,GAAyB,IAAzB;AACAiB,UAAAA,UAAU,CAAC,SAAD,EAAY,CAAC3K,QAAD,CAAZ,CAAV;AACD,SAHe,CAAhB;AAID;AACF,KAhCD;;AAkCAsO,IAAAA,KAAK;AACN;;AAED,WAASxE,IAAT,GAAsB;AACpB;AACA,+CAAa;AACXtI,MAAAA,QAAQ,CAACxB,QAAQ,CAACnD,KAAT,CAAe2M,WAAhB,EAA6B9I,uBAAuB,CAAC,MAAD,CAApD,CAAR;AACD,KAJmB;;;AAOpB,QAAM0O,eAAe,GAAG,CAACpP,QAAQ,CAACnD,KAAT,CAAeqD,SAAxC;AACA,QAAMsJ,WAAW,GAAGxJ,QAAQ,CAACnD,KAAT,CAAe2M,WAAnC;AACA,QAAMuF,UAAU,GAAG,CAAC/O,QAAQ,CAACnD,KAAT,CAAe0M,SAAnC;AACA,QAAM5F,QAAQ,GAAG7K,uBAAuB,CACtCkH,QAAQ,CAACrC,KAAT,CAAegG,QADuB,EAEtC,CAFsC,EAGtCN,YAAY,CAACM,QAHyB,CAAxC;;AAMA,QAAIyL,eAAe,IAAI5F,WAAnB,IAAkCuF,UAAtC,EAAkD;AAChD;AACD;;AAEDpE,IAAAA,UAAU,CAAC,QAAD,EAAW,CAAC3K,QAAD,CAAX,EAAuB,KAAvB,CAAV;;AACA,QAAIA,QAAQ,CAACrC,KAAT,CAAe4G,MAAf,CAAsBvE,QAAtB,MAAoC,KAAxC,EAA+C;AAC7C;AACD;;AAEDA,IAAAA,QAAQ,CAACnD,KAAT,CAAeqD,SAAf,GAA2B,KAA3B;AACAF,IAAAA,QAAQ,CAACnD,KAAT,CAAe6M,OAAf,GAAyB,KAAzB;AACAd,IAAAA,mBAAmB,GAAG,KAAtB;;AAEA,QAAImC,oBAAoB,EAAxB,EAA4B;AAC1B5D,MAAAA,MAAM,CAACzK,KAAP,CAAauS,UAAb,GAA0B,QAA1B;AACD;;AAEDpD,IAAAA,gCAAgC;AAChCG,IAAAA,mBAAmB;AACnBtB,IAAAA,YAAY;;AAEZ,QAAIK,oBAAoB,EAAxB,EAA4B;AAAA,mCACHE,0BAA0B,EADvB;AAAA,UACnBjM,GADmB,0BACnBA,GADmB;AAAA,UACd+D,OADc,0BACdA,OADc;;AAG1B,UAAI/C,QAAQ,CAACrC,KAAT,CAAekF,SAAnB,EAA8B;AAC5BtG,QAAAA,qBAAqB,CAAC,CAACyC,GAAD,EAAM+D,OAAN,CAAD,EAAiBY,QAAjB,CAArB;AACA/G,QAAAA,kBAAkB,CAAC,CAACoC,GAAD,EAAM+D,OAAN,CAAD,EAAiB,QAAjB,CAAlB;AACD;AACF;;AAEDyI,IAAAA,0BAA0B;AAC1Bf,IAAAA,2BAA2B;;AAE3B,QAAIzK,QAAQ,CAACrC,KAAT,CAAekF,SAAnB,EAA8B;AAC5B,UAAIkI,oBAAoB,EAAxB,EAA4B;AAC1BqB,QAAAA,iBAAiB,CAACzI,QAAD,EAAW3D,QAAQ,CAACkK,OAApB,CAAjB;AACD;AACF,KAJD,MAIO;AACLlK,MAAAA,QAAQ,CAACkK,OAAT;AACD;AACF;;AAED,WAASH,qBAAT,CAA+B1M,KAA/B,EAAwD;AACtD;AACA,+CAAa;AACXmE,MAAAA,QAAQ,CACNxB,QAAQ,CAACnD,KAAT,CAAe2M,WADT,EAEN9I,uBAAuB,CAAC,uBAAD,CAFjB,CAAR;AAID;;AAED0I,IAAAA,GAAG,CAAC1J,gBAAJ,CAAqB,WAArB,EAAkCuJ,oBAAlC;AACApO,IAAAA,YAAY,CAACsN,kBAAD,EAAqBc,oBAArB,CAAZ;AACAA,IAAAA,oBAAoB,CAAC5L,KAAD,CAApB;AACD;;AAED,WAAS6M,OAAT,GAAyB;AACvB;AACA,+CAAa;AACX1I,MAAAA,QAAQ,CAACxB,QAAQ,CAACnD,KAAT,CAAe2M,WAAhB,EAA6B9I,uBAAuB,CAAC,SAAD,CAApD,CAAR;AACD;;AAED,QAAIV,QAAQ,CAACnD,KAAT,CAAeqD,SAAnB,EAA8B;AAC5BF,MAAAA,QAAQ,CAAC8J,IAAT;AACD;;AAED,QAAI,CAAC9J,QAAQ,CAACnD,KAAT,CAAe4M,SAApB,EAA+B;AAC7B;AACD;;AAEDiE,IAAAA,qBAAqB,GAdE;AAiBvB;AACA;;AACAL,IAAAA,mBAAmB,GAAG/S,OAAtB,CAA8B,UAACsU,YAAD,EAAkB;AAC9CA,MAAAA,YAAY,CAACzS,MAAb,CAAqB+N,OAArB;AACD,KAFD;;AAIA,QAAI/C,MAAM,CAACoF,UAAX,EAAuB;AACrBpF,MAAAA,MAAM,CAACoF,UAAP,CAAkBvE,WAAlB,CAA8Bb,MAA9B;AACD;;AAEDiB,IAAAA,gBAAgB,GAAGA,gBAAgB,CAAC3N,MAAjB,CAAwB,UAAC4U,CAAD;AAAA,aAAOA,CAAC,KAAKrP,QAAb;AAAA,KAAxB,CAAnB;AAEAA,IAAAA,QAAQ,CAACnD,KAAT,CAAe4M,SAAf,GAA2B,KAA3B;AACAkB,IAAAA,UAAU,CAAC,UAAD,EAAa,CAAC3K,QAAD,CAAb,CAAV;AACD;;AAED,WAASmK,OAAT,GAAyB;AACvB;AACA,+CAAa;AACX3I,MAAAA,QAAQ,CAACxB,QAAQ,CAACnD,KAAT,CAAe2M,WAAhB,EAA6B9I,uBAAuB,CAAC,SAAD,CAApD,CAAR;AACD;;AAED,QAAIV,QAAQ,CAACnD,KAAT,CAAe2M,WAAnB,EAAgC;AAC9B;AACD;;AAEDxJ,IAAAA,QAAQ,CAAC2J,kBAAT;AACA3J,IAAAA,QAAQ,CAACkK,OAAT;AAEA6C,IAAAA,eAAe;AAEf,WAAO3Q,SAAS,CAACD,MAAjB;AAEA6D,IAAAA,QAAQ,CAACnD,KAAT,CAAe2M,WAAf,GAA6B,IAA7B;AAEAmB,IAAAA,UAAU,CAAC,WAAD,EAAc,CAAC3K,QAAD,CAAd,CAAV;AACD;AACF;;AC7lCD,SAASsP,KAAT,CACErN,OADF,EAEEsN,aAFF,EAGyB;AAAA,MADvBA,aACuB;AADvBA,IAAAA,aACuB,GADS,EACT;AAAA;;AACvB,MAAMzK,OAAO,GAAGzB,YAAY,CAACyB,OAAb,CAAqBlK,MAArB,CAA4B2U,aAAa,CAACzK,OAAd,IAAyB,EAArD,CAAhB;AAEA;;AACA,6CAAa;AACX9C,IAAAA,eAAe,CAACC,OAAD,CAAf;AACAuD,IAAAA,aAAa,CAAC+J,aAAD,EAAgBzK,OAAhB,CAAb;AACD;;AAED3E,EAAAA,wBAAwB;AAExB,MAAMuF,WAA2B,qBAAO6J,aAAP;AAAsBzK,IAAAA,OAAO,EAAPA;AAAtB,IAAjC;AAEA,MAAM0K,QAAQ,GAAGnT,kBAAkB,CAAC4F,OAAD,CAAnC;AAEA;;AACA,6CAAa;AACX,QAAMwN,sBAAsB,GAAG3T,SAAS,CAAC4J,WAAW,CAAC3C,OAAb,CAAxC;AACA,QAAM2M,6BAA6B,GAAGF,QAAQ,CAAC/I,MAAT,GAAkB,CAAxD;AACAjF,IAAAA,QAAQ,CACNiO,sBAAsB,IAAIC,6BADpB,EAEN,CACE,oEADF,EAEE,mEAFF,EAGE,mEAHF,EAIE,MAJF,EAKE,qEALF,EAME,kDANF,EAOE,MAPF,EAQE,iCARF,EASE,2CATF,EAUE9O,IAVF,CAUO,GAVP,CAFM,CAAR;AAcD;;AAED,MAAM+O,SAAS,GAAGH,QAAQ,CAAChU,MAAT,CAChB,UAACC,GAAD,EAAMW,SAAN,EAAgC;AAC9B,QAAM4D,QAAQ,GAAG5D,SAAS,IAAIiM,WAAW,CAACjM,SAAD,EAAYsJ,WAAZ,CAAzC;;AAEA,QAAI1F,QAAJ,EAAc;AACZvE,MAAAA,GAAG,CAACV,IAAJ,CAASiF,QAAT;AACD;;AAED,WAAOvE,GAAP;AACD,GATe,EAUhB,EAVgB,CAAlB;AAaA,SAAOK,SAAS,CAACmG,OAAD,CAAT,GAAqB0N,SAAS,CAAC,CAAD,CAA9B,GAAoCA,SAA3C;AACD;;AAEDL,KAAK,CAACjM,YAAN,GAAqBA,YAArB;AACAiM,KAAK,CAAChK,eAAN,GAAwBA,eAAxB;AACAgK,KAAK,CAAClQ,YAAN,GAAqBA,YAArB;AAEA,IAEawQ,OAAgB,GAAG,SAAnBA,OAAmB,QAGL;AAAA,gCAAP,EAAO;AAAA,MAFhBC,2BAEgB,QAFzBC,OAEyB;AAAA,MADzBnM,QACyB,QADzBA,QACyB;;AACzByE,EAAAA,gBAAgB,CAAC9N,OAAjB,CAAyB,UAAC0F,QAAD,EAAc;AACrC,QAAI+P,UAAU,GAAG,KAAjB;;AAEA,QAAIF,2BAAJ,EAAiC;AAC/BE,MAAAA,UAAU,GAAG7T,kBAAkB,CAAC2T,2BAAD,CAAlB,GACT7P,QAAQ,CAAC5D,SAAT,KAAuByT,2BADd,GAET7P,QAAQ,CAACmH,MAAT,KAAqB0I,2BAAD,CAA0C1I,MAFlE;AAGD;;AAED,QAAI,CAAC4I,UAAL,EAAiB;AACf,UAAMC,gBAAgB,GAAGhQ,QAAQ,CAACrC,KAAT,CAAegG,QAAxC;AAEA3D,MAAAA,QAAQ,CAAC4J,QAAT,CAAkB;AAACjG,QAAAA,QAAQ,EAARA;AAAD,OAAlB;AACA3D,MAAAA,QAAQ,CAAC8J,IAAT;;AAEA,UAAI,CAAC9J,QAAQ,CAACnD,KAAT,CAAe2M,WAApB,EAAiC;AAC/BxJ,QAAAA,QAAQ,CAAC4J,QAAT,CAAkB;AAACjG,UAAAA,QAAQ,EAAEqM;AAAX,SAAlB;AACD;AACF;AACF,GAnBD;AAoBD,CAxBM;;ACzDP,IAAMC,eAAgC,GAAG,SAAnCA,eAAmC,CACvCC,cADuC,EAEvCX,aAFuC,EAGpC;AAAA,MADHA,aACG;AADHA,IAAAA,aACG,GADa,EACb;AAAA;;AACH;AACA,6CAAa;AACXzN,IAAAA,SAAS,CACP,CAAC5I,KAAK,CAACC,OAAN,CAAc+W,cAAd,CADM,EAEP,CACE,oEADF,EAEE,uCAFF,EAGE7N,MAAM,CAAC6N,cAAD,CAHR,EAIEtP,IAJF,CAIO,GAJP,CAFO,CAAT;AAQD;;AAED,MAAIuP,iBAAiB,GAAGD,cAAxB;AACA,MAAIE,UAAmC,GAAG,EAA1C;AACA,MAAIjH,aAAJ;AACA,MAAIkH,SAAS,GAAGd,aAAa,CAACc,SAA9B;;AAEA,WAASC,aAAT,GAA+B;AAC7BF,IAAAA,UAAU,GAAGD,iBAAiB,CAAC9F,GAAlB,CAAsB,UAACrK,QAAD;AAAA,aAAcA,QAAQ,CAAC5D,SAAvB;AAAA,KAAtB,CAAb;AACD;;AAED,WAASmU,eAAT,CAAyBhH,SAAzB,EAAmD;AACjD4G,IAAAA,iBAAiB,CAAC7V,OAAlB,CAA0B,UAAC0F,QAAD,EAAc;AACtC,UAAIuJ,SAAJ,EAAe;AACbvJ,QAAAA,QAAQ,CAACgK,MAAT;AACD,OAFD,MAEO;AACLhK,QAAAA,QAAQ,CAACiK,OAAT;AACD;AACF,KAND;AAOD;;AAEDsG,EAAAA,eAAe,CAAC,KAAD,CAAf;AACAD,EAAAA,aAAa;AAEb,MAAME,SAAiB,GAAG;AACxB3W,IAAAA,EADwB,gBACnB;AACH,aAAO;AACLwK,QAAAA,SADK,uBACa;AAChBkM,UAAAA,eAAe,CAAC,IAAD,CAAf;AACD,SAHI;AAIL5L,QAAAA,SAJK,qBAIK3E,QAJL,EAIe3C,KAJf,EAI4B;AAC/B,cAAM0O,MAAM,GAAG1O,KAAK,CAAC8L,aAArB;AACA,cAAMnQ,KAAK,GAAGoX,UAAU,CAAC3W,OAAX,CAAmBsS,MAAnB,CAAd,CAF+B;;AAK/B,cAAIA,MAAM,KAAK5C,aAAf,EAA8B;AAC5B;AACD;;AAEDA,UAAAA,aAAa,GAAG4C,MAAhB;AAEA,cAAM0E,aAAa,GAAG,CAACJ,SAAS,IAAI,EAAd,EACnBzV,MADmB,CACZ,SADY,EAEnBY,MAFmB,CAEZ,UAACC,GAAD,EAAM6K,IAAN,EAAe;AACpB7K,YAAAA,GAAD,CAAa6K,IAAb,IAAqB6J,iBAAiB,CAACnX,KAAD,CAAjB,CAAyB2E,KAAzB,CAA+B2I,IAA/B,CAArB;AACA,mBAAO7K,GAAP;AACD,WALmB,EAKjB,EALiB,CAAtB;AAOAuE,UAAAA,QAAQ,CAAC4J,QAAT,mBACK6G,aADL;AAEE7M,YAAAA,sBAAsB,EAAE;AAAA,qBAAMmI,MAAM,CAACuB,qBAAP,EAAN;AAAA;AAF1B;AAID;AA1BI,OAAP;AA4BD;AA9BuB,GAA1B;AAiCA,MAAMtN,QAAQ,GAAGsP,KAAK,CAAC3T,GAAG,EAAJ,oBACjBxB,gBAAgB,CAACoV,aAAD,EAAgB,CAAC,WAAD,CAAhB,CADC;AAEpBzK,IAAAA,OAAO,GAAG0L,SAAH,SAAkBjB,aAAa,CAACzK,OAAd,IAAyB,EAA3C,CAFa;AAGpBM,IAAAA,aAAa,EAAEgL;AAHK,KAAtB;AAMA,MAAMM,gBAAgB,GAAG1Q,QAAQ,CAAC4J,QAAlC;;AAEA5J,EAAAA,QAAQ,CAAC4J,QAAT,GAAoB,UAACjM,KAAD,EAAiB;AACnC0S,IAAAA,SAAS,GAAG1S,KAAK,CAAC0S,SAAN,IAAmBA,SAA/B;AACAK,IAAAA,gBAAgB,CAAC/S,KAAD,CAAhB;AACD,GAHD;;AAKAqC,EAAAA,QAAQ,CAAC2Q,YAAT,GAAwB,UAACC,aAAD,EAAyB;AAC/CL,IAAAA,eAAe,CAAC,IAAD,CAAf;AAEAJ,IAAAA,iBAAiB,GAAGS,aAApB;AAEAL,IAAAA,eAAe,CAAC,KAAD,CAAf;AACAD,IAAAA,aAAa;AAEbtQ,IAAAA,QAAQ,CAAC4J,QAAT,CAAkB;AAACxE,MAAAA,aAAa,EAAEgL;AAAhB,KAAlB;AACD,GATD;;AAWA,SAAOpQ,QAAP;AACD,CAhGD;;ACLA,IAAM6Q,mBAAmB,GAAG;AAC1BC,EAAAA,SAAS,EAAE,YADe;AAE1BC,EAAAA,OAAO,EAAE,OAFiB;AAG1BC,EAAAA,KAAK,EAAE;AAHmB,CAA5B;AAMA;;;;;AAIA,SAASC,QAAT,CACEhP,OADF,EAEEtE,KAFF,EAGyB;AACvB;AACA,6CAAa;AACXmE,IAAAA,SAAS,CACP,EAAEnE,KAAK,IAAIA,KAAK,CAACoO,MAAjB,CADO,EAEP,CACE,4EADF,EAEE,kDAFF,EAGEnL,IAHF,CAGO,GAHP,CAFO,CAAT;AAOD;;AAED,MAAIoI,SAA2B,GAAG,EAAlC;AACA,MAAIkI,mBAA+B,GAAG,EAAtC;AAbuB,MAehBnF,MAfgB,GAeNpO,KAfM,CAehBoO,MAfgB;AAiBvB,MAAMoF,WAAW,GAAGhX,gBAAgB,CAACwD,KAAD,EAAQ,CAAC,QAAD,CAAR,CAApC;AACA,MAAMyT,WAAW,qBAAOD,WAAP;AAAoBhM,IAAAA,OAAO,EAAE,QAA7B;AAAuCD,IAAAA,KAAK,EAAE;AAA9C,IAAjB;AACA,MAAMmM,UAAU,qBAAOF,WAAP;AAAoBlM,IAAAA,YAAY,EAAE;AAAlC,IAAhB;AAEA,MAAMqM,WAAW,GAAGhC,KAAK,CAACrN,OAAD,EAAUmP,WAAV,CAAzB;AACA,MAAMG,qBAAqB,GAAG5W,gBAAgB,CAAC2W,WAAD,CAA9C;;AAEA,WAAS3M,SAAT,CAAmBtH,KAAnB,EAAuC;AACrC,QAAI,CAACA,KAAK,CAAC0O,MAAX,EAAmB;AACjB;AACD;;AAED,QAAMyF,UAAU,GAAInU,KAAK,CAAC0O,MAAP,CAA0B0F,OAA1B,CAAkC1F,MAAlC,CAAnB;;AAEA,QAAI,CAACyF,UAAL,EAAiB;AACf;AACD,KAToC;AAYrC;AACA;AACA;;;AACA,QAAMrM,OAAO,GACXqM,UAAU,CAACxL,YAAX,CAAwB,oBAAxB,KACArI,KAAK,CAACwH,OADN,IAEA9B,YAAY,CAAC8B,OAHf,CAfqC;;AAqBrC,QAAIqM,UAAU,CAACrV,MAAf,EAAuB;AACrB;AACD;;AAED,QAAIkB,KAAK,CAAC/D,IAAN,KAAe,YAAf,IAA+B,OAAO+X,UAAU,CAACnM,KAAlB,KAA4B,SAA/D,EAA0E;AACxE;AACD;;AAED,QACE7H,KAAK,CAAC/D,IAAN,KAAe,YAAf,IACA6L,OAAO,CAAC1L,OAAR,CAAiBoX,mBAAD,CAA6BxT,KAAK,CAAC/D,IAAnC,CAAhB,CAFF,EAGE;AACA;AACD;;AAED,QAAM0G,QAAQ,GAAGsP,KAAK,CAACkC,UAAD,EAAaH,UAAb,CAAtB;;AAEA,QAAIrR,QAAJ,EAAc;AACZkR,MAAAA,mBAAmB,GAAGA,mBAAmB,CAACtW,MAApB,CAA2BoF,QAA3B,CAAtB;AACD;AACF;;AAED,WAASyM,EAAT,CACEjF,IADF,EAEEkF,SAFF,EAGEC,OAHF,EAIEC,OAJF,EAKQ;AAAA,QADNA,OACM;AADNA,MAAAA,OACM,GADsB,KACtB;AAAA;;AACNpF,IAAAA,IAAI,CAAC9H,gBAAL,CAAsBgN,SAAtB,EAAiCC,OAAjC,EAA0CC,OAA1C;AACA5D,IAAAA,SAAS,CAACjO,IAAV,CAAe;AAACyM,MAAAA,IAAI,EAAJA,IAAD;AAAOkF,MAAAA,SAAS,EAATA,SAAP;AAAkBC,MAAAA,OAAO,EAAPA,OAAlB;AAA2BC,MAAAA,OAAO,EAAPA;AAA3B,KAAf;AACD;;AAED,WAAS8E,iBAAT,CAA2B1R,QAA3B,EAAqD;AAAA,QAC5C5D,SAD4C,GAC/B4D,QAD+B,CAC5C5D,SAD4C;AAGnDqQ,IAAAA,EAAE,CAACrQ,SAAD,EAAY,YAAZ,EAA0BuI,SAA1B,CAAF;AACA8H,IAAAA,EAAE,CAACrQ,SAAD,EAAY,WAAZ,EAAyBuI,SAAzB,CAAF;AACA8H,IAAAA,EAAE,CAACrQ,SAAD,EAAY,SAAZ,EAAuBuI,SAAvB,CAAF;AACA8H,IAAAA,EAAE,CAACrQ,SAAD,EAAY,OAAZ,EAAqBuI,SAArB,CAAF;AACD;;AAED,WAASgN,oBAAT,GAAsC;AACpC3I,IAAAA,SAAS,CAAC1O,OAAV,CAAkB,gBAAyD;AAAA,UAAvDkN,IAAuD,QAAvDA,IAAuD;AAAA,UAAjDkF,SAAiD,QAAjDA,SAAiD;AAAA,UAAtCC,OAAsC,QAAtCA,OAAsC;AAAA,UAA7BC,OAA6B,QAA7BA,OAA6B;AACzEpF,MAAAA,IAAI,CAAC3H,mBAAL,CAAyB6M,SAAzB,EAAoCC,OAApC,EAA6CC,OAA7C;AACD,KAFD;AAGA5D,IAAAA,SAAS,GAAG,EAAZ;AACD;;AAED,WAAS4I,cAAT,CAAwB5R,QAAxB,EAAkD;AAChD,QAAM6R,eAAe,GAAG7R,QAAQ,CAACmK,OAAjC;;AACAnK,IAAAA,QAAQ,CAACmK,OAAT,GAAmB,UAAC2H,2BAAD,EAA8C;AAAA,UAA7CA,2BAA6C;AAA7CA,QAAAA,2BAA6C,GAAf,IAAe;AAAA;;AAC/D,UAAIA,2BAAJ,EAAiC;AAC/BZ,QAAAA,mBAAmB,CAAC5W,OAApB,CAA4B,UAAC0F,QAAD,EAAc;AACxCA,UAAAA,QAAQ,CAACmK,OAAT;AACD,SAFD;AAGD;;AAED+G,MAAAA,mBAAmB,GAAG,EAAtB;AAEAS,MAAAA,oBAAoB;AACpBE,MAAAA,eAAe;AAChB,KAXD;;AAaAH,IAAAA,iBAAiB,CAAC1R,QAAD,CAAjB;AACD;;AAEDuR,EAAAA,qBAAqB,CAACjX,OAAtB,CAA8BsX,cAA9B;AAEA,SAAON,WAAP;AACD;;AChID,IAAM/O,WAAwB,GAAG;AAC/BqD,EAAAA,IAAI,EAAE,aADyB;AAE/B3M,EAAAA,YAAY,EAAE,KAFiB;AAG/BY,EAAAA,EAH+B,cAG5BmG,QAH4B,EAGlB;AAAA;;AACX;AACA,QAAI,2BAACA,QAAQ,CAACrC,KAAT,CAAeqH,MAAhB,qBAAC,sBAAuBiD,OAAxB,CAAJ,EAAqC;AACnC,iDAAa;AACXnG,QAAAA,SAAS,CACP9B,QAAQ,CAACrC,KAAT,CAAe4E,WADR,EAEP,gEAFO,CAAT;AAID;;AAED,aAAO,EAAP;AACD;;AAXU,uBAaY2E,WAAW,CAAClH,QAAQ,CAACmH,MAAV,CAbvB;AAAA,QAaJnI,GAbI,gBAaJA,GAbI;AAAA,QAaC+D,OAbD,gBAaCA,OAbD;;AAeX,QAAM4E,QAAQ,GAAG3H,QAAQ,CAACrC,KAAT,CAAe4E,WAAf,GACbwP,qBAAqB,EADR,GAEb,IAFJ;AAIA,WAAO;AACL3N,MAAAA,QADK,sBACY;AACf,YAAIuD,QAAJ,EAAc;AACZ3I,UAAAA,GAAG,CAACgT,YAAJ,CAAiBrK,QAAjB,EAA2B3I,GAAG,CAACoI,iBAA/B;AACApI,UAAAA,GAAG,CAAClC,YAAJ,CAAiB,kBAAjB,EAAqC,EAArC;AACAkC,UAAAA,GAAG,CAACtC,KAAJ,CAAUuV,QAAV,GAAqB,QAArB;AAEAjS,UAAAA,QAAQ,CAAC4J,QAAT,CAAkB;AAAC9G,YAAAA,KAAK,EAAE,KAAR;AAAeD,YAAAA,SAAS,EAAE;AAA1B,WAAlB;AACD;AACF,OATI;AAUL2B,MAAAA,OAVK,qBAUW;AACd,YAAImD,QAAJ,EAAc;AAAA,cACLhL,kBADK,GACiBqC,GAAG,CAACtC,KADrB,CACLC,kBADK;AAEZ,cAAMgH,QAAQ,GAAGuO,MAAM,CAACvV,kBAAkB,CAACqE,OAAnB,CAA2B,IAA3B,EAAiC,EAAjC,CAAD,CAAvB,CAFY;AAKZ;AACA;;AACA+B,UAAAA,OAAO,CAACrG,KAAR,CAAcyV,eAAd,GAAmCC,IAAI,CAACC,KAAL,CAAW1O,QAAQ,GAAG,EAAtB,CAAnC;AAEAgE,UAAAA,QAAQ,CAACjL,KAAT,CAAeC,kBAAf,GAAoCA,kBAApC;AACAC,UAAAA,kBAAkB,CAAC,CAAC+K,QAAD,CAAD,EAAa,SAAb,CAAlB;AACD;AACF,OAvBI;AAwBLlD,MAAAA,MAxBK,oBAwBU;AACb,YAAIkD,QAAJ,EAAc;AACZA,UAAAA,QAAQ,CAACjL,KAAT,CAAeC,kBAAf,GAAoC,KAApC;AACD;AACF,OA5BI;AA6BL4H,MAAAA,MA7BK,oBA6BU;AACb,YAAIoD,QAAJ,EAAc;AACZ/K,UAAAA,kBAAkB,CAAC,CAAC+K,QAAD,CAAD,EAAa,QAAb,CAAlB;AACD;AACF;AAjCI,KAAP;AAmCD;AAzD8B,CAAjC;AA4DA;AAEA,SAASoK,qBAAT,GAAiD;AAC/C,MAAMpK,QAAQ,GAAGhM,GAAG,EAApB;AACAgM,EAAAA,QAAQ,CAACb,SAAT,GAAqB1O,cAArB;AACAwE,EAAAA,kBAAkB,CAAC,CAAC+K,QAAD,CAAD,EAAa,QAAb,CAAlB;AACA,SAAOA,QAAP;AACD;;ACtED,IAAI2K,WAAW,GAAG;AAAChV,EAAAA,OAAO,EAAE,CAAV;AAAaC,EAAAA,OAAO,EAAE;AAAtB,CAAlB;AACA,IAAIgV,eAA2D,GAAG,EAAlE;;AAEA,SAASC,gBAAT,OAAgE;AAAA,MAArClV,OAAqC,QAArCA,OAAqC;AAAA,MAA5BC,OAA4B,QAA5BA,OAA4B;AAC9D+U,EAAAA,WAAW,GAAG;AAAChV,IAAAA,OAAO,EAAPA,OAAD;AAAUC,IAAAA,OAAO,EAAPA;AAAV,GAAd;AACD;;AAED,SAASkV,sBAAT,CAAgCrJ,GAAhC,EAAqD;AACnDA,EAAAA,GAAG,CAAC1J,gBAAJ,CAAqB,WAArB,EAAkC8S,gBAAlC;AACD;;AAED,SAASE,yBAAT,CAAmCtJ,GAAnC,EAAwD;AACtDA,EAAAA,GAAG,CAACvJ,mBAAJ,CAAwB,WAAxB,EAAqC2S,gBAArC;AACD;;AAED,IAAMhQ,YAA0B,GAAG;AACjCoD,EAAAA,IAAI,EAAE,cAD2B;AAEjC3M,EAAAA,YAAY,EAAE,KAFmB;AAGjCY,EAAAA,EAHiC,cAG9BmG,QAH8B,EAGpB;AACX,QAAM5D,SAAS,GAAG4D,QAAQ,CAAC5D,SAA3B;AACA,QAAMgN,GAAG,GAAGrM,gBAAgB,CAACiD,QAAQ,CAACrC,KAAT,CAAeyH,aAAf,IAAgChJ,SAAjC,CAA5B;AAEA,QAAIuW,gBAAgB,GAAG,KAAvB;AACA,QAAIC,aAAa,GAAG,KAApB;AACA,QAAIC,WAAW,GAAG,IAAlB;AACA,QAAIhL,SAAS,GAAG7H,QAAQ,CAACrC,KAAzB;;AAEA,aAASmV,oBAAT,GAAyC;AACvC,aACE9S,QAAQ,CAACrC,KAAT,CAAe6E,YAAf,KAAgC,SAAhC,IAA6CxC,QAAQ,CAACnD,KAAT,CAAeqD,SAD9D;AAGD;;AAED,aAAS6S,WAAT,GAA6B;AAC3B3J,MAAAA,GAAG,CAAC1J,gBAAJ,CAAqB,WAArB,EAAkCwJ,WAAlC;AACD;;AAED,aAAS8J,cAAT,GAAgC;AAC9B5J,MAAAA,GAAG,CAACvJ,mBAAJ,CAAwB,WAAxB,EAAqCqJ,WAArC;AACD;;AAED,aAAS+J,2BAAT,GAA6C;AAC3CN,MAAAA,gBAAgB,GAAG,IAAnB;AACA3S,MAAAA,QAAQ,CAAC4J,QAAT,CAAkB;AAAChG,QAAAA,sBAAsB,EAAE;AAAzB,OAAlB;AACA+O,MAAAA,gBAAgB,GAAG,KAAnB;AACD;;AAED,aAASzJ,WAAT,CAAqB7L,KAArB,EAA8C;AAC5C;AACA;AACA,UAAM6V,qBAAqB,GAAG7V,KAAK,CAAC0O,MAAN,GAC1B3P,SAAS,CAACsL,QAAV,CAAmBrK,KAAK,CAAC0O,MAAzB,CAD0B,GAE1B,IAFJ;AAH4C,UAMrCvJ,YANqC,GAMrBxC,QAAQ,CAACrC,KANY,CAMrC6E,YANqC;AAAA,UAOrClF,OAPqC,GAOjBD,KAPiB,CAOrCC,OAPqC;AAAA,UAO5BC,OAP4B,GAOjBF,KAPiB,CAO5BE,OAP4B;AAS5C,UAAM4V,IAAI,GAAG/W,SAAS,CAACkR,qBAAV,EAAb;AACA,UAAM8F,SAAS,GAAG9V,OAAO,GAAG6V,IAAI,CAAC5U,IAAjC;AACA,UAAM8U,SAAS,GAAG9V,OAAO,GAAG4V,IAAI,CAACjV,GAAjC;;AAEA,UAAIgV,qBAAqB,IAAI,CAAClT,QAAQ,CAACrC,KAAT,CAAeoG,WAA7C,EAA0D;AACxD/D,QAAAA,QAAQ,CAAC4J,QAAT,CAAkB;AAChBhG,UAAAA,sBADgB,oCACS;AACvB,gBAAMuP,IAAI,GAAG/W,SAAS,CAACkR,qBAAV,EAAb;AAEA,gBAAI9O,CAAC,GAAGlB,OAAR;AACA,gBAAIa,CAAC,GAAGZ,OAAR;;AAEA,gBAAIiF,YAAY,KAAK,SAArB,EAAgC;AAC9BhE,cAAAA,CAAC,GAAG2U,IAAI,CAAC5U,IAAL,GAAY6U,SAAhB;AACAjV,cAAAA,CAAC,GAAGgV,IAAI,CAACjV,GAAL,GAAWmV,SAAf;AACD;;AAED,gBAAMnV,GAAG,GAAGsE,YAAY,KAAK,YAAjB,GAAgC2Q,IAAI,CAACjV,GAArC,GAA2CC,CAAvD;AACA,gBAAMO,KAAK,GAAG8D,YAAY,KAAK,UAAjB,GAA8B2Q,IAAI,CAACzU,KAAnC,GAA2CF,CAAzD;AACA,gBAAMH,MAAM,GAAGmE,YAAY,KAAK,YAAjB,GAAgC2Q,IAAI,CAAC9U,MAArC,GAA8CF,CAA7D;AACA,gBAAMI,IAAI,GAAGiE,YAAY,KAAK,UAAjB,GAA8B2Q,IAAI,CAAC5U,IAAnC,GAA0CC,CAAvD;AAEA,mBAAO;AACL8U,cAAAA,KAAK,EAAE5U,KAAK,GAAGH,IADV;AAELgV,cAAAA,MAAM,EAAElV,MAAM,GAAGH,GAFZ;AAGLA,cAAAA,GAAG,EAAHA,GAHK;AAILQ,cAAAA,KAAK,EAALA,KAJK;AAKLL,cAAAA,MAAM,EAANA,MALK;AAMLE,cAAAA,IAAI,EAAJA;AANK,aAAP;AAQD;AAzBe,SAAlB;AA2BD;AACF;;AAED,aAASiV,MAAT,GAAwB;AACtB,UAAIxT,QAAQ,CAACrC,KAAT,CAAe6E,YAAnB,EAAiC;AAC/B+P,QAAAA,eAAe,CAACxX,IAAhB,CAAqB;AAACiF,UAAAA,QAAQ,EAARA,QAAD;AAAWoJ,UAAAA,GAAG,EAAHA;AAAX,SAArB;AACAqJ,QAAAA,sBAAsB,CAACrJ,GAAD,CAAtB;AACD;AACF;;AAED,aAASe,OAAT,GAAyB;AACvBoI,MAAAA,eAAe,GAAGA,eAAe,CAAC9X,MAAhB,CAChB,UAACgZ,IAAD;AAAA,eAAUA,IAAI,CAACzT,QAAL,KAAkBA,QAA5B;AAAA,OADgB,CAAlB;;AAIA,UAAIuS,eAAe,CAAC9X,MAAhB,CAAuB,UAACgZ,IAAD;AAAA,eAAUA,IAAI,CAACrK,GAAL,KAAaA,GAAvB;AAAA,OAAvB,EAAmD3C,MAAnD,KAA8D,CAAlE,EAAqE;AACnEiM,QAAAA,yBAAyB,CAACtJ,GAAD,CAAzB;AACD;AACF;;AAED,WAAO;AACLhF,MAAAA,QAAQ,EAAEoP,MADL;AAELnP,MAAAA,SAAS,EAAE8F,OAFN;AAGLhG,MAAAA,cAHK,4BAGkB;AACrB0D,QAAAA,SAAS,GAAG7H,QAAQ,CAACrC,KAArB;AACD,OALI;AAMLuG,MAAAA,aANK,yBAMSwP,CANT,SAMkC;AAAA,YAArBlR,YAAqB,SAArBA,YAAqB;;AACrC,YAAImQ,gBAAJ,EAAsB;AACpB;AACD;;AAED,YACEnQ,YAAY,KAAK9G,SAAjB,IACAmM,SAAS,CAACrF,YAAV,KAA2BA,YAF7B,EAGE;AACA2H,UAAAA,OAAO;;AAEP,cAAI3H,YAAJ,EAAkB;AAChBgR,YAAAA,MAAM;;AAEN,gBACExT,QAAQ,CAACnD,KAAT,CAAe4M,SAAf,IACA,CAACmJ,aADD,IAEA,CAACE,oBAAoB,EAHvB,EAIE;AACAC,cAAAA,WAAW;AACZ;AACF,WAVD,MAUO;AACLC,YAAAA,cAAc;AACdC,YAAAA,2BAA2B;AAC5B;AACF;AACF,OAhCI;AAiCLzO,MAAAA,OAjCK,qBAiCW;AACd,YAAIxE,QAAQ,CAACrC,KAAT,CAAe6E,YAAnB,EAAiC;AAC/B,cAAIqQ,WAAJ,EAAiB;AACf3J,YAAAA,WAAW,CAACoJ,WAAD,CAAX;AACAO,YAAAA,WAAW,GAAG,KAAd;AACD;;AAED,cAAI,CAACD,aAAD,IAAkB,CAACE,oBAAoB,EAA3C,EAA+C;AAC7CC,YAAAA,WAAW;AACZ;AACF;AACF,OA5CI;AA6CLpO,MAAAA,SA7CK,qBA6CK+O,CA7CL,SA6CsB;AAAA,YAAbpa,IAAa,SAAbA,IAAa;AACzBsZ,QAAAA,aAAa,GAAGtZ,IAAI,KAAK,OAAzB;AACD,OA/CI;AAgDLgL,MAAAA,QAhDK,sBAgDY;AACf,YAAItE,QAAQ,CAACrC,KAAT,CAAe6E,YAAnB,EAAiC;AAC/ByQ,UAAAA,2BAA2B;AAC3BD,UAAAA,cAAc;AACdH,UAAAA,WAAW,GAAG,IAAd;AACD;AACF;AAtDI,KAAP;AAwDD;AArJgC,CAAnC;;ACbA,SAASc,QAAT,CAAkBhW,KAAlB,EAAgCiW,QAAhC,EAA8E;AAAA;;AAC5E,SAAO;AACL7O,IAAAA,aAAa,oBACRpH,KAAK,CAACoH,aADE;AAEXmJ,MAAAA,SAAS,YACJ,CAAC,yBAAAvQ,KAAK,CAACoH,aAAN,0CAAqBmJ,SAArB,KAAkC,EAAnC,EAAuCzT,MAAvC,CACD;AAAA,YAAEmL,IAAF,QAAEA,IAAF;AAAA,eAAYA,IAAI,KAAKgO,QAAQ,CAAChO,IAA9B;AAAA,OADC,CADI,GAIPgO,QAJO;AAFE;AADR,GAAP;AAWD;;AAED,IAAMnR,iBAAoC,GAAG;AAC3CmD,EAAAA,IAAI,EAAE,mBADqC;AAE3C3M,EAAAA,YAAY,EAAE,KAF6B;AAG3CY,EAAAA,EAH2C,cAGxCmG,QAHwC,EAG9B;AAAA,QACJ5D,SADI,GACS4D,QADT,CACJ5D,SADI;;AAGX,aAASmN,SAAT,GAA8B;AAC5B,aAAO,CAAC,CAACvJ,QAAQ,CAACrC,KAAT,CAAe8E,iBAAxB;AACD;;AAED,QAAItH,SAAJ;AACA,QAAI0Y,eAAe,GAAG,CAAC,CAAvB;AACA,QAAIlB,gBAAgB,GAAG,KAAvB;AAEA,QAAMiB,QAAgD,GAAG;AACvDhO,MAAAA,IAAI,EAAE,wBADiD;AAEvDkI,MAAAA,OAAO,EAAE,IAF8C;AAGvDC,MAAAA,KAAK,EAAE,YAHgD;AAIvDlU,MAAAA,EAJuD,qBAI3C;AAAA,YAARgD,KAAQ,SAARA,KAAQ;;AACV,YAAI0M,SAAS,EAAb,EAAiB;AACf,cAAIpO,SAAS,KAAK0B,KAAK,CAAC1B,SAAxB,EAAmC;AACjC6E,YAAAA,QAAQ,CAAC4J,QAAT,CAAkB;AAChBhG,cAAAA,sBAAsB,EAAE;AAAA,uBACtBA,uBAAsB,CAAC/G,KAAK,CAAC1B,SAAP,CADA;AAAA;AADR,aAAlB;AAID;;AAEDA,UAAAA,SAAS,GAAG0B,KAAK,CAAC1B,SAAlB;AACD;AACF;AAfsD,KAAzD;;AAkBA,aAASyI,uBAAT,CAAgCzI,SAAhC,EAAkE;AAChE,aAAO2Y,2BAA2B,CAChC5Y,gBAAgB,CAACC,SAAD,CADgB,EAEhCiB,SAAS,CAACkR,qBAAV,EAFgC,EAGhClS,SAAS,CAACgB,SAAS,CAAC2X,cAAV,EAAD,CAHuB,EAIhCF,eAJgC,CAAlC;AAMD;;AAED,aAASG,gBAAT,CAA0BzO,YAA1B,EAA8D;AAC5DoN,MAAAA,gBAAgB,GAAG,IAAnB;AACA3S,MAAAA,QAAQ,CAAC4J,QAAT,CAAkBrE,YAAlB;AACAoN,MAAAA,gBAAgB,GAAG,KAAnB;AACD;;AAED,aAASsB,WAAT,GAA6B;AAC3B,UAAI,CAACtB,gBAAL,EAAuB;AACrBqB,QAAAA,gBAAgB,CAACL,QAAQ,CAAC3T,QAAQ,CAACrC,KAAV,EAAiBiW,QAAjB,CAAT,CAAhB;AACD;AACF;;AAED,WAAO;AACLxP,MAAAA,QAAQ,EAAE6P,WADL;AAEL/P,MAAAA,aAAa,EAAE+P,WAFV;AAGLtP,MAAAA,SAHK,qBAGK+O,CAHL,EAGQrW,KAHR,EAGqB;AACxB,YAAIpB,YAAY,CAACoB,KAAD,CAAhB,EAAyB;AACvB,cAAM6W,KAAK,GAAG9Y,SAAS,CAAC4E,QAAQ,CAAC5D,SAAT,CAAmB2X,cAAnB,EAAD,CAAvB;AACA,cAAMI,UAAU,GAAGD,KAAK,CAAC3M,IAAN,CACjB,UAAC4L,IAAD;AAAA,mBACEA,IAAI,CAAC5U,IAAL,GAAY,CAAZ,IAAiBlB,KAAK,CAACC,OAAvB,IACA6V,IAAI,CAACzU,KAAL,GAAa,CAAb,IAAkBrB,KAAK,CAACC,OADxB,IAEA6V,IAAI,CAACjV,GAAL,GAAW,CAAX,IAAgBb,KAAK,CAACE,OAFtB,IAGA4V,IAAI,CAAC9U,MAAL,GAAc,CAAd,IAAmBhB,KAAK,CAACE,OAJ3B;AAAA,WADiB,CAAnB;AAQAsW,UAAAA,eAAe,GAAGK,KAAK,CAACza,OAAN,CAAc0a,UAAd,CAAlB;AACD;AACF,OAhBI;AAiBLvP,MAAAA,WAjBK,yBAiBe;AAClBiP,QAAAA,eAAe,GAAG,CAAC,CAAnB;AACD;AAnBI,KAAP;AAqBD;AA1E0C,CAA7C;AA6EA,AAEO,SAASC,2BAAT,CACLM,oBADK,EAELC,YAFK,EAGLC,WAHK,EAILT,eAJK,EAKO;AACZ;AACA,MAAIS,WAAW,CAAC7N,MAAZ,GAAqB,CAArB,IAA0B2N,oBAAoB,KAAK,IAAvD,EAA6D;AAC3D,WAAOC,YAAP;AACD,GAJW;;;AAOZ,MACEC,WAAW,CAAC7N,MAAZ,KAAuB,CAAvB,IACAoN,eAAe,IAAI,CADnB,IAEAS,WAAW,CAAC,CAAD,CAAX,CAAe/V,IAAf,GAAsB+V,WAAW,CAAC,CAAD,CAAX,CAAe5V,KAHvC,EAIE;AACA,WAAO4V,WAAW,CAACT,eAAD,CAAX,IAAgCQ,YAAvC;AACD;;AAED,UAAQD,oBAAR;AACE,SAAK,KAAL;AACA,SAAK,QAAL;AAAe;AACb,YAAMG,SAAS,GAAGD,WAAW,CAAC,CAAD,CAA7B;AACA,YAAME,QAAQ,GAAGF,WAAW,CAACA,WAAW,CAAC7N,MAAZ,GAAqB,CAAtB,CAA5B;AACA,YAAMgO,KAAK,GAAGL,oBAAoB,KAAK,KAAvC;AAEA,YAAMlW,GAAG,GAAGqW,SAAS,CAACrW,GAAtB;AACA,YAAMG,MAAM,GAAGmW,QAAQ,CAACnW,MAAxB;AACA,YAAME,IAAI,GAAGkW,KAAK,GAAGF,SAAS,CAAChW,IAAb,GAAoBiW,QAAQ,CAACjW,IAA/C;AACA,YAAMG,KAAK,GAAG+V,KAAK,GAAGF,SAAS,CAAC7V,KAAb,GAAqB8V,QAAQ,CAAC9V,KAAjD;AACA,YAAM4U,KAAK,GAAG5U,KAAK,GAAGH,IAAtB;AACA,YAAMgV,MAAM,GAAGlV,MAAM,GAAGH,GAAxB;AAEA,eAAO;AAACA,UAAAA,GAAG,EAAHA,GAAD;AAAMG,UAAAA,MAAM,EAANA,MAAN;AAAcE,UAAAA,IAAI,EAAJA,IAAd;AAAoBG,UAAAA,KAAK,EAALA,KAApB;AAA2B4U,UAAAA,KAAK,EAALA,KAA3B;AAAkCC,UAAAA,MAAM,EAANA;AAAlC,SAAP;AACD;;AACD,SAAK,MAAL;AACA,SAAK,OAAL;AAAc;AACZ,YAAMmB,OAAO,GAAGtC,IAAI,CAACuC,GAAL,OAAAvC,IAAI,EAAQkC,WAAW,CAACjK,GAAZ,CAAgB,UAAC6J,KAAD;AAAA,iBAAWA,KAAK,CAAC3V,IAAjB;AAAA,SAAhB,CAAR,CAApB;AACA,YAAMqW,QAAQ,GAAGxC,IAAI,CAACyC,GAAL,OAAAzC,IAAI,EAAQkC,WAAW,CAACjK,GAAZ,CAAgB,UAAC6J,KAAD;AAAA,iBAAWA,KAAK,CAACxV,KAAjB;AAAA,SAAhB,CAAR,CAArB;AACA,YAAMoW,YAAY,GAAGR,WAAW,CAAC7Z,MAAZ,CAAmB,UAAC0Y,IAAD;AAAA,iBACtCiB,oBAAoB,KAAK,MAAzB,GACIjB,IAAI,CAAC5U,IAAL,KAAcmW,OADlB,GAEIvB,IAAI,CAACzU,KAAL,KAAekW,QAHmB;AAAA,SAAnB,CAArB;AAMA,YAAM1W,IAAG,GAAG4W,YAAY,CAAC,CAAD,CAAZ,CAAgB5W,GAA5B;AACA,YAAMG,OAAM,GAAGyW,YAAY,CAACA,YAAY,CAACrO,MAAb,GAAsB,CAAvB,CAAZ,CAAsCpI,MAArD;AACA,YAAME,KAAI,GAAGmW,OAAb;AACA,YAAMhW,MAAK,GAAGkW,QAAd;;AACA,YAAMtB,MAAK,GAAG5U,MAAK,GAAGH,KAAtB;;AACA,YAAMgV,OAAM,GAAGlV,OAAM,GAAGH,IAAxB;;AAEA,eAAO;AAACA,UAAAA,GAAG,EAAHA,IAAD;AAAMG,UAAAA,MAAM,EAANA,OAAN;AAAcE,UAAAA,IAAI,EAAJA,KAAd;AAAoBG,UAAAA,KAAK,EAALA,MAApB;AAA2B4U,UAAAA,KAAK,EAALA,MAA3B;AAAkCC,UAAAA,MAAM,EAANA;AAAlC,SAAP;AACD;;AACD;AAAS;AACP,eAAOc,YAAP;AACD;AArCH;AAuCD;;AC1JD,IAAM3R,MAAc,GAAG;AACrBkD,EAAAA,IAAI,EAAE,QADe;AAErB3M,EAAAA,YAAY,EAAE,KAFO;AAGrBY,EAAAA,EAHqB,cAGlBmG,QAHkB,EAGR;AAAA,QACJ5D,SADI,GACiB4D,QADjB,CACJ5D,SADI;AAAA,QACO+K,MADP,GACiBnH,QADjB,CACOmH,MADP;;AAGX,aAAS4N,YAAT,GAA2D;AACzD,aAAO/U,QAAQ,CAACsJ,cAAT,GACHtJ,QAAQ,CAACsJ,cAAT,CAAwBzM,KAAxB,CAA8B2S,QAA9B,CAAuCpT,SADpC,GAEHA,SAFJ;AAGD;;AAED,aAAS4Y,WAAT,CAAqBjc,KAArB,EAA6D;AAC3D,aAAOiH,QAAQ,CAACrC,KAAT,CAAe+E,MAAf,KAA0B,IAA1B,IAAkC1C,QAAQ,CAACrC,KAAT,CAAe+E,MAAf,KAA0B3J,KAAnE;AACD;;AAED,QAAIkc,WAA8B,GAAG,IAArC;AACA,QAAIC,WAA8B,GAAG,IAArC;;AAEA,aAASC,cAAT,GAAgC;AAC9B,UAAMC,cAAc,GAAGJ,WAAW,CAAC,WAAD,CAAX,GACnBD,YAAY,GAAGzH,qBAAf,EADmB,GAEnB,IAFJ;AAGA,UAAM+H,cAAc,GAAGL,WAAW,CAAC,QAAD,CAAX,GACnB7N,MAAM,CAACmG,qBAAP,EADmB,GAEnB,IAFJ;;AAIA,UACG8H,cAAc,IAAIE,iBAAiB,CAACL,WAAD,EAAcG,cAAd,CAApC,IACCC,cAAc,IAAIC,iBAAiB,CAACJ,WAAD,EAAcG,cAAd,CAFtC,EAGE;AACA,YAAIrV,QAAQ,CAACsJ,cAAb,EAA6B;AAC3BtJ,UAAAA,QAAQ,CAACsJ,cAAT,CAAwBiM,MAAxB;AACD;AACF;;AAEDN,MAAAA,WAAW,GAAGG,cAAd;AACAF,MAAAA,WAAW,GAAGG,cAAd;;AAEA,UAAIrV,QAAQ,CAACnD,KAAT,CAAe4M,SAAnB,EAA8B;AAC5BiF,QAAAA,qBAAqB,CAACyG,cAAD,CAArB;AACD;AACF;;AAED,WAAO;AACL3Q,MAAAA,OADK,qBACW;AACd,YAAIxE,QAAQ,CAACrC,KAAT,CAAe+E,MAAnB,EAA2B;AACzByS,UAAAA,cAAc;AACf;AACF;AALI,KAAP;AAOD;AAnDoB,CAAvB;AAsDA;AAEA,SAASG,iBAAT,CACEE,KADF,EAEEC,KAFF,EAGW;AACT,MAAID,KAAK,IAAIC,KAAb,EAAoB;AAClB,WACED,KAAK,CAACtX,GAAN,KAAcuX,KAAK,CAACvX,GAApB,IACAsX,KAAK,CAAC9W,KAAN,KAAgB+W,KAAK,CAAC/W,KADtB,IAEA8W,KAAK,CAACnX,MAAN,KAAiBoX,KAAK,CAACpX,MAFvB,IAGAmX,KAAK,CAACjX,IAAN,KAAekX,KAAK,CAAClX,IAJvB;AAMD;;AAED,SAAO,IAAP;AACD;;ACtED+Q,KAAK,CAAChK,eAAN,CAAsB;AAACN,EAAAA,MAAM,EAANA;AAAD,CAAtB;;;;;;;;;;;;"}