<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Database language settings
return [
    'invalidEvent'                     => '"{0}" is not a valid Model Event callback.',
    'invalidArgument'                  => 'You must provide a valid "{0}".',
    'invalidAllowedFields'             => 'Allowed fields must be specified for model: "{0}"',
    'emptyDataset'                     => 'There is no data to {0}.',
    'emptyPrimaryKey'                  => 'There is no primary key defined when trying to make {0}.',
    'failGetFieldData'                 => 'Failed to get field data from database.',
    'failGetIndexData'                 => 'Failed to get index data from database.',
    'failGetForeignKeyData'            => 'Failed to get foreign key data from database.',
    'parseStringFail'                  => 'Parsing key string failed.',
    'featureUnavailable'               => 'This feature is not available for the database you are using.',
    'tableNotFound'                    => 'Table "{0}" was not found in the current database.',
    'noPrimaryKey'                     => '"{0}" model class does not specify a Primary Key.',
    'noDateFormat'                     => '"{0}" model class does not have a valid dateFormat.',
    'fieldNotExists'                   => 'Field "{0}" not found.',
    'forEmptyInputGiven'               => 'Empty statement is given for the field "{0}"',
    'forFindColumnHaveMultipleColumns' => 'Only single column allowed in Column name.',
    'methodNotAvailable'               => 'You cannot use "{1}" in "{0}". This is a method of the Query Builder Class.',
];
