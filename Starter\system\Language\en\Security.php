<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Security language settings
return [
    'disallowedAction' => 'The action you requested is not allowed.',

    // @deprecated
    'invalidSameSite' => 'The SameSite value must be None, Lax, Strict, or a blank string. Given: "{0}"',
];
