!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@popperjs/core")):"function"==typeof define&&define.amd?define(["@popperjs/core"],e):(t=t||self).tippy=e(t.Popper)}(this,(function(t){"use strict";var e="undefined"!=typeof window&&"undefined"!=typeof document,n=e?navigator.userAgent:"",r=/MSIE |Trident\//.test(n),i={passive:!0,capture:!0};function o(t,e,n){if(Array.isArray(t)){var r=t[e];return null==r?Array.isArray(n)?n[e]:n:r}return t}function a(t,e){var n={}.toString.call(t);return 0===n.indexOf("[object")&&n.indexOf(e+"]")>-1}function s(t,e){return"function"==typeof t?t.apply(void 0,e):t}function p(t,e){return 0===e?t:function(r){clearTimeout(n),n=setTimeout((function(){t(r)}),e)};var n}function u(t,e){var n=Object.assign({},t);return e.forEach((function(t){delete n[t]})),n}function c(t){return[].concat(t)}function f(t,e){-1===t.indexOf(e)&&t.push(e)}function l(t){return t.split("-")[0]}function d(t){return[].slice.call(t)}function v(){return document.createElement("div")}function m(t){return["Element","Fragment"].some((function(e){return a(t,e)}))}function g(t){return a(t,"MouseEvent")}function h(t){return!(!t||!t._tippy||t._tippy.reference!==t)}function b(t){return m(t)?[t]:function(t){return a(t,"NodeList")}(t)?d(t):Array.isArray(t)?t:d(document.querySelectorAll(t))}function y(t,e){t.forEach((function(t){t&&(t.style.transitionDuration=e+"ms")}))}function x(t,e){t.forEach((function(t){t&&t.setAttribute("data-state",e)}))}function w(t){var e=c(t)[0];return e&&e.ownerDocument||document}function E(t,e,n){var r=e+"EventListener";["transitionend","webkitTransitionEnd"].forEach((function(e){t[r](e,n)}))}var T={isTouch:!1},C=0;function A(){T.isTouch||(T.isTouch=!0,window.performance&&document.addEventListener("mousemove",O))}function O(){var t=performance.now();t-C<20&&(T.isTouch=!1,document.removeEventListener("mousemove",O)),C=t}function L(){var t=document.activeElement;if(h(t)){var e=t._tippy;t.blur&&!e.state.isVisible&&t.blur()}}var D=Object.assign({appendTo:function(){return document.body},aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),k=Object.keys(D);function M(t){var e=(t.plugins||[]).reduce((function(e,n){var r=n.name,i=n.defaultValue;return r&&(e[r]=void 0!==t[r]?t[r]:i),e}),{});return Object.assign({},t,{},e)}function V(t,e){var n=Object.assign({},e,{content:s(e.content,[t])},e.ignoreAttributes?{}:function(t,e){return(e?Object.keys(M(Object.assign({},D,{plugins:e}))):k).reduce((function(e,n){var r=(t.getAttribute("data-tippy-"+n)||"").trim();if(!r)return e;if("content"===n)e[n]=r;else try{e[n]=JSON.parse(r)}catch(t){e[n]=r}return e}),{})}(t,e.plugins));return n.aria=Object.assign({},D.aria,{},n.aria),n.aria={expanded:"auto"===n.aria.expanded?e.interactive:n.aria.expanded,content:"auto"===n.aria.content?e.interactive?null:"describedby":n.aria.content},n}function R(t,e){t.innerHTML=e}function j(t){var e=v();return!0===t?e.className="tippy-arrow":(e.className="tippy-svg-arrow",m(t)?e.appendChild(t):R(e,t)),e}function P(t,e){m(e.content)?(R(t,""),t.appendChild(e.content)):"function"!=typeof e.content&&(e.allowHTML?R(t,e.content):t.textContent=e.content)}function I(t){var e=t.firstElementChild,n=d(e.children);return{box:e,content:n.find((function(t){return t.classList.contains("tippy-content")})),arrow:n.find((function(t){return t.classList.contains("tippy-arrow")||t.classList.contains("tippy-svg-arrow")})),backdrop:n.find((function(t){return t.classList.contains("tippy-backdrop")}))}}function S(t){var e=v(),n=v();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var r=v();function i(n,r){var i=I(e),o=i.box,a=i.content,s=i.arrow;r.theme?o.setAttribute("data-theme",r.theme):o.removeAttribute("data-theme"),"string"==typeof r.animation?o.setAttribute("data-animation",r.animation):o.removeAttribute("data-animation"),r.inertia?o.setAttribute("data-inertia",""):o.removeAttribute("data-inertia"),o.style.maxWidth="number"==typeof r.maxWidth?r.maxWidth+"px":r.maxWidth,r.role?o.setAttribute("role",r.role):o.removeAttribute("role"),n.content===r.content&&n.allowHTML===r.allowHTML||P(a,t.props),r.arrow?s?n.arrow!==r.arrow&&(o.removeChild(s),o.appendChild(j(r.arrow))):o.appendChild(j(r.arrow)):s&&o.removeChild(s)}return r.className="tippy-content",r.setAttribute("data-state","hidden"),P(r,t.props),e.appendChild(n),n.appendChild(r),i(t.props,t.props),{popper:e,onUpdate:i}}S.$$tippy=!0;var B=1,H=[],U=[];function N(e,n){var a,u,m,h,b,C,A,O,L,k=V(e,Object.assign({},D,{},M((a=n,Object.keys(a).reduce((function(t,e){return void 0!==a[e]&&(t[e]=a[e]),t}),{}))))),R=!1,j=!1,P=!1,S=!1,N=[],_=p(bt,k.interactiveDebounce),z=w(k.triggerTarget||e),F=B++,W=(L=k.plugins).filter((function(t,e){return L.indexOf(t)===e})),q={id:F,reference:e,popper:v(),popperInstance:null,props:k,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:W,clearDelayTimeouts:function(){clearTimeout(u),clearTimeout(m),cancelAnimationFrame(h)},setProps:function(t){if(q.state.isDestroyed)return;it("onBeforeUpdate",[q,t]),gt();var n=q.props,r=V(e,Object.assign({},q.props,{},t,{ignoreAttributes:!0}));q.props=r,mt(),n.interactiveDebounce!==r.interactiveDebounce&&(st(),_=p(bt,r.interactiveDebounce));n.triggerTarget&&!r.triggerTarget?c(n.triggerTarget).forEach((function(t){t.removeAttribute("aria-expanded")})):r.triggerTarget&&e.removeAttribute("aria-expanded");at(),rt(),Y&&Y(n,r);q.popperInstance&&(Et(),Ct().forEach((function(t){requestAnimationFrame(t._tippy.popperInstance.forceUpdate)})));it("onAfterUpdate",[q,t])},setContent:function(t){q.setProps({content:t})},show:function(){var t=q.state.isVisible,e=q.state.isDestroyed,n=!q.state.isEnabled,r=T.isTouch&&!q.props.touch,i=o(q.props.duration,0,D.duration);if(t||e||n||r)return;if(tt().hasAttribute("disabled"))return;if(it("onShow",[q],!1),!1===q.props.onShow(q))return;q.state.isVisible=!0,Z()&&(X.style.visibility="visible");rt(),ft(),q.state.isMounted||(X.style.transition="none");if(Z()){var a=et(),p=a.box,u=a.content;y([p,u],0)}A=function(){if(q.state.isVisible&&!S){if(S=!0,X.offsetHeight,X.style.transition=q.props.moveTransition,Z()&&q.props.animation){var t=et(),e=t.box,n=t.content;y([e,n],i),x([e,n],"visible")}ot(),at(),f(U,q),q.state.isMounted=!0,it("onMount",[q]),q.props.animation&&Z()&&function(t,e){dt(t,e)}(i,(function(){q.state.isShown=!0,it("onShown",[q])}))}},function(){var t,e=q.props.appendTo,n=tt();t=q.props.interactive&&e===D.appendTo||"parent"===e?n.parentNode:s(e,[n]);t.contains(X)||t.appendChild(X);Et()}()},hide:function(){var t=!q.state.isVisible,e=q.state.isDestroyed,n=!q.state.isEnabled,r=o(q.props.duration,1,D.duration);if(t||e||n)return;if(it("onHide",[q],!1),!1===q.props.onHide(q))return;q.state.isVisible=!1,q.state.isShown=!1,S=!1,Z()&&(X.style.visibility="hidden");if(st(),lt(),rt(),Z()){var i=et(),a=i.box,s=i.content;q.props.animation&&(y([a,s],r),x([a,s],"hidden"))}ot(),at(),q.props.animation?Z()&&function(t,e){dt(t,(function(){!q.state.isVisible&&X.parentNode&&X.parentNode.contains(X)&&e()}))}(r,q.unmount):q.unmount()},hideWithInteractivity:function(t){z.addEventListener("mousemove",_),f(H,_),_(t)},enable:function(){q.state.isEnabled=!0},disable:function(){q.hide(),q.state.isEnabled=!1},unmount:function(){q.state.isVisible&&q.hide();if(!q.state.isMounted)return;Tt(),Ct().forEach((function(t){t._tippy.unmount()})),X.parentNode&&X.parentNode.removeChild(X);U=U.filter((function(t){return t!==q})),q.state.isMounted=!1,it("onHidden",[q])},destroy:function(){if(q.state.isDestroyed)return;q.clearDelayTimeouts(),q.unmount(),gt(),delete e._tippy,q.state.isDestroyed=!0,it("onDestroy",[q])}};if(!k.render)return q;var $=k.render(q),X=$.popper,Y=$.onUpdate;X.setAttribute("data-tippy-root",""),X.id="tippy-"+q.id,q.popper=X,e._tippy=q,X._tippy=q;var J=W.map((function(t){return t.fn(q)})),G=e.hasAttribute("aria-expanded");return mt(),at(),rt(),it("onCreate",[q]),k.showOnCreate&&At(),X.addEventListener("mouseenter",(function(){q.props.interactive&&q.state.isVisible&&q.clearDelayTimeouts()})),X.addEventListener("mouseleave",(function(t){q.props.interactive&&q.props.trigger.indexOf("mouseenter")>=0&&(z.addEventListener("mousemove",_),_(t))})),q;function K(){var t=q.props.touch;return Array.isArray(t)?t:[t,0]}function Q(){return"hold"===K()[0]}function Z(){var t;return!!(null==(t=q.props.render)?void 0:t.$$tippy)}function tt(){return O||e}function et(){return I(X)}function nt(t){return q.state.isMounted&&!q.state.isVisible||T.isTouch||b&&"focus"===b.type?0:o(q.props.delay,t?0:1,D.delay)}function rt(){X.style.pointerEvents=q.props.interactive&&q.state.isVisible?"":"none",X.style.zIndex=""+q.props.zIndex}function it(t,e,n){var r;(void 0===n&&(n=!0),J.forEach((function(n){n[t]&&n[t].apply(void 0,e)})),n)&&(r=q.props)[t].apply(r,e)}function ot(){var t=q.props.aria;if(t.content){var n="aria-"+t.content,r=X.id;c(q.props.triggerTarget||e).forEach((function(t){var e=t.getAttribute(n);if(q.state.isVisible)t.setAttribute(n,e?e+" "+r:r);else{var i=e&&e.replace(r,"").trim();i?t.setAttribute(n,i):t.removeAttribute(n)}}))}}function at(){!G&&q.props.aria.expanded&&c(q.props.triggerTarget||e).forEach((function(t){q.props.interactive?t.setAttribute("aria-expanded",q.state.isVisible&&t===tt()?"true":"false"):t.removeAttribute("aria-expanded")}))}function st(){z.removeEventListener("mousemove",_),H=H.filter((function(t){return t!==_}))}function pt(t){if(!(T.isTouch&&(P||"mousedown"===t.type)||q.props.interactive&&X.contains(t.target))){if(tt().contains(t.target)){if(T.isTouch)return;if(q.state.isVisible&&q.props.trigger.indexOf("click")>=0)return}else it("onClickOutside",[q,t]);!0===q.props.hideOnClick&&(R=!1,q.clearDelayTimeouts(),q.hide(),j=!0,setTimeout((function(){j=!1})),q.state.isMounted||lt())}}function ut(){P=!0}function ct(){P=!1}function ft(){z.addEventListener("mousedown",pt,!0),z.addEventListener("touchend",pt,i),z.addEventListener("touchstart",ct,i),z.addEventListener("touchmove",ut,i)}function lt(){z.removeEventListener("mousedown",pt,!0),z.removeEventListener("touchend",pt,i),z.removeEventListener("touchstart",ct,i),z.removeEventListener("touchmove",ut,i)}function dt(t,e){var n=et().box;function r(t){t.target===n&&(E(n,"remove",r),e())}if(0===t)return e();E(n,"remove",C),E(n,"add",r),C=r}function vt(t,n,r){void 0===r&&(r=!1),c(q.props.triggerTarget||e).forEach((function(e){e.addEventListener(t,n,r),N.push({node:e,eventType:t,handler:n,options:r})}))}function mt(){var t;Q()&&(vt("touchstart",ht,{passive:!0}),vt("touchend",yt,{passive:!0})),(t=q.props.trigger,t.split(/\s+/).filter(Boolean)).forEach((function(t){if("manual"!==t)switch(vt(t,ht),t){case"mouseenter":vt("mouseleave",yt);break;case"focus":vt(r?"focusout":"blur",xt);break;case"focusin":vt("focusout",xt)}}))}function gt(){N.forEach((function(t){var e=t.node,n=t.eventType,r=t.handler,i=t.options;e.removeEventListener(n,r,i)})),N=[]}function ht(t){var e,n=!1;if(q.state.isEnabled&&!wt(t)&&!j){var r="focus"===(null==(e=b)?void 0:e.type);b=t,O=t.currentTarget,at(),!q.state.isVisible&&g(t)&&H.forEach((function(e){return e(t)})),"click"===t.type&&(q.props.trigger.indexOf("mouseenter")<0||R)&&!1!==q.props.hideOnClick&&q.state.isVisible?n=!0:At(t),"click"===t.type&&(R=!n),n&&!r&&Ot(t)}}function bt(t){var n=t.target,r=e.contains(n)||X.contains(n);"mousemove"===t.type&&r||function(t,e){var n=e.clientX,r=e.clientY;return t.every((function(t){var e=t.popperRect,i=t.popperState,o=t.props.interactiveBorder,a=l(i.placement),s=i.modifiersData.offset;if(!s)return!0;var p="bottom"===a?s.top.y:0,u="top"===a?s.bottom.y:0,c="right"===a?s.left.x:0,f="left"===a?s.right.x:0,d=e.top-r+p>o,v=r-e.bottom-u>o,m=e.left-n+c>o,g=n-e.right-f>o;return d||v||m||g}))}(Ct().concat(X).map((function(t){var e,n=null==(e=t._tippy.popperInstance)?void 0:e.state;return n?{popperRect:t.getBoundingClientRect(),popperState:n,props:k}:null})).filter(Boolean),t)&&(st(),Ot(t))}function yt(t){wt(t)||q.props.trigger.indexOf("click")>=0&&R||(q.props.interactive?q.hideWithInteractivity(t):Ot(t))}function xt(t){q.props.trigger.indexOf("focusin")<0&&t.target!==tt()||q.props.interactive&&t.relatedTarget&&X.contains(t.relatedTarget)||Ot(t)}function wt(t){return!!T.isTouch&&Q()!==t.type.indexOf("touch")>=0}function Et(){Tt();var n=q.props,r=n.popperOptions,i=n.placement,o=n.offset,a=n.getReferenceClientRect,s=n.moveTransition,p=Z()?I(X).arrow:null,u=a?{getBoundingClientRect:a,contextElement:a.contextElement||tt()}:e,c=[{name:"offset",options:{offset:o}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!s}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(t){var e=t.state;if(Z()){var n=et().box;["placement","reference-hidden","escaped"].forEach((function(t){"placement"===t?n.setAttribute("data-placement",e.placement):e.attributes.popper["data-popper-"+t]?n.setAttribute("data-"+t,""):n.removeAttribute("data-"+t)})),e.attributes.popper={}}}}];Z()&&p&&c.push({name:"arrow",options:{element:p,padding:3}}),c.push.apply(c,(null==r?void 0:r.modifiers)||[]),q.popperInstance=t.createPopper(u,X,Object.assign({},r,{placement:i,onFirstUpdate:A,modifiers:c}))}function Tt(){q.popperInstance&&(q.popperInstance.destroy(),q.popperInstance=null)}function Ct(){return d(X.querySelectorAll("[data-tippy-root]"))}function At(t){q.clearDelayTimeouts(),t&&it("onTrigger",[q,t]),ft();var e=nt(!0),n=K(),r=n[0],i=n[1];T.isTouch&&"hold"===r&&i&&(e=i),e?u=setTimeout((function(){q.show()}),e):q.show()}function Ot(t){if(q.clearDelayTimeouts(),it("onUntrigger",[q,t]),q.state.isVisible){if(!(q.props.trigger.indexOf("mouseenter")>=0&&q.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(t.type)>=0&&R)){var e=nt(!1);e?m=setTimeout((function(){q.state.isVisible&&q.hide()}),e):h=requestAnimationFrame((function(){q.hide()}))}}else lt()}}function _(t,e){void 0===e&&(e={});var n=D.plugins.concat(e.plugins||[]);document.addEventListener("touchstart",A,i),window.addEventListener("blur",L);var r=Object.assign({},e,{plugins:n}),o=b(t).reduce((function(t,e){var n=e&&N(e,r);return n&&t.push(n),t}),[]);return m(t)?o[0]:o}_.defaultProps=D,_.setDefaultProps=function(t){Object.keys(t).forEach((function(e){D[e]=t[e]}))},_.currentInput=T;var z={mouseover:"mouseenter",focusin:"focus",click:"click"};var F={name:"animateFill",defaultValue:!1,fn:function(t){var e;if(!(null==(e=t.props.render)?void 0:e.$$tippy))return{};var n=I(t.popper),r=n.box,i=n.content,o=t.props.animateFill?function(){var t=v();return t.className="tippy-backdrop",x([t],"hidden"),t}():null;return{onCreate:function(){o&&(r.insertBefore(o,r.firstElementChild),r.setAttribute("data-animatefill",""),r.style.overflow="hidden",t.setProps({arrow:!1,animation:"shift-away"}))},onMount:function(){if(o){var t=r.style.transitionDuration,e=Number(t.replace("ms",""));i.style.transitionDelay=Math.round(e/10)+"ms",o.style.transitionDuration=t,x([o],"visible")}},onShow:function(){o&&(o.style.transitionDuration="0ms")},onHide:function(){o&&x([o],"hidden")}}}};var W={clientX:0,clientY:0},q=[];function $(t){var e=t.clientX,n=t.clientY;W={clientX:e,clientY:n}}var X={name:"followCursor",defaultValue:!1,fn:function(t){var e=t.reference,n=w(t.props.triggerTarget||e),r=!1,i=!1,o=!0,a=t.props;function s(){return"initial"===t.props.followCursor&&t.state.isVisible}function p(){n.addEventListener("mousemove",f)}function u(){n.removeEventListener("mousemove",f)}function c(){r=!0,t.setProps({getReferenceClientRect:null}),r=!1}function f(n){var r=!n.target||e.contains(n.target),i=t.props.followCursor,o=n.clientX,a=n.clientY,s=e.getBoundingClientRect(),p=o-s.left,u=a-s.top;!r&&t.props.interactive||t.setProps({getReferenceClientRect:function(){var t=e.getBoundingClientRect(),n=o,r=a;"initial"===i&&(n=t.left+p,r=t.top+u);var s="horizontal"===i?t.top:r,c="vertical"===i?t.right:n,f="horizontal"===i?t.bottom:r,l="vertical"===i?t.left:n;return{width:c-l,height:f-s,top:s,right:c,bottom:f,left:l}}})}function l(){t.props.followCursor&&(q.push({instance:t,doc:n}),function(t){t.addEventListener("mousemove",$)}(n))}function d(){0===(q=q.filter((function(e){return e.instance!==t}))).filter((function(t){return t.doc===n})).length&&function(t){t.removeEventListener("mousemove",$)}(n)}return{onCreate:l,onDestroy:d,onBeforeUpdate:function(){a=t.props},onAfterUpdate:function(e,n){var o=n.followCursor;r||void 0!==o&&a.followCursor!==o&&(d(),o?(l(),!t.state.isMounted||i||s()||p()):(u(),c()))},onMount:function(){t.props.followCursor&&(o&&(f(W),o=!1),i||s()||p())},onTrigger:function(t,e){var n=e.type;i="focus"===n},onHidden:function(){t.props.followCursor&&(c(),u(),o=!0)}}}};var Y={name:"inlinePositioning",defaultValue:!1,fn:function(t){var e,n=t.reference;var r=-1,i=!1,o={name:"tippyInlinePositioning",enabled:!0,phase:"afterWrite",fn:function(i){var o=i.state;t.props.inlinePositioning&&(e!==o.placement&&t.setProps({getReferenceClientRect:function(){return function(t){return function(t,e,n,r){if(n.length<2||null===t)return e;if(2===n.length&&r>=0&&n[0].left>n[1].right)return n[r]||e;switch(t){case"top":case"bottom":var i=n[0],o=n[n.length-1],a="top"===t,s=i.top,p=o.bottom,u=a?i.left:o.left,c=a?i.right:o.right;return{top:s,bottom:p,left:u,right:c,width:c-u,height:p-s};case"left":case"right":var f=Math.min.apply(Math,n.map((function(t){return t.left}))),l=Math.max.apply(Math,n.map((function(t){return t.right}))),d=n.filter((function(e){return"left"===t?e.left===f:e.right===l})),v=d[0].top,m=d[d.length-1].bottom;return{top:v,bottom:m,left:f,right:l,width:l-f,height:m-v};default:return e}}(l(t),n.getBoundingClientRect(),d(n.getClientRects()),r)}(o.placement)}}),e=o.placement)}};function a(){var e;i||(e=function(t,e){var n;return{popperOptions:Object.assign({},t.popperOptions,{modifiers:[].concat(((null==(n=t.popperOptions)?void 0:n.modifiers)||[]).filter((function(t){return t.name!==e.name})),[e])})}}(t.props,o),i=!0,t.setProps(e),i=!1)}return{onCreate:a,onAfterUpdate:a,onTrigger:function(e,n){if(g(n)){var i=d(t.reference.getClientRects()),o=i.find((function(t){return t.left-2<=n.clientX&&t.right+2>=n.clientX&&t.top-2<=n.clientY&&t.bottom+2>=n.clientY}));r=i.indexOf(o)}},onUntrigger:function(){r=-1}}}};var J={name:"sticky",defaultValue:!1,fn:function(t){var e=t.reference,n=t.popper;function r(e){return!0===t.props.sticky||t.props.sticky===e}var i=null,o=null;function a(){var s=r("reference")?(t.popperInstance?t.popperInstance.state.elements.reference:e).getBoundingClientRect():null,p=r("popper")?n.getBoundingClientRect():null;(s&&G(i,s)||p&&G(o,p))&&t.popperInstance&&t.popperInstance.update(),i=s,o=p,t.state.isMounted&&requestAnimationFrame(a)}return{onMount:function(){t.props.sticky&&a()}}}};function G(t,e){return!t||!e||(t.top!==e.top||t.right!==e.right||t.bottom!==e.bottom||t.left!==e.left)}return e&&function(t){var e=document.createElement("style");e.textContent=t,e.setAttribute("data-tippy-stylesheet","");var n=document.head,r=document.querySelector("head>style,head>link");r?n.insertBefore(e,r):n.appendChild(e)}('.tippy-box[data-animation=fade][data-state=hidden]{opacity:0}[data-tippy-root]{max-width:calc(100vw - 10px)}.tippy-box{position:relative;background-color:#333;color:#fff;border-radius:4px;font-size:14px;line-height:1.4;outline:0;transition-property:transform,visibility,opacity}.tippy-box[data-placement^=top]>.tippy-arrow{bottom:0}.tippy-box[data-placement^=top]>.tippy-arrow:before{bottom:-7px;left:0;border-width:8px 8px 0;border-top-color:initial;transform-origin:center top}.tippy-box[data-placement^=bottom]>.tippy-arrow{top:0}.tippy-box[data-placement^=bottom]>.tippy-arrow:before{top:-7px;left:0;border-width:0 8px 8px;border-bottom-color:initial;transform-origin:center bottom}.tippy-box[data-placement^=left]>.tippy-arrow{right:0}.tippy-box[data-placement^=left]>.tippy-arrow:before{border-width:8px 0 8px 8px;border-left-color:initial;right:-7px;transform-origin:center left}.tippy-box[data-placement^=right]>.tippy-arrow{left:0}.tippy-box[data-placement^=right]>.tippy-arrow:before{left:-7px;border-width:8px 8px 8px 0;border-right-color:initial;transform-origin:center right}.tippy-box[data-inertia][data-state=visible]{transition-timing-function:cubic-bezier(.54,1.5,.38,1.11)}.tippy-arrow{width:16px;height:16px;color:#333}.tippy-arrow:before{content:"";position:absolute;border-color:transparent;border-style:solid}.tippy-content{position:relative;padding:5px 9px;z-index:1}'),_.setDefaultProps({plugins:[F,X,Y,J],render:S}),_.createSingleton=function(t,e){void 0===e&&(e={});var n,r=t,i=[],o=e.overrides;function a(){i=r.map((function(t){return t.reference}))}function s(t){r.forEach((function(e){t?e.enable():e.disable()}))}s(!1),a();var p={fn:function(){return{onDestroy:function(){s(!0)},onTrigger:function(t,e){var a=e.currentTarget,s=i.indexOf(a);if(a!==n){n=a;var p=(o||[]).concat("content").reduce((function(t,e){return t[e]=r[s].props[e],t}),{});t.setProps(Object.assign({},p,{getReferenceClientRect:function(){return a.getBoundingClientRect()}}))}}}}},c=_(v(),Object.assign({},u(e,["overrides"]),{plugins:[p].concat(e.plugins||[]),triggerTarget:i})),f=c.setProps;return c.setProps=function(t){o=t.overrides||o,f(t)},c.setInstances=function(t){s(!0),r=t,s(!1),a(),c.setProps({triggerTarget:i})},c},_.delegate=function(t,e){var n=[],r=[],i=e.target,o=u(e,["target"]),a=Object.assign({},o,{trigger:"manual",touch:!1}),s=Object.assign({},o,{showOnCreate:!0}),p=_(t,a);function f(t){if(t.target){var n=t.target.closest(i);if(n){var o=n.getAttribute("data-tippy-trigger")||e.trigger||D.trigger;if(!n._tippy&&!("touchstart"===t.type&&"boolean"==typeof s.touch||"touchstart"!==t.type&&o.indexOf(z[t.type]))){var a=_(n,s);a&&(r=r.concat(a))}}}}function l(t,e,r,i){void 0===i&&(i=!1),t.addEventListener(e,r,i),n.push({node:t,eventType:e,handler:r,options:i})}return c(p).forEach((function(t){var e=t.destroy;t.destroy=function(t){void 0===t&&(t=!0),t&&r.forEach((function(t){t.destroy()})),r=[],n.forEach((function(t){var e=t.node,n=t.eventType,r=t.handler,i=t.options;e.removeEventListener(n,r,i)})),n=[],e()},function(t){var e=t.reference;l(e,"touchstart",f),l(e,"mouseover",f),l(e,"focusin",f),l(e,"click",f)}(t)})),p},_.hideAll=function(t){var e=void 0===t?{}:t,n=e.exclude,r=e.duration;U.forEach((function(t){var e=!1;if(n&&(e=h(n)?t.reference===n:t.popper===n.popper),!e){var i=t.props.duration;t.setProps({duration:r}),t.hide(),t.state.isDestroyed||t.setProps({duration:i})}}))},_.roundArrow='<svg width="16" height="6" xmlns="http://www.w3.org/2000/svg"><path d="M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z"></svg>',_}));
//# sourceMappingURL=tippy-bundle.umd.min.js.map
