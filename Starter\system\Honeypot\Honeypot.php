<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

namespace CodeIgniter\Honeypot;

use CodeIgniter\Honeypot\Exceptions\HoneypotException;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Config\Honeypot as HoneypotConfig;

/**
 * class Honeypot
 */
class Honeypot
{
    /**
     * Our configuration.
     *
     * @var HoneypotConfig
     */
    protected $config;

    /**
     * Constructor.
     *
     * @throws HoneypotException
     */
    public function __construct(HoneypotConfig $config)
    {
        $this->config = $config;

        if (! $this->config->hidden) {
            throw HoneypotException::forNoHiddenValue();
        }

        if (empty($this->config->container) || strpos($this->config->container, '{template}') === false) {
            $this->config->container = '<div style="display:none">{template}</div>';
        }

        $this->config->containerId ??= 'hpc';

        if ($this->config->template === '') {
            throw HoneypotException::forNoTemplate();
        }

        if ($this->config->name === '') {
            throw HoneypotException::forNoNameField();
        }
    }

    /**
     * Checks the request if honeypot field has data.
     */
    public function hasContent(RequestInterface $request)
    {
        assert($request instanceof IncomingRequest);

        return ! empty($request->getPost($this->config->name));
    }

    /**
     * Attaches Honeypot template to response.
     */
    public function attachHoneypot(ResponseInterface $response)
    {
        if ($response->getCSP()->enabled()) {
            // Add id attribute to the container tag.
            $this->config->container = str_ireplace(
                '>{template}',
                ' id="' . $this->config->containerId . '">{template}',
                $this->config->container
            );
        }

        $prepField = $this->prepareTemplate($this->config->template);

        $body = $response->getBody();
        $body = str_ireplace('</form>', $prepField . '</form>', $body);

        if ($response->getCSP()->enabled()) {
            // Add style tag for the container tag in the head tag.
            $style = '<style ' . csp_style_nonce() . '>#' . $this->config->containerId . ' { display:none }</style>';
            $body  = str_ireplace('</head>', $style . '</head>', $body);
        }

        $response->setBody($body);
    }

    /**
     * Prepares the template by adding label
     * content and field name.
     */
    protected function prepareTemplate(string $template): string
    {
        $template = str_ireplace('{label}', $this->config->label, $template);
        $template = str_ireplace('{name}', $this->config->name, $template);

        if ($this->config->hidden) {
            $template = str_ireplace('{template}', $template, $this->config->container);
        }

        return $template;
    }
}
